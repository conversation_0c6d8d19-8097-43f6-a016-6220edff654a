#Wed Jul 30 04:54:08 BDT 2025
org.levimc.launcher.app-main-5\:/anim/slide_in_top.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_top.xml
org.levimc.launcher.app-main-5\:/drawable/background_gradient.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\background_gradient.xml
org.levimc.launcher.app-main-5\:/drawable/bg_abi_arm64_v8a.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_abi_arm64_v8a.xml
org.levimc.launcher.app-main-5\:/drawable/bg_abi_armeabi_v7a.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_abi_armeabi_v7a.xml
org.levimc.launcher.app-main-5\:/drawable/bg_abi_default.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_abi_default.xml
org.levimc.launcher.app-main-5\:/drawable/bg_abi_x86.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_abi_x86.xml
org.levimc.launcher.app-main-5\:/drawable/bg_abi_x86_64.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_abi_x86_64.xml
org.levimc.launcher.app-main-5\:/drawable/bg_item_rounded.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_item_rounded.xml
org.levimc.launcher.app-main-5\:/drawable/bg_round_gradient.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_round_gradient.xml
org.levimc.launcher.app-main-5\:/drawable/bg_rounded_card.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_rounded_card.xml
org.levimc.launcher.app-main-5\:/drawable/bg_third_level_item.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_third_level_item.xml
org.levimc.launcher.app-main-5\:/drawable/card_background.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_background.xml
org.levimc.launcher.app-main-5\:/drawable/ic_add.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
org.levimc.launcher.app-main-5\:/drawable/ic_arrow_down.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_down.xml
org.levimc.launcher.app-main-5\:/drawable/ic_check.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check.xml
org.levimc.launcher.app-main-5\:/drawable/ic_delete.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete.xml
org.levimc.launcher.app-main-5\:/drawable/ic_drag_handle.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_drag_handle.xml
org.levimc.launcher.app-main-5\:/drawable/ic_game_icon.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_game_icon.xml
org.levimc.launcher.app-main-5\:/drawable/ic_github.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_github.xml
org.levimc.launcher.app-main-5\:/drawable/ic_internet.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_internet.xml
org.levimc.launcher.app-main-5\:/drawable/ic_launch.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launch.xml
org.levimc.launcher.app-main-5\:/drawable/ic_leaf_logo.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_leaf_logo.png
org.levimc.launcher.app-main-5\:/drawable/ic_minecraft_cube.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_minecraft_cube.xml
org.levimc.launcher.app-main-5\:/drawable/ic_settings.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
org.levimc.launcher.app-main-5\:/font/misans.ttf=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\misans.ttf
org.levimc.launcher.app-main-5\:/menu/language_menu.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\language_menu.xml
org.levimc.launcher.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
org.levimc.launcher.app-main-5\:/mipmap-hdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.png
org.levimc.launcher.app-main-5\:/mipmap-hdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_adaptive_back.png
org.levimc.launcher.app-main-5\:/mipmap-hdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_adaptive_fore.png
org.levimc.launcher.app-main-5\:/mipmap-mdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.png
org.levimc.launcher.app-main-5\:/mipmap-mdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_adaptive_back.png
org.levimc.launcher.app-main-5\:/mipmap-mdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_adaptive_fore.png
org.levimc.launcher.app-main-5\:/mipmap-xhdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.png
org.levimc.launcher.app-main-5\:/mipmap-xhdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_adaptive_back.png
org.levimc.launcher.app-main-5\:/mipmap-xhdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_adaptive_fore.png
org.levimc.launcher.app-main-5\:/mipmap-xxhdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.png
org.levimc.launcher.app-main-5\:/mipmap-xxhdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_adaptive_back.png
org.levimc.launcher.app-main-5\:/mipmap-xxhdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_adaptive_fore.png
org.levimc.launcher.app-main-5\:/mipmap-xxxhdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.png
org.levimc.launcher.app-main-5\:/mipmap-xxxhdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_adaptive_back.png
org.levimc.launcher.app-main-5\:/mipmap-xxxhdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_adaptive_fore.png
org.levimc.launcher.app-main-5\:/xml/file_provider_paths.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\file_provider_paths.xml
org.levimc.launcher.app-main-5\:/xml/network_security_config.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\network_security_config.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/activity_main.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/activity_splash.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_splash.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/alert_dialog_custom.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\alert_dialog_custom.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/dialog_apk_version_confirm.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_apk_version_confirm.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/dialog_game_version_select.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_game_version_select.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/dialog_install_progress.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_install_progress.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/dialog_libs_repair.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_libs_repair.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/dialog_loading.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_loading.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/dialog_settings.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_settings.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/item_mod.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_mod.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/item_settings_button.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_settings_button.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/item_settings_edittext.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_settings_edittext.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/item_settings_spinner.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_settings_spinner.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/item_settings_switch.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_settings_switch.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/item_version.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_version.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/item_version_big_group.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_version_big_group.xml
org.levimc.launcher.app-packageDebugResources-2\:/layout/item_version_group_title.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_version_group_title.xml

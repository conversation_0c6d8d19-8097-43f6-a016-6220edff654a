{
    files = {
        [[build\.objs\leviutils\android\arm64-v8a\release\src\gui\gui.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\gui\ImGuiAnsiColor.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\gui\jni_imgui.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\gui\renderer.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\imgui\imgui.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\imgui\imgui_demo.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\imgui\imgui_draw.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\imgui\imgui_impl_android.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\imgui\imgui_impl_opengl3.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\imgui\imgui_tables.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\imgui\imgui_widgets.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\logger\jni_logger.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\logger\logger.cpp.o]],
        [[build\.objs\leviutils\android\arm64-v8a\release\src\main.cpp.o]]
    },
    values = {
        [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        {
            "-shared",
            "--target=aarch64-none-linux-android21",
            "-nostdlib++",
            "-fPIC",
            "-s",
            [[-LC:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++\libs\arm64-v8a]],
            "-landroid",
            "-lEGL",
            "-lGLESv3",
            "-llog",
            "-lc++_static",
            "-lc++abi"
        }
    }
}
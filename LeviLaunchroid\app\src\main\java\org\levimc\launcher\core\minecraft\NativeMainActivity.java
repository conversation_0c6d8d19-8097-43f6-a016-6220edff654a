package org.levimc.launcher.core.minecraft;

import android.app.NativeActivity;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

public class NativeMainActivity extends NativeActivity implements View.OnKeyListener {
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }
    
    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        return false;
    }
    
    public void startPickerActivity(Intent intent, int requestCode) {
        startActivityForResult(intent, requestCode);
    }
}

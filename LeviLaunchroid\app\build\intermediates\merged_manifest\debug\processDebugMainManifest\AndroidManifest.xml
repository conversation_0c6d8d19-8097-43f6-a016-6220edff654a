<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.levimc.launcher"
    android:versionCode="18161707"
    android:versionName="unknown" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <uses-feature
        android:glEsVersion="0x20000"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <queries>
        <package android:name="com.mojang.minecraftpe" />
    </queries>

    <permission
        android:name="org.levimc.launcher.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="org.levimc.launcher.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Base.Theme.FullScreen" >
        <activity
            android:name="org.levimc.launcher.ui.activities.SplashActivity"
            android:exported="true"
            android:screenOrientation="sensorLandscape" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="org.levimc.launcher.ui.activities.MainActivity"
            android:exported="true"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name="org.levimc.launcher.ui.activities.IntentHandler"
            android:alwaysRetainTaskState="true"
            android:configChanges="fontScale|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation|navigation|keyboardHidden|keyboard|touchscreen|locale|mnc|mcc"
            android:excludeFromRecents="false"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="sensorLandscape" >
            <meta-data
                android:name="android.app.lib_name"
                android:value="preloader" />

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="*/*" />
                <data android:host="*" />
                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:pathPattern=".*\\.so" />
                <data android:pathPattern=".*\\..*\\.so" />
                <data android:pathPattern=".*\\..*\\..*\\.so" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.so" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.so" />
                <data android:pathPattern=".*\\.mcworld" />
                <data android:pathPattern=".*\\..*\\.mcworld" />
                <data android:pathPattern=".*\\..*\\..*\\.mcworld" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcworld" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcworld" />
                <data android:pathPattern=".*\\.mcpack" />
                <data android:pathPattern=".*\\..*\\.mcpack" />
                <data android:pathPattern=".*\\..*\\..*\\.mcpack" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcpack" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcpack" />
                <data android:pathPattern=".*\\.mcaddon" />
                <data android:pathPattern=".*\\..*\\.mcaddon" />
                <data android:pathPattern=".*\\..*\\..*\\.mcaddon" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcaddon" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcaddon" />
                <data android:pathPattern=".*\\.mctemplate" />
                <data android:pathPattern=".*\\..*\\.mctemplate" />
                <data android:pathPattern=".*\\..*\\..*\\.mctemplate" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mctemplate" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mctemplate" />
            </intent-filter>
            <intent-filter>
                <action android:name="xbox_live_game_invite" />
                <action android:name="xbox_live_achievement_unlock" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="org.levimc.launcher.core.minecraft.NativeMinecraftLauncher"
            android:alwaysRetainTaskState="true"
            android:configChanges="fontScale|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation|navigation|keyboardHidden|keyboard|touchscreen|locale|mnc|mcc"
            android:excludeFromRecents="false"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppFullScreenTheme" >
            <meta-data
                android:name="android.app.lib_name"
                android:value="preloader" />
        </activity>
        <activity
            android:name="com.microsoft.xal.browser.IntentHandler"
            android:alwaysRetainTaskState="true"
            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
            android:exported="true"
            android:launchMode="singleTask" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <data
                    android:host="auth"
                    android:scheme="ms-xal-0000000048183522" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.microsoft.xal.browser.BrowserLaunchActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name="com.microsoft.xal.browser.WebKitWebViewController"
            android:alwaysRetainTaskState="true"
            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
            android:exported="false"
            android:launchMode="singleTask" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="org.levimc.launcher.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="org.levimc.launcher.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
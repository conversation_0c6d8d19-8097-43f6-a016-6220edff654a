1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="org.levimc.launcher"
4    android:versionCode="18161707"
5    android:versionName="unknown" >
6
7    <uses-sdk
7-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:6:5-9:62
8        android:minSdkVersion="24"
8-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:7:9-35
9        android:targetSdkVersion="35" />
9-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:8:9-38
10
11    <uses-feature
11-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:11:5-13:35
12        android:glEsVersion="0x20000"
12-->D:\<PERSON>\LeviLaunchroid\app\src\main\AndroidManifest.xml:12:9-38
13        android:required="true" />
13-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:13:9-32
14    <uses-feature
14-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:14:5-16:36
15        android:name="android.hardware.touchscreen"
15-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:15:9-52
16        android:required="false" />
16-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:16:9-33
17
18    <uses-permission android:name="android.permission.INTERNET" />
18-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:18:5-67
18-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:18:22-64
19    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
19-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:19:5-86
19-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:19:22-83
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:20:5-79
20-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:20:22-76
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:21:5-23:40
21-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:22:9-65
22    <uses-permission android:name="android.permission.VIBRATE" />
22-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:24:5-66
22-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:24:22-63
23    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
23-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:25:5-77
23-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:25:22-74
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:26:5-68
24-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:26:22-65
25    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
25-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:27:5-76
25-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:27:22-73
26    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
26-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:28:5-80
26-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:28:22-77
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
27-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:29:5-77
27-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:29:22-74
28    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
28-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:30:5-32:40
28-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:31:9-66
29    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
29-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:33:5-78
29-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:33:22-75
30    <uses-permission android:name="android.permission.OVERLAY_WINDOW" />
30-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:34:5-73
30-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:34:22-70
31    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
31-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:35:5-83
31-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:35:22-80
32    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
32-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:36:5-38:53
32-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:37:9-61
33
34    <queries>
34-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:40:5-42:15
35        <package android:name="com.mojang.minecraftpe" />
35-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:41:9-58
35-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:41:18-55
36    </queries>
37
38    <permission
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
39        android:name="org.levimc.launcher.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="org.levimc.launcher.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
43
44    <application
44-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:44:5-179:19
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:icon="@mipmap/ic_launcher"
48-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:45:9-43
49        android:label="@string/app_name"
49-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:46:9-41
50        android:networkSecurityConfig="@xml/network_security_config"
50-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:49:9-69
51        android:requestLegacyExternalStorage="true"
51-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:47:9-52
52        android:supportsRtl="true"
52-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:50:9-35
53        android:theme="@style/Base.Theme.FullScreen" >
53-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:48:9-53
54        <activity
54-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:52:9-61:20
55            android:name="org.levimc.launcher.ui.activities.SplashActivity"
55-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:53:13-76
56            android:exported="true"
56-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:54:13-36
57            android:screenOrientation="sensorLandscape" >
57-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:55:13-56
58            <intent-filter>
58-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:57:13-60:29
59                <action android:name="android.intent.action.MAIN" />
59-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:58:17-69
59-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:58:25-66
60
61                <category android:name="android.intent.category.LAUNCHER" />
61-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:59:17-77
61-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:59:27-74
62            </intent-filter>
63        </activity>
64        <activity
64-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:63:9-67:45
65            android:name="org.levimc.launcher.ui.activities.MainActivity"
65-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:64:13-74
66            android:exported="true"
66-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:65:13-36
67            android:screenOrientation="sensorLandscape" />
67-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:66:13-56
68        <activity
68-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:69:9-126:20
69            android:name="org.levimc.launcher.ui.activities.IntentHandler"
69-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:70:13-56
70            android:alwaysRetainTaskState="true"
70-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:71:13-49
71            android:configChanges="fontScale|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation|navigation|keyboardHidden|keyboard|touchscreen|locale|mnc|mcc"
71-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:72:13-178
72            android:excludeFromRecents="false"
72-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:73:13-47
73            android:exported="true"
73-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:74:13-36
74            android:launchMode="singleTask"
74-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:75:13-44
75            android:screenOrientation="sensorLandscape" >
75-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:76:13-56
76            <meta-data
76-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:78:13-80:45
77                android:name="android.app.lib_name"
77-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:79:17-52
78                android:value="preloader" />
78-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:80:17-42
79
80            <intent-filter>
80-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:82:13-85:29
81                <action android:name="android.intent.action.VIEW" />
81-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:83:17-69
81-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:83:25-66
82
83                <category android:name="android.intent.category.DEFAULT" />
83-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:17-76
83-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:27-73
84            </intent-filter>
85            <intent-filter>
85-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:86:13-119:29
86                <action android:name="android.intent.action.VIEW" />
86-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:83:17-69
86-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:83:25-66
87
88                <category android:name="android.intent.category.DEFAULT" />
88-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:17-76
88-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:27-73
89
90                <data android:mimeType="*/*" />
90-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
90-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:23-45
91                <data android:host="*" />
91-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
91-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:91:23-39
92                <data android:scheme="file" />
92-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
92-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:92:23-44
93                <data android:scheme="content" />
93-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
93-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:92:23-44
94                <data android:pathPattern=".*\\.so" />
94-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
94-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
95                <data android:pathPattern=".*\\..*\\.so" />
95-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
95-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
96                <data android:pathPattern=".*\\..*\\..*\\.so" />
96-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
96-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
97                <data android:pathPattern=".*\\..*\\..*\\..*\\.so" />
97-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
97-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
98                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.so" />
98-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
98-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
99                <data android:pathPattern=".*\\.mcworld" />
99-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
99-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
100                <data android:pathPattern=".*\\..*\\.mcworld" />
100-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
100-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
101                <data android:pathPattern=".*\\..*\\..*\\.mcworld" />
101-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
101-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
102                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcworld" />
102-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
102-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
103                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcworld" />
103-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
103-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
104                <data android:pathPattern=".*\\.mcpack" />
104-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
104-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
105                <data android:pathPattern=".*\\..*\\.mcpack" />
105-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
105-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
106                <data android:pathPattern=".*\\..*\\..*\\.mcpack" />
106-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
106-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
107                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcpack" />
107-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
107-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
108                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcpack" />
108-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
108-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
109                <data android:pathPattern=".*\\.mcaddon" />
109-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
109-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
110                <data android:pathPattern=".*\\..*\\.mcaddon" />
110-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
110-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
111                <data android:pathPattern=".*\\..*\\..*\\.mcaddon" />
111-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
111-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
112                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcaddon" />
112-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
112-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
113                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcaddon" />
113-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
113-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
114                <data android:pathPattern=".*\\.mctemplate" />
114-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
114-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
115                <data android:pathPattern=".*\\..*\\.mctemplate" />
115-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
115-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
116                <data android:pathPattern=".*\\..*\\..*\\.mctemplate" />
116-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
116-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
117                <data android:pathPattern=".*\\..*\\..*\\..*\\.mctemplate" />
117-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
117-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
118                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mctemplate" />
118-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
118-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
119            </intent-filter>
120            <intent-filter>
120-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:120:13-125:29
121                <action android:name="xbox_live_game_invite" />
121-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:121:17-64
121-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:121:25-61
122                <action android:name="xbox_live_achievement_unlock" />
122-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:122:17-71
122-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:122:25-68
123
124                <category android:name="android.intent.category.DEFAULT" />
124-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:17-76
124-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:27-73
125            </intent-filter>
126        </activity>
127        <activity
127-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:127:9-140:20
128            android:name="org.levimc.launcher.core.minecraft.NativeMinecraftLauncher"
128-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:128:13-86
129            android:alwaysRetainTaskState="true"
129-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:129:13-49
130            android:configChanges="fontScale|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation|navigation|keyboardHidden|keyboard|touchscreen|locale|mnc|mcc"
130-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:130:13-178
131            android:excludeFromRecents="false"
131-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:131:13-47
132            android:exported="false"
132-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:132:13-37
133            android:launchMode="singleTask"
133-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:133:13-44
134            android:screenOrientation="sensorLandscape"
134-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:134:13-56
135            android:theme="@style/AppFullScreenTheme" >
135-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:135:13-54
136            <meta-data
136-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:78:13-80:45
137                android:name="android.app.lib_name"
137-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:79:17-52
138                android:value="preloader" />
138-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:80:17-42
139        </activity>
140        <activity
140-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:141:9-156:20
141            android:name="com.microsoft.xal.browser.IntentHandler"
141-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:142:13-67
142            android:alwaysRetainTaskState="true"
142-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:146:13-49
143            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
143-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:145:13-81
144            android:exported="true"
144-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:143:13-36
145            android:launchMode="singleTask" >
145-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:144:13-44
146            <intent-filter>
146-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:147:13-155:29
147                <action android:name="android.intent.action.VIEW" />
147-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:83:17-69
147-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:83:25-66
148
149                <data
149-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
150                    android:host="auth"
150-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:91:23-39
151                    android:scheme="ms-xal-0000000048183522" />
151-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:92:23-44
152
153                <category android:name="android.intent.category.DEFAULT" />
153-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:17-76
153-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:27-73
154                <category android:name="android.intent.category.BROWSABLE" />
154-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:154:17-78
154-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:154:27-75
155            </intent-filter>
156        </activity>
157        <activity
157-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:157:9-162:52
158            android:name="com.microsoft.xal.browser.BrowserLaunchActivity"
158-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:158:13-75
159            android:alwaysRetainTaskState="true"
159-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:162:13-49
160            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
160-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:161:13-81
161            android:exported="false"
161-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:159:13-37
162            android:launchMode="singleTask" />
162-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:160:13-44
163        <activity
163-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:163:9-168:52
164            android:name="com.microsoft.xal.browser.WebKitWebViewController"
164-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:164:13-77
165            android:alwaysRetainTaskState="true"
165-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:168:13-49
166            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
166-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:167:13-81
167            android:exported="false"
167-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:165:13-37
168            android:launchMode="singleTask" />
168-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:166:13-44
169
170        <provider
170-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:170:9-178:20
171            android:name="androidx.core.content.FileProvider"
171-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:171:13-62
172            android:authorities="org.levimc.launcher.fileprovider"
172-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:173:13-67
173            android:exported="false"
173-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:172:13-37
174            android:grantUriPermissions="true" >
174-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:174:13-47
175            <meta-data
175-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:175:13-177:63
176                android:name="android.support.FILE_PROVIDER_PATHS"
176-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:176:17-67
177                android:resource="@xml/file_provider_paths" />
177-->D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:177:17-60
178        </provider>
179        <provider
179-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
180            android:name="androidx.startup.InitializationProvider"
180-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
181            android:authorities="org.levimc.launcher.androidx-startup"
181-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
182            android:exported="false" >
182-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
183            <meta-data
183-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
184                android:name="androidx.emoji2.text.EmojiCompatInitializer"
184-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
185                android:value="androidx.startup" />
185-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
186            <meta-data
186-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
187                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
187-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
188                android:value="androidx.startup" />
188-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
189            <meta-data
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
190                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
191                android:value="androidx.startup" />
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
192        </provider>
193
194        <receiver
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
195            android:name="androidx.profileinstaller.ProfileInstallReceiver"
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
196            android:directBootAware="false"
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
197            android:enabled="true"
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
198            android:exported="true"
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
199            android:permission="android.permission.DUMP" >
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
201                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
204                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
205            </intent-filter>
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
207                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
208            </intent-filter>
209            <intent-filter>
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
210                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
211            </intent-filter>
212        </receiver>
213    </application>
214
215</manifest>

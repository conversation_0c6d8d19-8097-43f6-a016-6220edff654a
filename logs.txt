2025-07-30 04:57:07.656  6718-6718  VRI[MainAc...y]@6fd9999 org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-30 04:57:07.666  6718-6718  VRI[MainAc...y]@6fd9999 org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@6fd9999
2025-07-30 04:57:07.748  6718-6718  VRI[MainAc...y]@6fd9999 org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-30 04:57:07.758  6718-7022  LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-30 04:57:07.759  6718-7022  LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: classes2.dex
2025-07-30 04:57:07.760  6718-7022  LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: classes.dex
2025-07-30 04:57:07.760  6718-7022  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:57:07.760  6718-7022  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:57:07.769  6718-6718  Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-30 04:57:07.803  6718-6718  DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@3a1ddd3
2025-07-30 04:57:07.804  6718-6718  DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@763f20e
2025-07-30 04:57:07.805  6718-6718  WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{e22842f V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:302 
2025-07-30 04:57:07.808  6718-6718  ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-30 04:57:07.809  6718-6736  NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-30 04:57:07.821  6718-6718  InputTransport          org.levimc.launcher                  D  Input channel constructed: 'f070320', fd=153
2025-07-30 04:57:07.822  6718-6718  InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 04:57:07.822  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-30 04:57:07.822  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@e22842f IsHRR=false TM=true
2025-07-30 04:57:07.832  6718-6718  BufferQueueConsumer     org.levimc.launcher                  D  [](id:1a3e00000002,api:0,p:-1,c:6718) connect: controlledByApp=false
2025-07-30 04:57:07.833  6718-6718  BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@452ca3c#2](f:0,a:0,s:0) constructor()
2025-07-30 04:57:07.833  6718-6718  BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@452ca3c mNativeObject= 0xb400007add576800 sc.mNativeObject= 0xb4000079890a7f80 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-30 04:57:07.833  6718-6718  BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@452ca3c mNativeObject= 0xb400007add576800 sc.mNativeObject= 0xb4000079890a7f80 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-30 04:57:07.834  6718-6718  BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@452ca3c#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-30 04:57:07.834  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=6 res=0x3 s={true 0xb400007988fe9000} ch=true seqId=0
2025-07-30 04:57:07.835  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-30 04:57:07.835  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007988fe9000} hwInitialized=true
2025-07-30 04:57:07.836  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-30 04:57:07.836  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@452ca3c#6
2025-07-30 04:57:07.836  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@452ca3c#7
2025-07-30 04:57:07.838  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 04:57:07.840  6718-6767  VRI[MainAc...y]@452ca3c org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-30 04:57:07.840  6718-6767  VRI[MainAc...y]@452ca3c org.levimc.launcher                  I  mWNT: t=0xb400007a22004880 mBlastBufferQueue=0xb400007add576800 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-30 04:57:07.840  6718-6767  VRI[MainAc...y]@452ca3c org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 04:57:07.842  6718-6736  BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@452ca3c#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 04:57:07.843  6718-6736  BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@452ca3c#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=291175758535355(auto) mPendingTransactions.size=0 graphicBufferId=28853590294553 transform=7
2025-07-30 04:57:07.843  6718-6736  VRI[MainAc...y]@452ca3c org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-30 04:57:07.844  6718-6736  HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 6718    Tid : 6736
2025-07-30 04:57:07.845  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 04:57:07.846  6718-6736  HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-30 04:57:07.855  6718-7022  LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-30 04:57:07.869  6718-6718  ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 04:57:07.869  6718-6718  ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 04:57:07.905  6718-6718  VRI[MainAc...y]@452ca3c org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb400007988fe9000}
2025-07-30 04:57:07.905  6718-6718  InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-30 04:57:07.906  6718-6718  InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-30 04:57:07.908  6718-7022  LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-30 04:57:07.910  6718-6732  InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=164
2025-07-30 04:57:07.935  6718-6718  InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-30 04:57:07.965  6718-7022  LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-30 04:57:08.011  6718-7022  LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-30 04:57:08.011  6718-7022  LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-30 04:57:08.011  6718-7022  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:57:08.011  6718-7022  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:57:08.011  6718-7022  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:57:08.011  6718-7022  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:57:08.019  6718-7026  nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~WHkPXWfss6KNykoNatF7Hg==/org.levimc.launcher--vPQaXYmGu4oG5BGnUECgQ==/base.apk!classes7.dex): ok
2025-07-30 04:57:08.022  6718-7026  nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~WHkPXWfss6KNykoNatF7Hg==/org.levimc.launcher--vPQaXYmGu4oG5BGnUECgQ==/base.apk!classes7.dex): ok
2025-07-30 04:57:08.125  6718-7026  nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~WHkPXWfss6KNykoNatF7Hg==/org.levimc.launcher--vPQaXYmGu4oG5BGnUECgQ==/base.apk!classes7.dex): ok
2025-07-30 04:57:08.125  6718-7026  Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x7a153635f0
2025-07-30 04:57:08.126  6718-7026  Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-30 04:57:08.172  6718-6718  ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 04:57:08.172  6718-6718  ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 04:57:08.173  6718-6718  ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-30 04:57:08.179  6718-6718  nativeloader            org.levimc.launcher                  D  Load libpreloader.so using class loader ns clns-7 (caller=/data/app/~~WHkPXWfss6KNykoNatF7Hg==/org.levimc.launcher--vPQaXYmGu4oG5BGnUECgQ==/base.apk!classes7.dex): dlopen failed: library "libpreloader.so" not found
2025-07-30 04:57:08.179  6718-6718  AndroidRuntime          org.levimc.launcher                  D  Shutting down VM
2025-07-30 04:57:08.180  6718-6718  AndroidRuntime          org.levimc.launcher                  E  FATAL EXCEPTION: main
                                                                                                    Process: org.levimc.launcher, PID: 6718
                                                                                                    java.lang.UnsatisfiedLinkError: dlopen failed: library "libpreloader.so" not found
                                                                                                    	at java.lang.Runtime.loadLibrary0(Runtime.java:1090)
                                                                                                    	at java.lang.Runtime.loadLibrary0(Runtime.java:1012)
                                                                                                    	at java.lang.System.loadLibrary(System.java:1765)
                                                                                                    	at org.levimc.launcher.core.minecraft.NativeMinecraftLauncher.<clinit>(NativeMinecraftLauncher.java:21)
                                                                                                    	at java.lang.Class.newInstance(Native Method)
                                                                                                    	at android.app.AppComponentFactory.instantiateActivity(AppComponentFactory.java:95)
                                                                                                    	at androidx.core.app.CoreComponentFactory.instantiateActivity(CoreComponentFactory.java:44)
                                                                                                    	at android.app.Instrumentation.newActivity(Instrumentation.java:1448)
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4325)
                                                                                                    	at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)
                                                                                                    	at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:107)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-30 04:57:08.447  1598-1804  WindowManager           system_server                        E  win=Window{f070320 u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=0 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator$$ExternalSyntheticLambda1.run:28 com.android.server.wm.SurfaceAnimator$$ExternalSyntheticLambda0.onAnimationFinished:65 com.android.server.wm.LocalAnimationAdapter$$ExternalSyntheticLambda0.run:10 android.os.Handler.handleCallback:959 
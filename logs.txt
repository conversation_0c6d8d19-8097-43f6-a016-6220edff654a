2025-07-30 04:59:35.755  7341-7341  VRI[MainAc...y]@c1ad8e3 org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-30 04:59:35.831  7341-7341  VRI[MainAc...y]@c1ad8e3 org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-30 04:59:35.854  7341-7434  LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-30 04:59:35.854  7341-7434  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:59:35.854  7341-7434  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:59:35.862  7341-7341  Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-30 04:59:35.898  7341-7341  DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@fd5cd3f
2025-07-30 04:59:35.899  7341-7341  DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@3ab896a
2025-07-30 04:59:35.899  7341-7341  WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{5e3fe5b V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:302 
2025-07-30 04:59:35.902  7341-7341  ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-30 04:59:35.903  7341-7359  NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-30 04:59:35.911  7341-7341  InputTransport          org.levimc.launcher                  D  Input channel constructed: 'baf0ae1', fd=173
2025-07-30 04:59:35.912  7341-7341  InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 04:59:35.913  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-30 04:59:35.913  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@5e3fe5b IsHRR=false TM=true
2025-07-30 04:59:35.924  7341-7434  LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-30 04:59:35.925  7341-7341  BufferQueueConsumer     org.levimc.launcher                  D  [](id:1cad00000002,api:0,p:-1,c:7341) connect: controlledByApp=false
2025-07-30 04:59:35.925  7341-7341  BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:0,s:0) constructor()
2025-07-30 04:59:35.925  7341-7341  BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@ef536f8 mNativeObject= 0xb4000079e59e7000 sc.mNativeObject= 0xb4000079883f6e00 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-30 04:59:35.925  7341-7341  BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@ef536f8 mNativeObject= 0xb4000079e59e7000 sc.mNativeObject= 0xb4000079883f6e00 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-30 04:59:35.925  7341-7341  BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-30 04:59:35.926  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=7 res=0x3 s={true 0xb4000079891d9000} ch=true seqId=0
2025-07-30 04:59:35.927  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-30 04:59:35.928  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000079891d9000} hwInitialized=true
2025-07-30 04:59:35.929  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-30 04:59:35.929  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@ef536f8#8
2025-07-30 04:59:35.929  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@ef536f8#9
2025-07-30 04:59:35.930  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 04:59:35.933  7341-7378  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-30 04:59:35.934  7341-7378  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  mWNT: t=0xb400007989217280 mBlastBufferQueue=0xb4000079e59e7000 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-30 04:59:35.934  7341-7378  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 04:59:35.937  7341-7359  BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 04:59:35.938  7341-7359  BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=291323853157517(auto) mPendingTransactions.size=0 graphicBufferId=31529354919960 transform=7
2025-07-30 04:59:35.938  7341-7359  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-30 04:59:35.939  7341-7359  HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 7341    Tid : 7359
2025-07-30 04:59:35.939  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 04:59:35.940  7341-7359  HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-30 04:59:35.949  7341-7341  ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 04:59:35.950  7341-7341  ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 04:59:35.980  7341-7434  LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-30 04:59:35.997  7341-7341  VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000079891d9000}
2025-07-30 04:59:35.998  7341-7341  InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-30 04:59:35.998  7341-7341  InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-30 04:59:36.002  7341-7354  InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=163
2025-07-30 04:59:36.010  7341-7341  InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-30 04:59:36.034  7341-7434  LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-30 04:59:36.080  7341-7434  LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-30 04:59:36.081  7341-7434  LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-30 04:59:36.081  7341-7434  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:59:36.081  7341-7434  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:59:36.081  7341-7434  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:59:36.081  7341-7434  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:59:36.086  7341-7438  nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk!classes7.dex): ok
2025-07-30 04:59:36.089  7341-7438  nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk!classes7.dex): ok
2025-07-30 04:59:36.196  7341-7438  nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk!classes7.dex): ok
2025-07-30 04:59:36.197  7341-7438  Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x78d6c785f0
2025-07-30 04:59:36.198  7341-7438  Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-30 04:59:36.233  7341-7341  ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-30 04:59:36.248  7341-7341  levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/content/res/AssetManager;->addAssetPath(Ljava/lang/String;)I (runtime_flags=0, domain=platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/NativeMinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 04:59:36.257  7341-7341  DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@ffb3a6c
2025-07-30 04:59:36.262  7341-7341  TransactionExecutor     org.levimc.launcher                  E  Failed to execute the transaction: tId:-924898556 ClientTransaction{
                                                                                                    tId:-924898556   transactionItems=[
                                                                                                    tId:-924898556     LaunchActivityItem{activityToken=android.os.BinderProxy@400767d,intent=Intent { flg=0x14000000 cmp=org.levimc.launcher/.core.minecraft.NativeMinecraftLauncher (has extras) },ident=37687223,info=ActivityInfo{725a396 org.levimc.launcher.core.minecraft.NativeMinecraftLauncher},curConfig={1.0 470mcc3mnc [en_GB] ldltr sw384dp w832dp h384dp 450dpi nrml long land night finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 2340, 1080) mAppBounds=Rect(0, 0 - 2340, 1080) mMaxBounds=Rect(0, 0 - 2340, 1080) mDisplayRotation=ROTATION_90 mWindowingMode=fullscreen mActivityType=undefined mAlwaysOnTop=undefined mRotation=ROTATION_90 mStageConfig=undefined mPopOver=off mOverlappingWithCutout=false mCompatSandboxScale=-1.0} s.74837 fontWeightAdjustment=0 ff=0 bf=0 bts=0 cst=1.0 nightDim=0 themeSeq=0},overrideConfig={1.0 470mcc3mnc [en_GB] ldltr sw384dp w832dp h384dp 450dpi nrml long land night finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 2340, 1080) mAppBounds=Rect(0, 0 - 2340, 1080) mMaxBounds=Rect(0, 0 - 2340, 1080) mDisplayRotation=ROTATION_90 mWindowingMode=fullscreen mActivityType=standard mAlwaysOnTop=undefined mRotation=ROTATION_90 mStageConfig=undefined mPopOver=off mOverlappingWithCutout=false mCompatSandboxScale=-1.0} s.2 fontWeightAdjustment=0 ff=0 bf=0 bts=0 cst=1.0 nightDim=0 themeSeq=0},deviceId=0,referrer=org.levimc.launcher,procState=2,state=null,persistentState=null,pendingResults=null,pendingNewIntents=null,sceneTransitionInfo=null,profilerInfo=null,assistToken=android.os.BinderProxy@947c63b,shareableActivityToken=android.os.BinderProxy@e979158,activityWindowInfo=ActivityWindowInfo{isEmbedded=false, taskBounds=Rect(0, 0 - 2340, 1080), taskFragmentBounds=Rect(0, 0 - 2340, 1080)}}
                                                                                                    tId:-924898556     ResumeActivityItem{mActivityToken=android.os.BinderProxy@400767d,procState=-1,updateProcState=false,isForward=false,shouldSendCompatFakeFocus=false}
                                                                                                    tId:-924898556     Target activity: org.levimc.launcher.core.minecraft.NativeMinecraftLauncher
                                                                                                    tId:-924898556   ]
                                                                                                    tId:-924898556 }
2025-07-30 04:59:36.262  7341-7341  AndroidRuntime          org.levimc.launcher                  D  Shutting down VM
2025-07-30 04:59:36.264  7341-7341  AndroidRuntime          org.levimc.launcher                  E  FATAL EXCEPTION: main
                                                                                                    Process: org.levimc.launcher, PID: 7341
                                                                                                    java.lang.RuntimeException: Unable to start activity ComponentInfo{org.levimc.launcher/org.levimc.launcher.core.minecraft.NativeMinecraftLauncher}: java.lang.RuntimeException: java.lang.IllegalArgumentException: Unable to find native library preloader using classloader: dalvik.system.PathClassLoader[DexPathList[[zip file "/data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk", dex file "/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex", dex file "/data/user/0/org.levimc.launcher/code_cache/dex/classes.dex"],nativeLibraryDirectories=[/data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64, /data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/lib/arm64, /data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk!/lib/arm64-v8a, /system/lib64, /system_ext/lib64, /system/lib64, /system_ext/lib64]]]
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4458)
                                                                                                    	at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)
                                                                                                    	at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:107)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
                                                                                                    Caused by: java.lang.RuntimeException: java.lang.IllegalArgumentException: Unable to find native library preloader using classloader: dalvik.system.PathClassLoader[DexPathList[[zip file "/data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk", dex file "/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex", dex file "/data/user/0/org.levimc.launcher/code_cache/dex/classes.dex"],nativeLibraryDirectories=[/data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64, /data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/lib/arm64, /data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk!/lib/arm64-v8a, /system/lib64, /system_ext/lib64, /system/lib64, /system_ext/lib64]]]
                                                                                                    	at org.levimc.launcher.core.minecraft.NativeMinecraftLauncher.onCreate(NativeMinecraftLauncher.java:34)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:9363)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:9332)
                                                                                                    	at android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)
                                                                                                    	at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689) 
                                                                                                    	at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112) 
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174) 
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109) 
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81) 
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899) 
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:107) 
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257) 
                                                                                                    	at android.os.Looper.loop(Looper.java:342) 
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638) 
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method) 
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619) 
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929) 
2025-07-30 04:59:36.264  7341-7341  AndroidRuntime          org.levimc.launcher                  E  Caused by: java.lang.IllegalArgumentException: Unable to find native library preloader using classloader: dalvik.system.PathClassLoader[DexPathList[[zip file "/data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk", dex file "/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex", dex file "/data/user/0/org.levimc.launcher/code_cache/dex/classes.dex"],nativeLibraryDirectories=[/data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64, /data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/lib/arm64, /data/app/~~IM3k6KV4A9iKT55c81imxg==/org.levimc.launcher-mBAETcQ-iMpuTFv8gdFw9w==/base.apk!/lib/arm64-v8a, /system/lib64, /system_ext/lib64, /system/lib64, /system_ext/lib64]]]
                                                                                                    	at android.app.NativeActivity.onCreate(NativeActivity.java:164)
                                                                                                    	at org.levimc.launcher.core.minecraft.NativeMainActivity.onCreate(NativeMainActivity.java:13)
                                                                                                    	at org.levimc.launcher.core.minecraft.NativeMinecraftLauncher.onCreate(NativeMinecraftLauncher.java:32)
                                                                                                    	... 17 more
2025-07-30 04:59:36.312  7341-7341  Process                 org.levimc.launcher                  I  Sending signal. PID: 7341 SIG: 9
---------------------------- PROCESS ENDED (7341) for package org.levimc.launcher ----------------------------
2025-07-30 04:59:36.561  1598-1804  WindowManager           system_server                        E  win=Window{baf0ae1 u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=0 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator$$ExternalSyntheticLambda1.run:28 com.android.server.wm.SurfaceAnimator$$ExternalSyntheticLambda0.onAnimationFinished:65 com.android.server.wm.LocalAnimationAdapter$$ExternalSyntheticLambda0.run:10 android.os.Handler.handleCallback:959 

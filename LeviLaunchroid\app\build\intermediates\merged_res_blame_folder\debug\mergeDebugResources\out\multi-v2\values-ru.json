{"logs": [{"outputFile": "org.levimc.launcher.app-mergeDebugResources-30:/values-ru/values-ru.xml", "map": [{"source": "D:\\Levi\\LeviLaunchroid\\app\\src\\main\\res\\values-ru\\strings.xml", "from": {"startLines": "15,119,90,2,40,79,138,31,16,132,122,124,123,131,121,85,92,11,71,137,47,127,112,129,116,117,115,114,51,43,44,17,39,86,63,57,26,25,58,61,89,33,69,68,128,136,5,23,22,34,3,102,101,10,12,8,60,83,49,50,9,66,64,53,54,29,28,27,103,95,99,97,104,100,98,94,96,105,21,20,139,74,73,30,38,55,37,110,108,109,107,126,125,77,76,88,87,84,45,4,91,134,59,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "599,7127,4896,38,2121,4247,8320,1619,655,8041,7330,7546,7486,7976,7258,4535,5053,426,3664,8275,2429,7742,6428,7913,7028,7076,6573,6496,2600,2195,2273,733,2068,4599,3326,2986,1223,1149,3044,3248,4813,1670,3572,3515,7833,8232,182,1089,1032,1739,89,5876,5790,361,504,249,3150,4367,2473,2552,302,3448,3389,2642,2760,1522,1418,1348,6003,5218,5626,5416,6052,5690,5483,5126,5289,6136,921,839,8362,3790,3736,1574,1923,2881,1825,6374,6257,6320,6211,7671,7605,3947,3861,4745,4672,4441,2354,138,4976,8139,3092,4310", "endColumns": "54,127,78,49,41,61,40,47,76,94,154,57,58,63,70,62,69,76,68,43,40,89,64,59,46,47,453,75,38,76,79,80,51,71,61,56,123,72,46,74,81,67,88,55,78,41,44,56,55,57,47,125,84,63,64,51,96,72,77,46,57,63,55,116,119,50,102,68,47,69,62,65,82,98,141,90,125,71,109,80,43,67,52,43,143,101,96,50,61,52,44,69,64,296,84,66,71,92,71,42,75,61,56,51", "endOffsets": "649,7250,4970,83,2158,4304,8356,1662,727,8131,7480,7599,7540,8035,7324,4593,5118,498,3728,8314,2465,7827,6488,7968,7070,7119,7022,6567,2634,2267,2348,809,2115,4666,3383,3038,1342,1217,3086,3318,4890,1733,3656,3566,7907,8269,222,1141,1083,1792,132,5997,5870,420,564,296,3242,4435,2546,2594,355,3507,3440,2754,2875,1568,1516,1412,6046,5283,5684,5477,6130,5784,5620,5212,5410,6203,1026,915,8401,3853,3784,1613,2062,2978,1917,6420,6314,6368,6251,7736,7665,4239,3941,4807,4739,4529,2421,176,5047,8196,3144,4357"}, "to": {"startLines": "35,36,37,38,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,72,74,75,76,77,78,79,80,82,83,84,85,87,88,89,90,91,92,93,94,95,96,97,98,100,101,102,103,104,118,119,120,121,122,123,169,170,171,172,173,174,175,176,177,178,179,180,182,183,184,185,186,187,188,189,190,191,192,193,194,198,199,201,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,3203,3331,3410,4616,4892,4954,5087,5135,5212,5307,5462,5520,5579,5643,5714,5777,5847,5924,5993,6037,6153,6309,6374,6434,6481,6529,6983,7059,7197,7274,7354,7435,7564,7636,7698,7755,7879,7952,7999,8074,8156,8224,8313,8369,8511,8553,8598,8655,8711,9746,9794,9920,10005,10069,10134,13866,13963,14036,14114,14161,14219,14283,14339,14456,14576,14627,14730,14879,14927,14997,15060,15126,15209,15308,15450,15541,15667,15739,15849,15930,16226,16294,16426,16571,16715,16817,16914,16965,17027,17080,17125,17195,17260,17557,17642,17709,17781,17874,17946,17989,18065,18127,18184", "endColumns": "54,127,78,49,41,61,40,47,76,94,154,57,58,63,70,62,69,76,68,43,40,89,64,59,46,47,453,75,38,76,79,80,51,71,61,56,123,72,46,74,81,67,88,55,78,41,44,56,55,57,47,125,84,63,64,51,96,72,77,46,57,63,55,116,119,50,102,68,47,69,62,65,82,98,141,90,125,71,109,80,43,67,52,43,143,101,96,50,61,52,44,69,64,296,84,66,71,92,71,42,75,61,56,51", "endOffsets": "3198,3326,3405,3455,4653,4949,4990,5130,5207,5302,5457,5515,5574,5638,5709,5772,5842,5919,5988,6032,6073,6238,6369,6429,6476,6524,6978,7054,7093,7269,7349,7430,7482,7631,7693,7750,7874,7947,7994,8069,8151,8219,8308,8364,8443,8548,8593,8650,8706,8764,9789,9915,10000,10064,10129,10181,13958,14031,14109,14156,14214,14278,14334,14451,14571,14622,14725,14794,14922,14992,15055,15121,15204,15303,15445,15536,15662,15734,15844,15925,15969,16289,16342,16465,16710,16812,16909,16960,17022,17075,17120,17190,17255,17552,17637,17704,17776,17869,17941,17984,18060,18122,18179,18231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b923a94a0a3fff51545ad4d215c7ae9a\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,39,40,41,42,43,52,53,56,71,73,81,86,99,105,106,107,108,109,110,111,112,113,114,115,116,117,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,181,196,197,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3460,3538,3616,3700,3798,4658,4755,4995,6078,6243,7098,7487,8448,8769,8887,8948,9013,9070,9140,9201,9255,9371,9428,9490,9544,9618,10186,10274,10361,10464,10556,10642,10779,10863,10948,11082,11173,11249,11303,11354,11420,11492,11570,11641,11723,11803,11879,11956,12033,12140,12229,12302,12392,12487,12561,12642,12735,12790,12871,12937,13023,13108,13170,13234,13297,13369,13467,13566,13661,13753,13811,14799,16056,16150,16347", "endLines": "7,39,40,41,42,43,52,53,56,71,73,81,86,99,105,106,107,108,109,110,111,112,113,114,115,116,117,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,181,196,197,200", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3533,3611,3695,3793,3884,4750,4887,5082,6148,6304,7192,7559,8506,8882,8943,9008,9065,9135,9196,9250,9366,9423,9485,9539,9613,9741,10269,10356,10459,10551,10637,10774,10858,10943,11077,11168,11244,11298,11349,11415,11487,11565,11636,11718,11798,11874,11951,12028,12135,12224,12297,12387,12482,12556,12637,12730,12785,12866,12932,13018,13103,13165,13229,13292,13364,13462,13561,13656,13748,13806,13861,14874,16145,16221,16421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7f30831b919b2528567b209346c061ff\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,15974", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,16051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e65fc3b7d0a9477c2db825c7f4882f80\\transformed\\core-1.13.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "44,45,46,47,48,49,50,202", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3889,3987,4089,4190,4291,4396,4499,16470", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3982,4084,4185,4286,4391,4494,4611,16566"}}]}]}
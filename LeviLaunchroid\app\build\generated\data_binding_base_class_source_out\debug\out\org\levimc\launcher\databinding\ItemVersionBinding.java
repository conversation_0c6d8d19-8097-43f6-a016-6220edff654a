// Generated by view binder compiler. Do not edit!
package org.levimc.launcher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.levimc.launcher.R;

public final class ItemVersionBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final LinearLayout linearParent;

  @NonNull
  public final TextView tvVersionNameItem;

  private ItemVersionBinding(@NonNull FrameLayout rootView, @NonNull LinearLayout linearParent,
      @NonNull TextView tvVersionNameItem) {
    this.rootView = rootView;
    this.linearParent = linearParent;
    this.tvVersionNameItem = tvVersionNameItem;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemVersionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemVersionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_version, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemVersionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.linear_parent;
      LinearLayout linearParent = ViewBindings.findChildViewById(rootView, id);
      if (linearParent == null) {
        break missingId;
      }

      id = R.id.tv_version_name_item;
      TextView tvVersionNameItem = ViewBindings.findChildViewById(rootView, id);
      if (tvVersionNameItem == null) {
        break missingId;
      }

      return new ItemVersionBinding((FrameLayout) rootView, linearParent, tvVersionNameItem);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

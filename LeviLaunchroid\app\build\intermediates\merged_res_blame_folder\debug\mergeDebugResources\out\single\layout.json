[{"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/activity_main.xml", "source": "org.levimc.launcher.app-main-34:/layout/activity_main.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/item_version_group_title.xml", "source": "org.levimc.launcher.app-main-34:/layout/item_version_group_title.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/dialog_game_version_select.xml", "source": "org.levimc.launcher.app-main-34:/layout/dialog_game_version_select.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/dialog_settings.xml", "source": "org.levimc.launcher.app-main-34:/layout/dialog_settings.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/item_settings_button.xml", "source": "org.levimc.launcher.app-main-34:/layout/item_settings_button.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/alert_dialog_custom.xml", "source": "org.levimc.launcher.app-main-34:/layout/alert_dialog_custom.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/item_settings_edittext.xml", "source": "org.levimc.launcher.app-main-34:/layout/item_settings_edittext.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/dialog_install_progress.xml", "source": "org.levimc.launcher.app-main-34:/layout/dialog_install_progress.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/item_mod.xml", "source": "org.levimc.launcher.app-main-34:/layout/item_mod.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/dialog_loading.xml", "source": "org.levimc.launcher.app-main-34:/layout/dialog_loading.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/activity_splash.xml", "source": "org.levimc.launcher.app-main-34:/layout/activity_splash.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/dialog_libs_repair.xml", "source": "org.levimc.launcher.app-main-34:/layout/dialog_libs_repair.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/item_version.xml", "source": "org.levimc.launcher.app-main-34:/layout/item_version.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/dialog_apk_version_confirm.xml", "source": "org.levimc.launcher.app-main-34:/layout/dialog_apk_version_confirm.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/item_settings_spinner.xml", "source": "org.levimc.launcher.app-main-34:/layout/item_settings_spinner.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/item_settings_switch.xml", "source": "org.levimc.launcher.app-main-34:/layout/item_settings_switch.xml"}, {"merged": "org.levimc.launcher.app-mergeDebugResources-31:/layout/item_version_big_group.xml", "source": "org.levimc.launcher.app-main-34:/layout/item_version_big_group.xml"}]
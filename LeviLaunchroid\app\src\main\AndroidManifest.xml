<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingClass">

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35"
        tools:overrideLibrary="androidx.core.splashscreen" />

    <uses-feature
        android:glEsVersion="0x20000"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <queries>
        <package android:name="com.mojang.minecraftpe" />
    </queries>

    <application
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/Base.Theme.FullScreen"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true">

        <activity
            android:name="org.levimc.launcher.ui.activities.SplashActivity"
            android:exported="true"
            android:screenOrientation="sensorLandscape"
            tools:ignore="DiscouragedApi">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="org.levimc.launcher.ui.activities.MainActivity"
            android:exported="true"
            android:screenOrientation="sensorLandscape"
            tools:ignore="DiscouragedApi" />

        <activity
            android:name=".ui.activities.IntentHandler"
            android:alwaysRetainTaskState="true"
            android:configChanges="fontScale|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation|navigation|keyboardHidden|keyboard|touchscreen|locale|mnc|mcc"
            android:excludeFromRecents="false"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="sensorLandscape"
            tools:ignore="DiscouragedApi">
            <meta-data
                android:name="android.app.lib_name"
                android:value="preloader" />

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="*/*" />
                <data android:host="*" />
                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:pathPattern=".*\\.so" />
                <data android:pathPattern=".*\\..*\\.so" />
                <data android:pathPattern=".*\\..*\\..*\\.so" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.so" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.so" />
                <data android:pathPattern=".*\\.mcworld" />
                <data android:pathPattern=".*\\..*\\.mcworld" />
                <data android:pathPattern=".*\\..*\\..*\\.mcworld" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcworld" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcworld" />
                <data android:pathPattern=".*\\.mcpack" />
                <data android:pathPattern=".*\\..*\\.mcpack" />
                <data android:pathPattern=".*\\..*\\..*\\.mcpack" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcpack" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcpack" />
                <data android:pathPattern=".*\\.mcaddon" />
                <data android:pathPattern=".*\\..*\\.mcaddon" />
                <data android:pathPattern=".*\\..*\\..*\\.mcaddon" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mcaddon" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mcaddon" />
                <data android:pathPattern=".*\\.mctemplate" />
                <data android:pathPattern=".*\\..*\\.mctemplate" />
                <data android:pathPattern=".*\\..*\\..*\\.mctemplate" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mctemplate" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mctemplate" />
            </intent-filter>
            <intent-filter>
                <action android:name="xbox_live_game_invite" />
                <action android:name="xbox_live_achievement_unlock" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="org.levimc.launcher.core.minecraft.NativeMinecraftLauncher"
            android:alwaysRetainTaskState="true"
            android:configChanges="fontScale|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation|navigation|keyboardHidden|keyboard|touchscreen|locale|mnc|mcc"
            android:excludeFromRecents="false"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppFullScreenTheme"
            tools:ignore="DiscouragedApi">
            <meta-data
                android:name="android.app.lib_name"
                android:value="preloader" />
        </activity>
        <activity
            android:name="com.microsoft.xal.browser.IntentHandler"
            android:exported="true"
            android:launchMode="singleTask"
            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
            android:alwaysRetainTaskState="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <data
                    android:scheme="ms-xal-0000000048183522"
                    android:host="auth" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.microsoft.xal.browser.BrowserLaunchActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
            android:alwaysRetainTaskState="true" />
        <activity
            android:name="com.microsoft.xal.browser.WebKitWebViewController"
            android:exported="false"
            android:launchMode="singleTask"
            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
            android:alwaysRetainTaskState="true" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:exported="false"
            android:authorities="org.levimc.launcher.fileprovider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>
    </application>

</manifest>
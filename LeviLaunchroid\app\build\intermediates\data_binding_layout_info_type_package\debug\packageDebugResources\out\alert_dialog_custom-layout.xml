<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="alert_dialog_custom" modulePackage="org.levimc.launcher" filePath="app\src\main\res\layout\alert_dialog_custom.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/alert_dialog_custom_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="14"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="14" endOffset="45"/></Target><Target id="@+id/tv_message" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="28" endOffset="50"/></Target><Target id="@+id/btn_negative" view="Button"><Expressions/><location startLine="44" startOffset="12" endLine="49" endOffset="40"/></Target><Target id="@+id/btn_spacing_neg_neu" view="View"><Expressions/><location startLine="51" startOffset="12" endLine="54" endOffset="45"/></Target><Target id="@+id/btn_neutral" view="Button"><Expressions/><location startLine="56" startOffset="12" endLine="62" endOffset="43"/></Target><Target id="@+id/btn_spacing_neu_pos" view="View"><Expressions/><location startLine="64" startOffset="12" endLine="67" endOffset="45"/></Target><Target id="@+id/btn_positive" view="Button"><Expressions/><location startLine="69" startOffset="12" endLine="74" endOffset="41"/></Target></Targets></Layout>
{
    files = {
        [[src\logger\jni_logger.cpp]]
    },
    depfiles = "build\\.objs\\leviutils\\android\\arm64-v8a\\release\\src\\logger\\__cpp_jni_logger.cpp.cpp:   src/logger/jni_logger.cpp src/logger/logger.h\
",
    depfiles_format = "gcc",
    values = {
        [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        {
            [[--sysroot=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android]],
            "-nostdinc++",
            "-Qunused-arguments",
            "--target=aarch64-none-linux-android21",
            "-fPIC",
            "-Oz",
            "-std=c++20",
            "-Isrc",
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++\include]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++\libs\arm64-v8a\include]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++abi\include]],
            "-O3",
            "-DNDEBUG"
        }
    }
}
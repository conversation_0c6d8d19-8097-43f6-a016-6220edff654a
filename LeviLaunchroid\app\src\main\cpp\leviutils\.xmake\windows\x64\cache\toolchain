{
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        __checked = true,
        arch = "armeabi-v7a",
        plat = "android"
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a",
            plat = "android"
        }
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        cross = "arm-linux-androideabi-",
        arch = "armeabi-v7a",
        plat = "android",
        __global = true,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndkver = 25,
        ndk_sdkver = "21",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        __checked = true
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        __checked = true,
        arch = "armeabi-v7a",
        plat = "android"
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a",
            plat = "android"
        }
    }
}
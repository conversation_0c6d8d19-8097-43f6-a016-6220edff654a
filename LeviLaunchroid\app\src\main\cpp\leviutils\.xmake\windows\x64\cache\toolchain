{
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        __global = true,
        plat = "android",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        arch = "armeabi-v7a",
        ndkver = 25,
        cross = "arm-linux-androideabi-",
        ndk_sdkver = "21",
        __checked = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        __checked = true,
        __global = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        __checked = true,
        __global = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    }
}
{
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        plat = "android",
        __checked = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            plat = "android"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        cross = "arm-linux-androideabi-",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        __global = true,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sdkver = "21",
        ndkver = 25,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        arch = "armeabi-v7a",
        plat = "android",
        __checked = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            plat = "android"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        plat = "android",
        __checked = true
    }
}
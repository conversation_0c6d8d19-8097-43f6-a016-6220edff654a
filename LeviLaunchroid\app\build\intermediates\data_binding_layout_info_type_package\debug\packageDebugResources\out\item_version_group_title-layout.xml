<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_version_group_title" modulePackage="org.levimc.launcher" filePath="app\src\main\res\layout\item_version_group_title.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.TextView" rootNodeViewId="@+id/tv_version_code_group"><Targets><Target id="@+id/tv_version_code_group" tag="layout/item_version_group_title_0" view="TextView"><Expressions/><location startLine="0" startOffset="0" endLine="10" endOffset="41"/></Target></Targets></Layout>
// Generated by view binder compiler. Do not edit!
package org.levimc.launcher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.levimc.launcher.R;

public final class DialogApkVersionConfirmBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnInstall;

  @NonNull
  public final EditText editVersionName;

  @NonNull
  public final TextView textVersionError;

  private DialogApkVersionConfirmBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnInstall, @NonNull EditText editVersionName,
      @NonNull TextView textVersionError) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnInstall = btnInstall;
    this.editVersionName = editVersionName;
    this.textVersionError = textVersionError;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogApkVersionConfirmBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogApkVersionConfirmBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_apk_version_confirm, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogApkVersionConfirmBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_install;
      Button btnInstall = ViewBindings.findChildViewById(rootView, id);
      if (btnInstall == null) {
        break missingId;
      }

      id = R.id.edit_version_name;
      EditText editVersionName = ViewBindings.findChildViewById(rootView, id);
      if (editVersionName == null) {
        break missingId;
      }

      id = R.id.text_version_error;
      TextView textVersionError = ViewBindings.findChildViewById(rootView, id);
      if (textVersionError == null) {
        break missingId;
      }

      return new DialogApkVersionConfirmBinding((LinearLayout) rootView, btnCancel, btnInstall,
          editVersionName, textVersionError);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

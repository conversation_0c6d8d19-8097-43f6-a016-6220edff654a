<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="4dp"
    android:layout_marginStart="0dp"
    android:layout_marginEnd="12dp">

    <LinearLayout
        android:id="@+id/linear_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="52dp"
        android:background="@drawable/bg_third_level_item"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:orientation="vertical"
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:id="@+id/tv_version_name_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="#666666"/>
    </LinearLayout>
</FrameLayout>
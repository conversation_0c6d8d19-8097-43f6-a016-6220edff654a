org.levimc.launcher:styleable/include = 0x7f130099
org.levimc.launcher:styleable/ViewStubCompat = 0x7f130097
org.levimc.launcher:styleable/ViewPager2 = 0x7f130096
org.levimc.launcher:styleable/View = 0x7f130094
org.levimc.launcher:styleable/Variant = 0x7f130093
org.levimc.launcher:styleable/Transition = 0x7f130092
org.levimc.launcher:styleable/Toolbar = 0x7f13008f
org.levimc.launcher:styleable/SwitchCompat = 0x7f130086
org.levimc.launcher:styleable/StateSet = 0x7f130085
org.levimc.launcher:styleable/StateListDrawable = 0x7f130083
org.levimc.launcher:styleable/State = 0x7f130082
org.levimc.launcher:styleable/Spinner = 0x7f130081
org.levimc.launcher:styleable/SnackbarLayout = 0x7f130080
org.levimc.launcher:styleable/SearchView = 0x7f13007a
org.levimc.launcher:styleable/RecyclerView = 0x7f130076
org.levimc.launcher:styleable/RadialViewGroup = 0x7f130073
org.levimc.launcher:styleable/PopupWindow = 0x7f130070
org.levimc.launcher:styleable/OnSwipe = 0x7f13006f
org.levimc.launcher:styleable/NavigationView = 0x7f13006d
org.levimc.launcher:styleable/MotionTelltales = 0x7f130069
org.levimc.launcher:styleable/MotionScene = 0x7f130068
org.levimc.launcher:styleable/MotionLabel = 0x7f130066
org.levimc.launcher:styleable/MotionHelper = 0x7f130065
org.levimc.launcher:styleable/MotionEffect = 0x7f130064
org.levimc.launcher:styleable/MaterialToolbar = 0x7f13005e
org.levimc.launcher:styleable/MaterialTimePicker = 0x7f13005d
org.levimc.launcher:styleable/MaterialTextView = 0x7f13005c
org.levimc.launcher:styleable/MaterialTextAppearance = 0x7f13005b
org.levimc.launcher:styleable/MaterialSwitch = 0x7f13005a
org.levimc.launcher:styleable/MaterialShape = 0x7f130059
org.levimc.launcher:styleable/MaterialRadioButton = 0x7f130058
org.levimc.launcher:styleable/MaterialCheckBoxStates = 0x7f130056
org.levimc.launcher:styleable/MaterialAlertDialogTheme = 0x7f13004e
org.levimc.launcher:styleable/MaterialAlertDialog = 0x7f13004d
org.levimc.launcher:styleable/ListPopupWindow = 0x7f13004c
org.levimc.launcher:styleable/LinearProgressIndicator = 0x7f13004b
org.levimc.launcher:styleable/LinearLayoutCompat = 0x7f130049
org.levimc.launcher:styleable/KeyTrigger = 0x7f130047
org.levimc.launcher:styleable/KeyFramesAcceleration = 0x7f130043
org.levimc.launcher:styleable/KeyFrame = 0x7f130042
org.levimc.launcher:styleable/Grid = 0x7f13003d
org.levimc.launcher:styleable/Fragment = 0x7f130039
org.levimc.launcher:styleable/ForegroundLinearLayout = 0x7f130038
org.levimc.launcher:styleable/FontFamilyFont = 0x7f130037
org.levimc.launcher:styleable/FlowLayout = 0x7f130035
org.levimc.launcher:styleable/FloatingActionButton_Behavior_Layout = 0x7f130034
org.levimc.launcher:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f130032
org.levimc.launcher:styleable/DrawerLayout = 0x7f130030
org.levimc.launcher:styleable/CoordinatorLayout_Layout = 0x7f13002d
org.levimc.launcher:styleable/CoordinatorLayout = 0x7f13002c
org.levimc.launcher:styleable/ConstraintSet = 0x7f13002b
org.levimc.launcher:styleable/ConstraintOverride = 0x7f13002a
org.levimc.launcher:styleable/ConstraintLayout_placeholder = 0x7f130029
org.levimc.launcher:styleable/ConstraintLayout_Layout = 0x7f130027
org.levimc.launcher:styleable/CompoundButton = 0x7f130025
org.levimc.launcher:styleable/CollapsingToolbarLayout_Layout = 0x7f130023
org.levimc.launcher:styleable/CollapsingToolbarLayout = 0x7f130022
org.levimc.launcher:styleable/Carousel = 0x7f13001b
org.levimc.launcher:styleable/CardView = 0x7f13001a
org.levimc.launcher:styleable/Capability = 0x7f130019
org.levimc.launcher:styleable/ButtonBarLayout = 0x7f130018
org.levimc.launcher:styleable/BottomNavigationView = 0x7f130016
org.levimc.launcher:styleable/Badge = 0x7f130013
org.levimc.launcher:styleable/AppCompatTheme = 0x7f130012
org.levimc.launcher:styleable/AppCompatEmojiHelper = 0x7f13000d
org.levimc.launcher:styleable/AnimatedStateListDrawableTransition = 0x7f130009
org.levimc.launcher:styleable/AnimatedStateListDrawableItem = 0x7f130008
org.levimc.launcher:styleable/ActivityChooserView = 0x7f130005
org.levimc.launcher:styleable/TextAppearance = 0x7f13008a
org.levimc.launcher:styleable/ActionMode = 0x7f130004
org.levimc.launcher:styleable/ActionBarLayout = 0x7f130001
org.levimc.launcher:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f120470
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f12046b
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f120468
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.Display = 0x7f120467
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f120466
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.Button = 0x7f120465
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker = 0x7f120464
org.levimc.launcher:style/Widget.MaterialComponents.TextView = 0x7f120463
org.levimc.launcher:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f120462
org.levimc.launcher:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f120461
org.levimc.launcher:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f120460
org.levimc.launcher:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f12045f
org.levimc.launcher:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f12045c
org.levimc.launcher:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f12045b
org.levimc.launcher:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f12045a
org.levimc.launcher:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f120459
org.levimc.launcher:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f120457
org.levimc.launcher:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f120455
org.levimc.launcher:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f120453
org.levimc.launcher:style/Widget.MaterialComponents.Snackbar = 0x7f120451
org.levimc.launcher:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f12044b
org.levimc.launcher:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f120448
org.levimc.launcher:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f120446
org.levimc.launcher:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f120445
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f120441
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f12043e
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f12043c
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f12043b
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f12043a
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f120439
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f120437
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f120436
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f120434
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f120433
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f120430
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f12042f
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f12042c
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar = 0x7f12042b
org.levimc.launcher:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f120425
org.levimc.launcher:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f120424
org.levimc.launcher:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f120423
org.levimc.launcher:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f120420
org.levimc.launcher:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f12041f
org.levimc.launcher:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f12041e
org.levimc.launcher:style/Widget.MaterialComponents.Chip.Action = 0x7f120418
org.levimc.launcher:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f120415
org.levimc.launcher:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f120414
org.levimc.launcher:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f120413
org.levimc.launcher:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f120411
org.levimc.launcher:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f120410
org.levimc.launcher:style/Widget.MaterialComponents.Button.TextButton = 0x7f12040e
org.levimc.launcher:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f12040c
org.levimc.launcher:style/Widget.MaterialComponents.Button.Icon = 0x7f12040b
org.levimc.launcher:style/Widget.MaterialComponents.BottomSheet = 0x7f120408
org.levimc.launcher:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f120407
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f12042e
org.levimc.launcher:style/Widget.MaterialComponents.BottomNavigationView = 0x7f120405
org.levimc.launcher:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f120404
org.levimc.launcher:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f120403
org.levimc.launcher:style/Widget.MaterialComponents.BottomAppBar = 0x7f120402
org.levimc.launcher:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1203ff
org.levimc.launcher:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1203fd
org.levimc.launcher:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f1203f6
org.levimc.launcher:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f1203f5
org.levimc.launcher:style/Widget.Material3.Tooltip = 0x7f1203f4
org.levimc.launcher:style/Widget.Material3.Toolbar.Surface = 0x7f1203f3
org.levimc.launcher:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1203ee
org.levimc.launcher:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1203ed
org.levimc.launcher:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1203ec
org.levimc.launcher:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1203e6
org.levimc.launcher:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1203e5
org.levimc.launcher:style/Widget.Material3.TabLayout.OnSurface = 0x7f1203e3
org.levimc.launcher:style/Widget.Material3.Snackbar.FullWidth = 0x7f1203e0
org.levimc.launcher:style/Widget.Material3.SideSheet.Modal = 0x7f1203d9
org.levimc.launcher:style/Widget.Material3.SideSheet = 0x7f1203d7
org.levimc.launcher:style/Widget.Material3.SearchBar.Outlined = 0x7f1203d3
org.levimc.launcher:style/Widget.Material3.SearchBar = 0x7f1203d2
org.levimc.launcher:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1203d0
org.levimc.launcher:style/Widget.Material3.PopupMenu = 0x7f1203cc
org.levimc.launcher:style/Widget.Material3.NavigationView = 0x7f1203cb
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1203c6
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1203c5
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1203c4
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1203c1
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1203c0
org.levimc.launcher:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1203be
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1203bc
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1203bb
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Year = 0x7f1203b9
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1203b8
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1203b5
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1203b3
org.levimc.launcher:styleable/ShapeAppearance = 0x7f13007b
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1203b2
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1203b1
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1203ac
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1203a9
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1203a8
org.levimc.launcher:style/Widget.Material3.MaterialCalendar = 0x7f1203a6
org.levimc.launcher:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1203a5
org.levimc.launcher:style/Widget.Material3.LinearProgressIndicator = 0x7f1203a3
org.levimc.launcher:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1203a2
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1203a1
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f12039d
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f12039b
org.levimc.launcher:style/Widget.Material3.Snackbar.TextView = 0x7f1203e1
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f120399
org.levimc.launcher:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f120392
org.levimc.launcher:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f120390
org.levimc.launcher:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f12038f
org.levimc.launcher:style/Widget.Material3.CompoundButton.CheckBox = 0x7f120389
org.levimc.launcher:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f120385
org.levimc.launcher:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f120384
org.levimc.launcher:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f120381
org.levimc.launcher:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f120380
org.levimc.launcher:style/Widget.Material3.CircularProgressIndicator = 0x7f12037e
org.levimc.launcher:style/Widget.Material3.ChipGroup = 0x7f12037d
org.levimc.launcher:style/Widget.Material3.Chip.Filter.Elevated = 0x7f120376
org.levimc.launcher:style/Widget.Material3.CardView.Outlined = 0x7f120371
org.levimc.launcher:style/Widget.Material3.CardView.Filled = 0x7f120370
org.levimc.launcher:style/Widget.Material3.CardView.Elevated = 0x7f12036f
org.levimc.launcher:style/Widget.Material3.Button.UnelevatedButton = 0x7f12036e
org.levimc.launcher:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f120368
org.levimc.launcher:style/Widget.Material3.Button.TextButton = 0x7f120366
org.levimc.launcher:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f120365
org.levimc.launcher:style/Widget.Material3.Button.IconButton.Outlined = 0x7f120363
org.levimc.launcher:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f120362
org.levimc.launcher:style/Widget.Material3.Button.IconButton = 0x7f120360
org.levimc.launcher:style/Widget.Material3.BottomSheet.Modal = 0x7f12035b
org.levimc.launcher:style/Widget.Material3.BottomSheet = 0x7f120359
org.levimc.launcher:style/Widget.Material3.Badge = 0x7f120351
org.levimc.launcher:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120350
org.levimc.launcher:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f12034f
org.levimc.launcher:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f120471
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f120442
org.levimc.launcher:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f12034e
org.levimc.launcher:style/Widget.Material3.AppBarLayout = 0x7f12034c
org.levimc.launcher:style/Widget.MaterialComponents.Chip.Filter = 0x7f12041b
org.levimc.launcher:style/Widget.Material3.ActionMode = 0x7f12034b
org.levimc.launcher:style/Widget.Design.TextInputLayout = 0x7f120349
org.levimc.launcher:style/Widget.Design.Snackbar = 0x7f120346
org.levimc.launcher:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f120345
org.levimc.launcher:style/Widget.Design.BottomSheet.Modal = 0x7f120341
org.levimc.launcher:style/Widget.Design.AppBarLayout = 0x7f12033f
org.levimc.launcher:style/Widget.Compat.NotificationActionContainer = 0x7f12033d
org.levimc.launcher:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f120337
org.levimc.launcher:style/Widget.AppCompat.Spinner.DropDown = 0x7f120336
org.levimc.launcher:style/Widget.AppCompat.SeekBar.Discrete = 0x7f120334
org.levimc.launcher:style/Widget.MaterialComponents.PopupMenu = 0x7f12044a
org.levimc.launcher:style/Widget.AppCompat.SeekBar = 0x7f120333
org.levimc.launcher:style/Widget.AppCompat.SearchView.ActionBar = 0x7f120332
org.levimc.launcher:style/Widget.AppCompat.RatingBar.Small = 0x7f120330
org.levimc.launcher:style/Widget.AppCompat.RatingBar.Indicator = 0x7f12032f
org.levimc.launcher:style/Widget.AppCompat.RatingBar = 0x7f12032e
org.levimc.launcher:style/Widget.AppCompat.ListView.Menu = 0x7f120328
org.levimc.launcher:style/Widget.AppCompat.ListView.DropDown = 0x7f120327
org.levimc.launcher:style/Widget.AppCompat.ListView = 0x7f120326
org.levimc.launcher:style/Widget.AppCompat.Light.SearchView = 0x7f120322
org.levimc.launcher:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f120321
org.levimc.launcher:style/Widget.AppCompat.Light.PopupMenu = 0x7f120320
org.levimc.launcher:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f12031c
org.levimc.launcher:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f12031a
org.levimc.launcher:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f120319
org.levimc.launcher:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f12030b
org.levimc.launcher:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f120307
org.levimc.launcher:style/Widget.AppCompat.Button.Small = 0x7f120304
org.levimc.launcher:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f120302
org.levimc.launcher:style/Widget.AppCompat.Button.Borderless = 0x7f120300
org.levimc.launcher:style/Widget.AppCompat.Button = 0x7f1202ff
org.levimc.launcher:style/Widget.AppCompat.AutoCompleteTextView = 0x7f1202fe
org.levimc.launcher:style/Widget.AppCompat.ActivityChooserView = 0x7f1202fd
org.levimc.launcher:style/Widget.AppCompat.ActionButton.Overflow = 0x7f1202fb
org.levimc.launcher:style/Widget.AppCompat.ActionBar.TabText = 0x7f1202f7
org.levimc.launcher:style/Widget.AppCompat.ActionBar.Solid = 0x7f1202f5
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f1202f3
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1202f0
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f1202ef
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f1202ea
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f1202e7
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1202e5
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1202e0
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1202df
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Light = 0x7f1202de
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1202da
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1202d9
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1202d6
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1202d5
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1202ce
org.levimc.launcher:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f12044c
org.levimc.launcher:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1202c9
org.levimc.launcher:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1202c6
org.levimc.launcher:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1202c5
org.levimc.launcher:style/ThemeOverlay.Material3.TabLayout = 0x7f1202c4
org.levimc.launcher:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1202c2
org.levimc.launcher:style/ThemeOverlay.Material3.NavigationView = 0x7f1202bf
org.levimc.launcher:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1202bd
org.levimc.launcher:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1202bc
org.levimc.launcher:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1202bb
org.levimc.launcher:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1202b7
org.levimc.launcher:style/ThemeOverlay.Material3.Light = 0x7f1202b5
org.levimc.launcher:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1202b4
org.levimc.launcher:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1202b2
org.levimc.launcher:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f12045e
org.levimc.launcher:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1202b1
org.levimc.launcher:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1202ab
org.levimc.launcher:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f120393
org.levimc.launcher:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1202a9
org.levimc.launcher:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1202a7
org.levimc.launcher:style/ThemeOverlay.Material3.Dark = 0x7f1202a1
org.levimc.launcher:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1202a0
org.levimc.launcher:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f12029e
org.levimc.launcher:style/ThemeOverlay.Material3.Button = 0x7f120297
org.levimc.launcher:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f120294
org.levimc.launcher:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f120290
org.levimc.launcher:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f12028e
org.levimc.launcher:style/ThemeOverlay.Material3.ActionBar = 0x7f12028d
org.levimc.launcher:style/ThemeOverlay.AppCompat.Light = 0x7f12028a
org.levimc.launcher:style/ThemeOverlay.AppCompat.Dialog = 0x7f120288
org.levimc.launcher:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f120287
org.levimc.launcher:style/ThemeOverlay.AppCompat.ActionBar = 0x7f120283
org.levimc.launcher:style/ThemeOverlay.AppCompat = 0x7f120282
org.levimc.launcher:style/Theme.SplashScreen.Common = 0x7f120280
org.levimc.launcher:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f12027c
org.levimc.launcher:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f120279
org.levimc.launcher:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f120278
org.levimc.launcher:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f120277
org.levimc.launcher:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f120276
org.levimc.launcher:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120275
org.levimc.launcher:style/Theme.MaterialComponents.Light = 0x7f12026d
org.levimc.launcher:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f12026a
org.levimc.launcher:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f120268
org.levimc.launcher:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f12044d
org.levimc.launcher:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f120267
org.levimc.launcher:style/Theme.MaterialComponents.Dialog = 0x7f120264
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f12025d
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f12025c
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f120258
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f120257
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f120256
org.levimc.launcher:style/Theme.MaterialComponents.Bridge = 0x7f120252
org.levimc.launcher:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f12024c
org.levimc.launcher:styleable/ShapeableImageView = 0x7f13007c
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1203b4
org.levimc.launcher:style/Theme.Material3.Light.Dialog = 0x7f12024a
org.levimc.launcher:style/Theme.Material3.Light.BottomSheetDialog = 0x7f120249
org.levimc.launcher:style/Theme.Material3.DynamicColors.Light = 0x7f120246
org.levimc.launcher:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f120245
org.levimc.launcher:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f120243
org.levimc.launcher:style/Theme.Material3.DynamicColors.Dark = 0x7f120242
org.levimc.launcher:style/Theme.Material3.DayNight.NoActionBar = 0x7f120240
org.levimc.launcher:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f12023f
org.levimc.launcher:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f12023d
org.levimc.launcher:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f12023b
org.levimc.launcher:style/Theme.Material3.DayNight = 0x7f12023a
org.levimc.launcher:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f120236
org.levimc.launcher:style/Theme.Design.NoActionBar = 0x7f120231
org.levimc.launcher:style/Theme.Design.Light.NoActionBar = 0x7f120230
org.levimc.launcher:styleable/KeyFramesVelocity = 0x7f130044
org.levimc.launcher:style/Theme.Design.Light.BottomSheetDialog = 0x7f12022f
org.levimc.launcher:style/Theme.Design.Light = 0x7f12022e
org.levimc.launcher:style/Theme.Design = 0x7f12022c
org.levimc.launcher:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f120228
org.levimc.launcher:style/Theme.AppCompat.Empty = 0x7f120223
org.levimc.launcher:style/Theme.AppCompat.Dialog.Alert = 0x7f120220
org.levimc.launcher:styleable/MenuGroup = 0x7f13005f
org.levimc.launcher:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f12021c
org.levimc.launcher:style/Theme.AppCompat.DayNight = 0x7f120218
org.levimc.launcher:style/Theme.AppCompat.CompactMenu = 0x7f120217
org.levimc.launcher:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f120214
org.levimc.launcher:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f120211
org.levimc.launcher:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f120210
org.levimc.launcher:style/TextAppearance.MaterialComponents.Overline = 0x7f12020e
org.levimc.launcher:style/TextAppearance.MaterialComponents.Headline6 = 0x7f12020d
org.levimc.launcher:style/TextAppearance.MaterialComponents.Headline3 = 0x7f12020a
org.levimc.launcher:style/TextAppearance.MaterialComponents.Body2 = 0x7f120204
org.levimc.launcher:style/TextAppearance.MaterialComponents.Body1 = 0x7f120203
org.levimc.launcher:style/TextAppearance.Material3.TitleSmall = 0x7f120201
org.levimc.launcher:style/TextAppearance.Material3.TitleLarge = 0x7f1201ff
org.levimc.launcher:style/TextAppearance.Material3.SearchView.Prefix = 0x7f1201fe
org.levimc.launcher:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f120369
org.levimc.launcher:style/TextAppearance.Material3.SearchView = 0x7f1201fd
org.levimc.launcher:style/TextAppearance.Material3.SearchBar = 0x7f1201fc
org.levimc.launcher:style/TextAppearance.Material3.LabelLarge = 0x7f1201f8
org.levimc.launcher:style/TextAppearance.Material3.HeadlineLarge = 0x7f1201f5
org.levimc.launcher:style/TextAppearance.Material3.DisplayLarge = 0x7f1201f2
org.levimc.launcher:style/TextAppearance.Material3.BodySmall = 0x7f1201f1
org.levimc.launcher:style/TextAppearance.Material3.BodyMedium = 0x7f1201f0
org.levimc.launcher:style/TextAppearance.Material3.BodyLarge = 0x7f1201ef
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1201ec
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1201eb
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1201ea
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1201e9
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1201e7
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1201e3
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1201e1
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1201e0
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1201df
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1201de
org.levimc.launcher:style/TextAppearance.Design.Placeholder = 0x7f1201d9
org.levimc.launcher:style/TextAppearance.Design.Error = 0x7f1201d6
org.levimc.launcher:style/TextAppearance.Design.Counter = 0x7f1201d4
org.levimc.launcher:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f12031b
org.levimc.launcher:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1201d3
org.levimc.launcher:style/TextAppearance.Compat.Notification.Time = 0x7f1201d1
org.levimc.launcher:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f12040f
org.levimc.launcher:style/TextAppearance.Compat.Notification.Line2 = 0x7f1201d0
org.levimc.launcher:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f1203fa
org.levimc.launcher:style/TextAppearance.Compat.Notification = 0x7f1201ce
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1201ca
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1201c9
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1201c6
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.Button = 0x7f1201c4
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1201c2
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1201bb
org.levimc.launcher:style/TextAppearance.AppCompat.Tooltip = 0x7f1201ba
org.levimc.launcher:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1201b7
org.levimc.launcher:style/TextAppearance.AppCompat.Subhead = 0x7f1201b6
org.levimc.launcher:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1201b5
org.levimc.launcher:style/TextAppearance.AppCompat.Small = 0x7f1201b4
org.levimc.launcher:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1201b3
org.levimc.launcher:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1201b2
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1203af
org.levimc.launcher:style/TextAppearance.AppCompat.Menu = 0x7f1201b1
org.levimc.launcher:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1201ac
org.levimc.launcher:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1201ab
org.levimc.launcher:style/TextAppearance.AppCompat.Inverse = 0x7f1201a8
org.levimc.launcher:style/TextAppearance.AppCompat.Display4 = 0x7f1201a6
org.levimc.launcher:style/TextAppearance.AppCompat.Display3 = 0x7f1201a5
org.levimc.launcher:style/TextAppearance.AppCompat.Display2 = 0x7f1201a4
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f12019c
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f12019b
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f120197
org.levimc.launcher:styleable/BottomSheetBehavior_Layout = 0x7f130017
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f120194
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f120191
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f12018c
org.levimc.launcher:style/ThemeOverlay.Material3.Button.IconButton = 0x7f120299
org.levimc.launcher:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f12018a
org.levimc.launcher:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f120189
org.levimc.launcher:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f120187
org.levimc.launcher:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f120182
org.levimc.launcher:style/ShapeAppearance.Material3.MediumComponent = 0x7f120181
org.levimc.launcher:style/ShapeAppearance.Material3.LargeComponent = 0x7f120180
org.levimc.launcher:style/ShapeAppearance.Material3.Corner.Large = 0x7f12017c
org.levimc.launcher:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f12017a
org.levimc.launcher:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f120179
org.levimc.launcher:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f120176
org.levimc.launcher:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f120170
org.levimc.launcher:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f12016c
org.levimc.launcher:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f12016b
org.levimc.launcher:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f12016a
org.levimc.launcher:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f120167
org.levimc.launcher:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f120164
org.levimc.launcher:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f120163
org.levimc.launcher:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f120162
org.levimc.launcher:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f120161
org.levimc.launcher:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f12015e
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f120159
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f120157
org.levimc.launcher:style/Widget.Material3.SearchView.Toolbar = 0x7f1203d6
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f120156
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f120154
org.levimc.launcher:styleable/MockView = 0x7f130062
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f120152
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f120151
org.levimc.launcher:style/Platform.Widget.AppCompat.Spinner = 0x7f12014e
org.levimc.launcher:style/Platform.V21.AppCompat = 0x7f12014a
org.levimc.launcher:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f120149
org.levimc.launcher:style/Widget.MaterialComponents.TabLayout = 0x7f120454
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f120140
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f12013f
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f12015a
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f120138
org.levimc.launcher:style/MaterialAlertDialog.Material3.Title.Text = 0x7f120135
org.levimc.launcher:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f120134
org.levimc.launcher:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f120132
org.levimc.launcher:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f120130
org.levimc.launcher:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1201aa
org.levimc.launcher:style/MaterialAlertDialog.Material3.Body.Text = 0x7f12012f
org.levimc.launcher:styleable/ActionMenuView = 0x7f130003
org.levimc.launcher:style/MaterialAlertDialog.Material3 = 0x7f12012d
org.levimc.launcher:style/Widget.Material3.Slider.Label = 0x7f1203dc
org.levimc.launcher:style/CardView = 0x7f12012a
org.levimc.launcher:style/Base.v27.Theme.SplashScreen.Light = 0x7f120129
org.levimc.launcher:style/Base.v21.Theme.SplashScreen.Light = 0x7f120127
org.levimc.launcher:style/Base.Widget.MaterialComponents.TextView = 0x7f120125
org.levimc.launcher:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f120123
org.levimc.launcher:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f12045d
org.levimc.launcher:style/Base.Widget.MaterialComponents.Snackbar = 0x7f120122
org.levimc.launcher:style/Base.Widget.MaterialComponents.Slider = 0x7f120121
org.levimc.launcher:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f120120
org.levimc.launcher:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f12011f
org.levimc.launcher:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f12011d
org.levimc.launcher:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f12011c
org.levimc.launcher:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f120119
org.levimc.launcher:style/Base.Widget.Material3.TabLayout = 0x7f120115
org.levimc.launcher:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f120113
org.levimc.launcher:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f120112
org.levimc.launcher:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f12010e
org.levimc.launcher:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f12010d
org.levimc.launcher:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f12010b
org.levimc.launcher:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f12010a
org.levimc.launcher:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1203ef
org.levimc.launcher:style/Base.Widget.Material3.Chip = 0x7f120108
org.levimc.launcher:style/Base.Widget.Material3.BottomNavigationView = 0x7f120106
org.levimc.launcher:style/Base.Widget.Design.TabLayout = 0x7f120103
org.levimc.launcher:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f120102
org.levimc.launcher:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f120136
org.levimc.launcher:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1200fe
org.levimc.launcher:style/Base.Widget.AppCompat.Spinner = 0x7f1200fd
org.levimc.launcher:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1200fc
org.levimc.launcher:style/Base.Widget.AppCompat.SeekBar = 0x7f1200fb
org.levimc.launcher:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1200fa
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1203ae
org.levimc.launcher:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1200f8
org.levimc.launcher:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1200f0
org.levimc.launcher:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1200ef
org.levimc.launcher:style/Base.Widget.AppCompat.ListView = 0x7f1200ee
org.levimc.launcher:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1200ed
org.levimc.launcher:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1200ea
org.levimc.launcher:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1200e8
org.levimc.launcher:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1200e7
org.levimc.launcher:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1200e6
org.levimc.launcher:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1200e5
org.levimc.launcher:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1200df
org.levimc.launcher:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1200de
org.levimc.launcher:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1200dd
org.levimc.launcher:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1200db
org.levimc.launcher:style/Base.Widget.AppCompat.ButtonBar = 0x7f1200da
org.levimc.launcher:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1200d6
org.levimc.launcher:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1200d5
org.levimc.launcher:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1200cb
org.levimc.launcher:style/Base.Widget.AppCompat.ActionBar = 0x7f1200c9
org.levimc.launcher:style/Base.V7.Widget.AppCompat.EditText = 0x7f1200c7
org.levimc.launcher:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1200c6
org.levimc.launcher:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1200c4
org.levimc.launcher:style/Base.V7.Theme.AppCompat.Light = 0x7f1200c3
org.levimc.launcher:style/Base.V7.Theme.AppCompat = 0x7f1200c1
org.levimc.launcher:style/Base.V26.Theme.AppCompat.Light = 0x7f1200bd
org.levimc.launcher:style/Base.V26.Theme.AppCompat = 0x7f1200bc
org.levimc.launcher:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1200bb
org.levimc.launcher:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1200b9
org.levimc.launcher:style/Base.V24.Theme.Material3.Dark = 0x7f1200b8
org.levimc.launcher:style/Base.V23.Theme.AppCompat = 0x7f1200b6
org.levimc.launcher:style/Base.V22.Theme.AppCompat = 0x7f1200b4
org.levimc.launcher:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1200b3
org.levimc.launcher:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1200b2
org.levimc.launcher:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1200b1
org.levimc.launcher:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1200b0
org.levimc.launcher:styleable/ActionBar = 0x7f130000
org.levimc.launcher:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1200af
org.levimc.launcher:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1200ad
org.levimc.launcher:style/Base.V21.Theme.AppCompat = 0x7f1200a8
org.levimc.launcher:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1200a7
org.levimc.launcher:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1200a6
org.levimc.launcher:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1200a3
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f1202e8
org.levimc.launcher:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f1200a2
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f12009f
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents.Light = 0x7f12009c
org.levimc.launcher:style/Widget.AppCompat.DrawerArrowToggle = 0x7f12030a
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f12009a
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1201bd
org.levimc.launcher:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f120092
org.levimc.launcher:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1202b8
org.levimc.launcher:style/Base.V14.Theme.Material3.Dark = 0x7f120090
org.levimc.launcher:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f12008c
org.levimc.launcher:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f12008a
org.levimc.launcher:style/Base.ThemeOverlay.Material3.Dialog = 0x7f120088
org.levimc.launcher:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f120086
org.levimc.launcher:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f120084
org.levimc.launcher:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f120082
org.levimc.launcher:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f120080
org.levimc.launcher:style/Base.ThemeOverlay.AppCompat = 0x7f12007f
org.levimc.launcher:style/Base.Theme.SplashScreen = 0x7f12007c
org.levimc.launcher:style/Widget.AppCompat.CompoundButton.Switch = 0x7f120309
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f12007b
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f12007a
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120078
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120077
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f120074
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light = 0x7f120072
org.levimc.launcher:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f120071
org.levimc.launcher:style/TextAppearance.AppCompat.Body1 = 0x7f12019f
org.levimc.launcher:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f120070
org.levimc.launcher:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f12006f
org.levimc.launcher:style/Base.Theme.MaterialComponents.Dialog = 0x7f12006c
org.levimc.launcher:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f12006b
org.levimc.launcher:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f120068
org.levimc.launcher:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f120067
org.levimc.launcher:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f120064
org.levimc.launcher:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f120062
org.levimc.launcher:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f120061
org.levimc.launcher:style/Widget.MaterialComponents.CardView = 0x7f120416
org.levimc.launcher:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f12005e
org.levimc.launcher:style/Base.Theme.FullScreen = 0x7f12005c
org.levimc.launcher:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f120058
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f12043d
org.levimc.launcher:style/Base.Theme.AppCompat.Light = 0x7f120055
org.levimc.launcher:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f12040d
org.levimc.launcher:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f120054
org.levimc.launcher:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f120053
org.levimc.launcher:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f120051
org.levimc.launcher:style/Theme.MaterialComponents.Light.Dialog = 0x7f120272
org.levimc.launcher:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f12004c
org.levimc.launcher:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f12004a
org.levimc.launcher:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f120047
org.levimc.launcher:style/Base.TextAppearance.Material3.Search = 0x7f120046
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f120045
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f120042
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f12003c
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f12003b
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f120038
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f120033
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Title = 0x7f120032
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f120031
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Small = 0x7f12002e
org.levimc.launcher:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f12002d
org.levimc.launcher:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f12002b
org.levimc.launcher:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f120251
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Menu = 0x7f12002a
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Medium = 0x7f120028
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Large = 0x7f120024
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Headline = 0x7f120022
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Display4 = 0x7f120021
org.levimc.launcher:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120018
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1201e2
org.levimc.launcher:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f120017
org.levimc.launcher:style/Base.CardView = 0x7f120013
org.levimc.launcher:style/Base.AlertDialog.AppCompat.Light = 0x7f12000f
org.levimc.launcher:style/Theme.AppCompat = 0x7f120216
org.levimc.launcher:style/AppTheme.Light = 0x7f12000d
org.levimc.launcher:style/AppTheme.Dark = 0x7f12000c
org.levimc.launcher:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f12000a
org.levimc.launcher:style/Animation.Material3.SideSheetDialog.Right = 0x7f120009
org.levimc.launcher:style/Animation.Material3.SideSheetDialog.Left = 0x7f120008
org.levimc.launcher:style/Animation.Material3.SideSheetDialog = 0x7f120007
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f12009e
org.levimc.launcher:style/Animation.Material3.BottomSheetDialog = 0x7f120006
org.levimc.launcher:style/Animation.Design.BottomSheetDialog = 0x7f120005
org.levimc.launcher:style/Animation.AppCompat.Tooltip = 0x7f120004
org.levimc.launcher:style/Animation.AppCompat.Dialog = 0x7f120002
org.levimc.launcher:style/AlertDialog.AppCompat = 0x7f120000
org.levimc.launcher:string/version_name = 0x7f11010c
org.levimc.launcher:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f120091
org.levimc.launcher:string/version_isolation = 0x7f11010b
org.levimc.launcher:string/version = 0x7f110109
org.levimc.launcher:string/update_failed = 0x7f110105
org.levimc.launcher:string/toast_delete_success = 0x7f110102
org.levimc.launcher:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f1202fa
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1202dc
org.levimc.launcher:string/theme_follow_system = 0x7f1100fe
org.levimc.launcher:styleable/GradientColorItem = 0x7f13003c
org.levimc.launcher:string/theme_dark = 0x7f1100fd
org.levimc.launcher:string/storage_permission_title = 0x7f1100fc
org.levimc.launcher:string/status_bar_notification_info_overflow = 0x7f1100f9
org.levimc.launcher:string/skip = 0x7f1100f8
org.levimc.launcher:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f120426
org.levimc.launcher:string/settings_title = 0x7f1100f5
org.levimc.launcher:string/searchview_navigation_content_description = 0x7f1100f3
org.levimc.launcher:string/searchview_clear_text_content_description = 0x7f1100f2
org.levimc.launcher:string/searchbar_scrolling_view_behavior = 0x7f1100f1
org.levimc.launcher:string/requires_repair = 0x7f1100ec
org.levimc.launcher:string/repair_libs_failed_message = 0x7f1100e9
org.levimc.launcher:string/repair_failed = 0x7f1100e6
org.levimc.launcher:string/path_password_strike_through = 0x7f1100e2
org.levimc.launcher:string/overwrite = 0x7f1100db
org.levimc.launcher:string/overlay_permission_message = 0x7f1100d9
org.levimc.launcher:string/not_mc_apk = 0x7f1100d8
org.levimc.launcher:string/no_mods_found = 0x7f1100d6
org.levimc.launcher:string/no_minecraft = 0x7f1100d5
org.levimc.launcher:styleable/MaterialDivider = 0x7f130057
org.levimc.launcher:string/no_install_minecraft = 0x7f1100d4
org.levimc.launcher:style/Widget.MaterialComponents.CheckedTextView = 0x7f120417
org.levimc.launcher:string/new_version_found = 0x7f1100d3
org.levimc.launcher:string/name_invalid = 0x7f1100d2
org.levimc.launcher:string/mtrl_timepicker_confirm = 0x7f1100d1
org.levimc.launcher:string/mtrl_switch_track_path = 0x7f1100cf
org.levimc.launcher:string/mtrl_switch_thumb_path_name = 0x7f1100cb
org.levimc.launcher:string/mtrl_switch_thumb_path_morphing = 0x7f1100ca
org.levimc.launcher:string/mtrl_switch_thumb_path_checked = 0x7f1100c9
org.levimc.launcher:string/mtrl_switch_thumb_group_name = 0x7f1100c8
org.levimc.launcher:string/mtrl_picker_toggle_to_day_selection = 0x7f1100c5
org.levimc.launcher:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1100c4
org.levimc.launcher:string/mtrl_picker_text_input_day_abbr = 0x7f1100c0
org.levimc.launcher:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1100be
org.levimc.launcher:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1202a6
org.levimc.launcher:string/mtrl_picker_start_date_description = 0x7f1100bc
org.levimc.launcher:string/mtrl_picker_save = 0x7f1100bb
org.levimc.launcher:string/mtrl_picker_range_header_unselected = 0x7f1100ba
org.levimc.launcher:style/TextAppearance.MaterialComponents.Caption = 0x7f120206
org.levimc.launcher:string/mtrl_picker_range_header_selected = 0x7f1100b8
org.levimc.launcher:string/mtrl_picker_range_header_only_end_selected = 0x7f1100b6
org.levimc.launcher:string/mtrl_picker_out_of_range = 0x7f1100b5
org.levimc.launcher:string/mtrl_picker_invalid_format_use = 0x7f1100b1
org.levimc.launcher:string/mtrl_picker_invalid_format_example = 0x7f1100b0
org.levimc.launcher:string/mtrl_picker_date_header_unselected = 0x7f1100ac
org.levimc.launcher:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1202b0
org.levimc.launcher:string/mtrl_picker_date_header_title = 0x7f1100ab
org.levimc.launcher:string/mtrl_picker_date_header_selected = 0x7f1100aa
org.levimc.launcher:string/mtrl_picker_confirm = 0x7f1100a9
org.levimc.launcher:string/mtrl_picker_cancel = 0x7f1100a8
org.levimc.launcher:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f12031e
org.levimc.launcher:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f120016
org.levimc.launcher:string/mtrl_picker_a11y_next_month = 0x7f1100a3
org.levimc.launcher:string/mtrl_exceed_max_badge_number_suffix = 0x7f1100a2
org.levimc.launcher:string/mtrl_exceed_max_badge_number_content_description = 0x7f1100a1
org.levimc.launcher:string/mtrl_checkbox_state_description_indeterminate = 0x7f11009e
org.levimc.launcher:string/mtrl_checkbox_state_description_checked = 0x7f11009d
org.levimc.launcher:string/mtrl_checkbox_button_path_unchecked = 0x7f11009c
org.levimc.launcher:string/mtrl_checkbox_button_path_name = 0x7f11009b
org.levimc.launcher:string/mtrl_checkbox_button_path_group_name = 0x7f11009a
org.levimc.launcher:string/mtrl_picker_announce_current_selection_none = 0x7f1100a7
org.levimc.launcher:string/mtrl_checkbox_button_path_checked = 0x7f110099
org.levimc.launcher:string/mtrl_checkbox_button_icon_path_name = 0x7f110098
org.levimc.launcher:string/mtrl_checkbox_button_icon_path_group_name = 0x7f110096
org.levimc.launcher:string/mtrl_checkbox_button_icon_path_checked = 0x7f110095
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1203ba
org.levimc.launcher:string/mtrl_badge_numberless_content_description = 0x7f110094
org.levimc.launcher:string/mods_title = 0x7f110093
org.levimc.launcher:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1200c8
org.levimc.launcher:string/mod_reordered = 0x7f110092
org.levimc.launcher:string/mod_load_order = 0x7f110091
org.levimc.launcher:string/missing_libs_title = 0x7f110090
org.levimc.launcher:string/missing_libs_message = 0x7f11008f
org.levimc.launcher:string/material_timepicker_pm = 0x7f11008b
org.levimc.launcher:string/material_timepicker_minute = 0x7f11008a
org.levimc.launcher:string/material_slider_value = 0x7f110086
org.levimc.launcher:string/material_motion_easing_linear = 0x7f110082
org.levimc.launcher:string/material_motion_easing_accelerated = 0x7f11007f
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1203aa
org.levimc.launcher:string/material_minute_suffix = 0x7f11007e
org.levimc.launcher:string/material_minute_selection = 0x7f11007d
org.levimc.launcher:string/material_hour_suffix = 0x7f11007c
org.levimc.launcher:string/material_hour_24h_suffix = 0x7f11007a
org.levimc.launcher:string/material_clock_toggle_content_description = 0x7f110079
org.levimc.launcher:string/m3_sys_motion_easing_standard_decelerate = 0x7f110077
org.levimc.launcher:string/m3_sys_motion_easing_legacy_decelerate = 0x7f110073
org.levimc.launcher:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f12016d
org.levimc.launcher:string/m3_sys_motion_easing_legacy = 0x7f110071
org.levimc.launcher:string/m3_sys_motion_easing_emphasized_path_data = 0x7f110070
org.levimc.launcher:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f11006e
org.levimc.launcher:string/m3_sys_motion_easing_emphasized = 0x7f11006d
org.levimc.launcher:string/m3_ref_typeface_plain_medium = 0x7f11006b
org.levimc.launcher:string/local_custom = 0x7f110067
org.levimc.launcher:styleable/AppBarLayoutStates = 0x7f13000b
org.levimc.launcher:style/Platform.AppCompat = 0x7f120141
org.levimc.launcher:string/launch = 0x7f110064
org.levimc.launcher:string/invalid_mod_file = 0x7f110061
org.levimc.launcher:string/installing_title = 0x7f110060
org.levimc.launcher:string/installing_message = 0x7f11005f
org.levimc.launcher:string/import_apk = 0x7f110058
org.levimc.launcher:string/files_processed = 0x7f110050
org.levimc.launcher:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120400
org.levimc.launcher:string/fab_transformation_scrim_behavior = 0x7f11004e
org.levimc.launcher:string/exposed_dropdown_menu_content_description = 0x7f11004d
org.levimc.launcher:string/eula_title = 0x7f11004b
org.levimc.launcher:string/eula_message = 0x7f11004a
org.levimc.launcher:string/eula_exit = 0x7f110049
org.levimc.launcher:string/error_no_browser = 0x7f110046
org.levimc.launcher:string/error_icon_content_description = 0x7f110045
org.levimc.launcher:style/TextAppearance.Material3.LabelMedium = 0x7f1201f9
org.levimc.launcher:string/error_delete_builtin_version = 0x7f110044
org.levimc.launcher:string/error_a11y_label = 0x7f110043
org.levimc.launcher:string/enable_debug_log = 0x7f110040
org.levimc.launcher:string/drag_to_reorder = 0x7f11003f
org.levimc.launcher:string/downloading_update = 0x7f11003e
org.levimc.launcher:string/download_update = 0x7f11003d
org.levimc.launcher:string/dialog_title_delete_version = 0x7f11003c
org.levimc.launcher:string/dialog_title_delete_mod = 0x7f11003b
org.levimc.launcher:string/dialog_negative_cancel = 0x7f110039
org.levimc.launcher:string/dialog_message_delete_mod = 0x7f110037
org.levimc.launcher:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f120354
org.levimc.launcher:string/copyright = 0x7f110036
org.levimc.launcher:string/chinese = 0x7f110033
org.levimc.launcher:string/check_update = 0x7f110032
org.levimc.launcher:string/character_counter_content_description = 0x7f11002f
org.levimc.launcher:string/call_notification_screening_text = 0x7f11002d
org.levimc.launcher:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f120388
org.levimc.launcher:string/call_notification_incoming_text = 0x7f11002b
org.levimc.launcher:string/bottomsheet_drag_handle_content_description = 0x7f110026
org.levimc.launcher:string/bottom_sheet_behavior = 0x7f110021
org.levimc.launcher:string/appbar_scrolling_view_behavior = 0x7f110020
org.levimc.launcher:string/app_name = 0x7f11001f
org.levimc.launcher:string/androidx_startup = 0x7f11001e
org.levimc.launcher:string/abc_toolbar_collapse_description = 0x7f11001a
org.levimc.launcher:string/abc_shareactionprovider_share_with = 0x7f110018
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Item = 0x7f1203b6
org.levimc.launcher:string/abc_searchview_description_voice = 0x7f110017
org.levimc.launcher:string/abc_searchview_description_search = 0x7f110015
org.levimc.launcher:styleable/MaterialButtonToggleGroup = 0x7f130051
org.levimc.launcher:string/abc_searchview_description_clear = 0x7f110013
org.levimc.launcher:string/abc_menu_sym_shortcut_label = 0x7f110010
org.levimc.launcher:string/abc_menu_space_shortcut_label = 0x7f11000f
org.levimc.launcher:string/abc_menu_function_shortcut_label = 0x7f11000c
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f12046d
org.levimc.launcher:string/abc_menu_delete_shortcut_label = 0x7f11000a
org.levimc.launcher:string/abc_menu_ctrl_shortcut_label = 0x7f110009
org.levimc.launcher:string/abc_activitychooserview_choose_application = 0x7f110005
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1201c3
org.levimc.launcher:string/abc_activity_chooser_view_see_all = 0x7f110004
org.levimc.launcher:string/abc_action_menu_overflow_description = 0x7f110002
org.levimc.launcher:string/abc_action_bar_home_description = 0x7f110000
org.levimc.launcher:plurals/mtrl_badge_content_description = 0x7f100000
org.levimc.launcher:mipmap/ic_launcher_adaptive_fore = 0x7f0f0002
org.levimc.launcher:mipmap/ic_launcher = 0x7f0f0000
org.levimc.launcher:menu/language_menu = 0x7f0e0000
org.levimc.launcher:macro/m3_sys_color_dark_surface_tint = 0x7f0d0175
org.levimc.launcher:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0d0174
org.levimc.launcher:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0d0171
org.levimc.launcher:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0d016e
org.levimc.launcher:style/Theme.Material3.Dark.Dialog.Alert = 0x7f120235
org.levimc.launcher:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0d016d
org.levimc.launcher:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0d016c
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0d016a
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0d0169
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f120431
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0d0167
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0d0166
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f120037
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0d0163
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0d015d
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0d015b
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0d015a
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0d0158
org.levimc.launcher:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f120412
org.levimc.launcher:style/Base.Theme.Material3.Light.Dialog = 0x7f120065
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0d0156
org.levimc.launcher:macro/m3_comp_time_picker_container_color = 0x7f0d014e
org.levimc.launcher:style/Widget.Design.FloatingActionButton = 0x7f120343
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0d0152
org.levimc.launcher:macro/m3_comp_time_picker_clock_dial_color = 0x7f0d014c
org.levimc.launcher:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0d014b
org.levimc.launcher:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f120219
org.levimc.launcher:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0d0148
org.levimc.launcher:macro/m3_comp_text_button_label_text_color = 0x7f0d0144
org.levimc.launcher:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0d0143
org.levimc.launcher:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0d0142
org.levimc.launcher:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f12004d
org.levimc.launcher:macro/m3_comp_switch_unselected_track_color = 0x7f0d0140
org.levimc.launcher:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0d013f
org.levimc.launcher:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0d013d
org.levimc.launcher:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0d013b
org.levimc.launcher:macro/m3_comp_switch_unselected_icon_color = 0x7f0d013a
org.levimc.launcher:styleable/MaterialButton = 0x7f130050
org.levimc.launcher:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0d0138
org.levimc.launcher:style/Base.Widget.AppCompat.Button.Colored = 0x7f1200d8
org.levimc.launcher:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0d0137
org.levimc.launcher:macro/m3_comp_switch_unselected_handle_color = 0x7f0d0134
org.levimc.launcher:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0d0132
org.levimc.launcher:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0d012f
org.levimc.launcher:macro/m3_comp_switch_selected_track_color = 0x7f0d012e
org.levimc.launcher:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0d012d
org.levimc.launcher:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0d012b
org.levimc.launcher:macro/m3_comp_switch_selected_hover_track_color = 0x7f0d0128
org.levimc.launcher:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f120174
org.levimc.launcher:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0d0127
org.levimc.launcher:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0d0125
org.levimc.launcher:macro/m3_comp_switch_selected_handle_color = 0x7f0d0124
org.levimc.launcher:style/Widget.Material3.Button.Icon = 0x7f12035f
org.levimc.launcher:macro/m3_comp_switch_selected_focus_track_color = 0x7f0d0123
org.levimc.launcher:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0d0122
org.levimc.launcher:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0d011e
org.levimc.launcher:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0d011d
org.levimc.launcher:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0d011b
org.levimc.launcher:string/files_processing_error = 0x7f110051
org.levimc.launcher:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0d011a
org.levimc.launcher:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0d0118
org.levimc.launcher:macro/m3_comp_suggestion_chip_container_shape = 0x7f0d0117
org.levimc.launcher:macro/m3_comp_snackbar_supporting_text_type = 0x7f0d0116
org.levimc.launcher:macro/m3_comp_snackbar_container_shape = 0x7f0d0114
org.levimc.launcher:macro/m3_comp_slider_label_label_text_color = 0x7f0d0112
org.levimc.launcher:macro/m3_comp_slider_label_container_color = 0x7f0d0111
org.levimc.launcher:macro/m3_comp_slider_handle_color = 0x7f0d010f
org.levimc.launcher:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f120247
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f12018d
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f120073
org.levimc.launcher:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0d010e
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Day = 0x7f1203a7
org.levimc.launcher:macro/m3_comp_slider_disabled_active_track_color = 0x7f0d010c
org.levimc.launcher:style/Widget.AppCompat.ActionBar = 0x7f1202f4
org.levimc.launcher:macro/m3_comp_slider_active_track_color = 0x7f0d010b
org.levimc.launcher:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0d0109
org.levimc.launcher:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0d0108
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f12015c
org.levimc.launcher:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0d0105
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d0103
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0d0102
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0d0101
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0d00ff
org.levimc.launcher:string/m3_sys_motion_easing_standard_accelerate = 0x7f110076
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0d00fa
org.levimc.launcher:macro/m3_comp_search_view_header_input_text_type = 0x7f0d00f5
org.levimc.launcher:macro/m3_comp_search_view_header_input_text_color = 0x7f0d00f4
org.levimc.launcher:macro/m3_comp_search_view_docked_container_shape = 0x7f0d00f3
org.levimc.launcher:string/material_timepicker_clock_mode_description = 0x7f110088
org.levimc.launcher:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0d00f0
org.levimc.launcher:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f12042a
org.levimc.launcher:macro/m3_comp_search_bar_supporting_text_type = 0x7f0d00ef
org.levimc.launcher:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0d00ed
org.levimc.launcher:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0d00ec
org.levimc.launcher:macro/m3_comp_search_bar_input_text_color = 0x7f0d00e9
org.levimc.launcher:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0d00e3
org.levimc.launcher:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1202a8
org.levimc.launcher:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f12029d
org.levimc.launcher:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0d00de
org.levimc.launcher:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0d00dd
org.levimc.launcher:macro/m3_comp_radio_button_selected_icon_color = 0x7f0d00dc
org.levimc.launcher:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0d00da
org.levimc.launcher:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0d00d9
org.levimc.launcher:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0d00d8
org.levimc.launcher:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f120358
org.levimc.launcher:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0d00d7
org.levimc.launcher:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0d00d6
org.levimc.launcher:style/Base.V14.Theme.Material3.Light = 0x7f120094
org.levimc.launcher:string/error = 0x7f110042
org.levimc.launcher:macro/m3_comp_progress_indicator_track_color = 0x7f0d00d5
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0d00d3
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0d00cf
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0d00ce
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0d00cb
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0d00ca
org.levimc.launcher:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0d00c5
org.levimc.launcher:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0d00be
org.levimc.launcher:style/Platform.V25.AppCompat = 0x7f12014c
org.levimc.launcher:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0d00bb
org.levimc.launcher:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0d00ba
org.levimc.launcher:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0d00b9
org.levimc.launcher:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0d00b7
org.levimc.launcher:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0d00b6
org.levimc.launcher:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0d00b5
org.levimc.launcher:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0d00b4
org.levimc.launcher:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0d00b3
org.levimc.launcher:macro/m3_comp_outlined_text_field_container_shape = 0x7f0d00b1
org.levimc.launcher:macro/m3_comp_outlined_text_field_caret_color = 0x7f0d00b0
org.levimc.launcher:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0d00ad
org.levimc.launcher:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0d00ab
org.levimc.launcher:style/ShapeAppearance.MaterialComponents.Badge = 0x7f120186
org.levimc.launcher:style/Base.Animation.AppCompat.Dialog = 0x7f120010
org.levimc.launcher:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0d00aa
org.levimc.launcher:macro/m3_comp_outlined_card_container_shape = 0x7f0d00a9
org.levimc.launcher:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0d012a
org.levimc.launcher:macro/m3_comp_outlined_card_container_color = 0x7f0d00a8
org.levimc.launcher:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0d00a7
org.levimc.launcher:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0d00a5
org.levimc.launcher:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0d00a4
org.levimc.launcher:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0d00a3
org.levimc.launcher:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0d00a1
org.levimc.launcher:string/illegal_apk_title = 0x7f110057
org.levimc.launcher:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0d00a0
org.levimc.launcher:macro/m3_comp_navigation_rail_label_text_type = 0x7f0d009f
org.levimc.launcher:macro/m3_comp_navigation_rail_container_color = 0x7f0d0099
org.levimc.launcher:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0d0098
org.levimc.launcher:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0d0097
org.levimc.launcher:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0d0094
org.levimc.launcher:string/abc_action_bar_up_description = 0x7f110001
org.levimc.launcher:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0d0093
org.levimc.launcher:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0d0092
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0d008f
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0d008c
org.levimc.launcher:string/settings_desc = 0x7f1100f4
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0d008a
org.levimc.launcher:string/abc_searchview_description_query = 0x7f110014
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0d0155
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0d0088
org.levimc.launcher:macro/m3_comp_navigation_drawer_headline_type = 0x7f0d0085
org.levimc.launcher:macro/m3_comp_navigation_drawer_headline_color = 0x7f0d0084
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0d0083
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0d0082
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0d007e
org.levimc.launcher:string/language = 0x7f110063
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0d007d
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0d007c
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0d007b
org.levimc.launcher:style/TextAppearance.AppCompat.Display1 = 0x7f1201a3
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0d006f
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0d006d
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0d006c
org.levimc.launcher:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0d006a
org.levimc.launcher:styleable/ThemeEnforcement = 0x7f13008e
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f120438
org.levimc.launcher:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0d0069
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f12015b
org.levimc.launcher:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0d0068
org.levimc.launcher:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0d0067
org.levimc.launcher:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0d0066
org.levimc.launcher:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0d005e
org.levimc.launcher:macro/m3_comp_input_chip_container_shape = 0x7f0d005b
org.levimc.launcher:style/TextAppearance.MaterialComponents.Badge = 0x7f120202
org.levimc.launcher:macro/m3_comp_icon_button_selected_icon_color = 0x7f0d0059
org.levimc.launcher:macro/m3_comp_filter_chip_container_shape = 0x7f0d0057
org.levimc.launcher:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0d0055
org.levimc.launcher:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0d0054
org.levimc.launcher:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0d0053
org.levimc.launcher:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0d0051
org.levimc.launcher:macro/m3_comp_filled_text_field_input_text_type = 0x7f0d0050
org.levimc.launcher:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0d004d
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f12043f
org.levimc.launcher:macro/m3_comp_filled_text_field_container_shape = 0x7f0d004c
org.levimc.launcher:macro/m3_comp_filled_text_field_container_color = 0x7f0d004b
org.levimc.launcher:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0d004a
org.levimc.launcher:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0d0049
org.levimc.launcher:macro/m3_comp_filled_card_container_shape = 0x7f0d0047
org.levimc.launcher:macro/m3_comp_filled_button_container_color = 0x7f0d0043
org.levimc.launcher:macro/m3_comp_fab_tertiary_icon_color = 0x7f0d0040
org.levimc.launcher:macro/m3_comp_fab_secondary_icon_color = 0x7f0d003c
org.levimc.launcher:macro/m3_comp_fab_primary_small_container_shape = 0x7f0d003a
org.levimc.launcher:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f120391
org.levimc.launcher:style/Base.v27.Theme.SplashScreen = 0x7f120128
org.levimc.launcher:macro/m3_comp_fab_primary_icon_color = 0x7f0d0038
org.levimc.launcher:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f12002c
org.levimc.launcher:macro/m3_comp_fab_primary_container_color = 0x7f0d0036
org.levimc.launcher:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0d0035
org.levimc.launcher:style/Base.Widget.AppCompat.ImageButton = 0x7f1200e3
org.levimc.launcher:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0d0034
org.levimc.launcher:string/repair_error = 0x7f1100e5
org.levimc.launcher:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0d0030
org.levimc.launcher:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0d002e
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f120079
org.levimc.launcher:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0d002d
org.levimc.launcher:macro/m3_comp_elevated_card_container_shape = 0x7f0d002b
org.levimc.launcher:macro/m3_comp_elevated_card_container_color = 0x7f0d002a
org.levimc.launcher:styleable/MaterialCheckBox = 0x7f130055
org.levimc.launcher:style/TextAppearance.AppCompat.Body2 = 0x7f1201a0
org.levimc.launcher:macro/m3_comp_divider_color = 0x7f0d0028
org.levimc.launcher:macro/m3_comp_dialog_supporting_text_type = 0x7f0d0027
org.levimc.launcher:macro/m3_comp_dialog_supporting_text_color = 0x7f0d0026
org.levimc.launcher:macro/m3_comp_dialog_container_shape = 0x7f0d0023
org.levimc.launcher:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0d0021
org.levimc.launcher:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0d001f
org.levimc.launcher:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0d001d
org.levimc.launcher:styleable/MaterialCalendarItem = 0x7f130053
org.levimc.launcher:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0d001a
org.levimc.launcher:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0d0017
org.levimc.launcher:style/ShapeAppearance.MaterialComponents = 0x7f120185
org.levimc.launcher:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f12006d
org.levimc.launcher:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0d0015
org.levimc.launcher:style/Base.Widget.AppCompat.PopupMenu = 0x7f1200f1
org.levimc.launcher:string/bottomsheet_action_collapse = 0x7f110022
org.levimc.launcher:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0d0014
org.levimc.launcher:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0d0013
org.levimc.launcher:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0d0010
org.levimc.launcher:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0d000f
org.levimc.launcher:macro/m3_comp_date_picker_modal_container_color = 0x7f0d000d
org.levimc.launcher:macro/m3_comp_checkbox_selected_icon_color = 0x7f0d000b
org.levimc.launcher:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0d000a
org.levimc.launcher:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0d0007
org.levimc.launcher:macro/m3_comp_badge_large_label_text_type = 0x7f0d0004
org.levimc.launcher:macro/m3_comp_badge_large_label_text_color = 0x7f0d0003
org.levimc.launcher:macro/m3_comp_assist_chip_label_text_type = 0x7f0d0001
org.levimc.launcher:layout/select_dialog_singlechoice_material = 0x7f0c007a
org.levimc.launcher:layout/select_dialog_item_material = 0x7f0c0078
org.levimc.launcher:layout/notification_template_icon_group = 0x7f0c0075
org.levimc.launcher:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1202af
org.levimc.launcher:layout/notification_action_tombstone = 0x7f0c0073
org.levimc.launcher:layout/mtrl_search_view = 0x7f0c0071
org.levimc.launcher:layout/mtrl_picker_text_input_date = 0x7f0c006e
org.levimc.launcher:layout/mtrl_picker_header_title_text = 0x7f0c006c
org.levimc.launcher:layout/mtrl_picker_header_fullscreen = 0x7f0c006a
org.levimc.launcher:layout/mtrl_picker_header_dialog = 0x7f0c0069
org.levimc.launcher:layout/mtrl_picker_fullscreen = 0x7f0c0068
org.levimc.launcher:style/Widget.Material3.Button.TonalButton.Icon = 0x7f12036d
org.levimc.launcher:layout/mtrl_picker_actions = 0x7f0c0066
org.levimc.launcher:layout/mtrl_layout_snackbar_include = 0x7f0c0064
org.levimc.launcher:layout/mtrl_calendar_year = 0x7f0c0062
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0d0076
org.levimc.launcher:layout/mtrl_calendar_vertical = 0x7f0c0061
org.levimc.launcher:layout/mtrl_calendar_months = 0x7f0c0060
org.levimc.launcher:layout/mtrl_calendar_month_navigation = 0x7f0c005f
org.levimc.launcher:layout/mtrl_calendar_horizontal = 0x7f0c005c
org.levimc.launcher:layout/mtrl_calendar_days_of_week = 0x7f0c005b
org.levimc.launcher:layout/mtrl_calendar_day_of_week = 0x7f0c005a
org.levimc.launcher:style/Theme.Material3.Light = 0x7f120248
org.levimc.launcher:layout/mtrl_auto_complete_simple_item = 0x7f0c0058
org.levimc.launcher:layout/mtrl_alert_select_dialog_item = 0x7f0c0055
org.levimc.launcher:layout/mtrl_alert_dialog = 0x7f0c0052
org.levimc.launcher:layout/material_timepicker_textinput_display = 0x7f0c0051
org.levimc.launcher:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f120406
org.levimc.launcher:layout/material_timepicker_dialog = 0x7f0c0050
org.levimc.launcher:layout/material_time_input = 0x7f0c004e
org.levimc.launcher:layout/material_time_chip = 0x7f0c004d
org.levimc.launcher:layout/material_clock_period_toggle_land = 0x7f0c0048
org.levimc.launcher:layout/material_clock_period_toggle = 0x7f0c0047
org.levimc.launcher:layout/m3_side_sheet_dialog = 0x7f0c0043
org.levimc.launcher:layout/m3_auto_complete_simple_item = 0x7f0c0042
org.levimc.launcher:layout/m3_alert_dialog = 0x7f0c003f
org.levimc.launcher:macro/m3_comp_text_button_label_text_type = 0x7f0d0145
org.levimc.launcher:layout/item_version = 0x7f0c003c
org.levimc.launcher:string/material_timepicker_am = 0x7f110087
org.levimc.launcher:layout/item_settings_button = 0x7f0c0038
org.levimc.launcher:layout/mtrl_picker_dialog = 0x7f0c0067
org.levimc.launcher:layout/dialog_libs_repair = 0x7f0c0032
org.levimc.launcher:layout/dialog_apk_version_confirm = 0x7f0c002f
org.levimc.launcher:layout/design_navigation_menu_item = 0x7f0c002c
org.levimc.launcher:layout/design_navigation_item_subheader = 0x7f0c002a
org.levimc.launcher:layout/design_navigation_item_separator = 0x7f0c0029
org.levimc.launcher:layout/design_navigation_item_header = 0x7f0c0028
org.levimc.launcher:layout/design_navigation_item = 0x7f0c0027
org.levimc.launcher:layout/design_menu_item_action_area = 0x7f0c0026
org.levimc.launcher:layout/design_bottom_sheet_dialog = 0x7f0c0021
org.levimc.launcher:layout/design_bottom_navigation_item = 0x7f0c0020
org.levimc.launcher:layout/custom_dialog = 0x7f0c001f
org.levimc.launcher:layout/abc_select_dialog_material = 0x7f0c001a
org.levimc.launcher:layout/abc_search_view = 0x7f0c0019
org.levimc.launcher:string/mtrl_chip_close_icon_content_description = 0x7f1100a0
org.levimc.launcher:layout/abc_search_dropdown_item_icons_2line = 0x7f0c0018
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f120310
org.levimc.launcher:layout/abc_screen_simple_overlay_action_mode = 0x7f0c0016
org.levimc.launcher:layout/abc_screen_content_include = 0x7f0c0014
org.levimc.launcher:layout/abc_list_menu_item_radio = 0x7f0c0011
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f120255
org.levimc.launcher:layout/abc_list_menu_item_layout = 0x7f0c0010
org.levimc.launcher:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1200a5
org.levimc.launcher:layout/abc_list_menu_item_icon = 0x7f0c000f
org.levimc.launcher:layout/abc_dialog_title_material = 0x7f0c000c
org.levimc.launcher:layout/abc_alert_dialog_button_bar_material = 0x7f0c0008
org.levimc.launcher:string/eula_agree = 0x7f110048
org.levimc.launcher:layout/abc_action_mode_bar = 0x7f0c0004
org.levimc.launcher:style/Base.V23.Theme.AppCompat.Light = 0x7f1200b7
org.levimc.launcher:layout/abc_action_menu_layout = 0x7f0c0003
org.levimc.launcher:layout/abc_action_menu_item_layout = 0x7f0c0002
org.levimc.launcher:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1203f0
org.levimc.launcher:layout/abc_action_bar_up_container = 0x7f0c0001
org.levimc.launcher:layout/abc_action_bar_title_item = 0x7f0c0000
org.levimc.launcher:interpolator/mtrl_linear_out_slow_in = 0x7f0b0011
org.levimc.launcher:style/Widget.Material3.Chip.Input.Icon = 0x7f120379
org.levimc.launcher:interpolator/mtrl_fast_out_slow_in = 0x7f0b000f
org.levimc.launcher:style/Widget.Material3.PopupMenu.Overflow = 0x7f1203cf
org.levimc.launcher:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0b000c
org.levimc.launcher:interpolator/m3_sys_motion_easing_standard = 0x7f0b000b
org.levimc.launcher:interpolator/m3_sys_motion_easing_linear = 0x7f0b000a
org.levimc.launcher:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0b0009
org.levimc.launcher:interpolator/m3_sys_motion_easing_emphasized = 0x7f0b0007
org.levimc.launcher:macro/m3_comp_dialog_headline_type = 0x7f0d0025
org.levimc.launcher:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005
org.levimc.launcher:layout/mtrl_picker_text_input_date_range = 0x7f0c006f
org.levimc.launcher:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001
org.levimc.launcher:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000
org.levimc.launcher:integer/status_bar_notification_info_maxnum = 0x7f0a0044
org.levimc.launcher:integer/mtrl_view_visible = 0x7f0a0042
org.levimc.launcher:integer/mtrl_view_invisible = 0x7f0a0041
org.levimc.launcher:integer/mtrl_view_gone = 0x7f0a0040
org.levimc.launcher:integer/mtrl_switch_track_viewport_width = 0x7f0a003e
org.levimc.launcher:integer/mtrl_switch_track_viewport_height = 0x7f0a003d
org.levimc.launcher:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f120291
org.levimc.launcher:integer/mtrl_switch_thumb_viewport_size = 0x7f0a003c
org.levimc.launcher:macro/m3_comp_slider_inactive_track_color = 0x7f0d0110
org.levimc.launcher:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0a003b
org.levimc.launcher:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0d0104
org.levimc.launcher:integer/mtrl_switch_thumb_pressed_duration = 0x7f0a003a
org.levimc.launcher:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0a0039
org.levimc.launcher:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0a0038
org.levimc.launcher:integer/mtrl_card_anim_duration_ms = 0x7f0a0035
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f120196
org.levimc.launcher:integer/material_motion_path = 0x7f0a002d
org.levimc.launcher:integer/material_motion_duration_short_2 = 0x7f0a002c
org.levimc.launcher:integer/material_motion_duration_short_1 = 0x7f0a002b
org.levimc.launcher:integer/m3_sys_shape_corner_small_corner_family = 0x7f0a0026
org.levimc.launcher:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0a0025
org.levimc.launcher:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f120097
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0d00c8
org.levimc.launcher:integer/m3_sys_shape_corner_large_corner_family = 0x7f0a0024
org.levimc.launcher:integer/m3_sys_shape_corner_full_corner_family = 0x7f0a0023
org.levimc.launcher:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0a0021
org.levimc.launcher:integer/m3_sys_motion_duration_short4 = 0x7f0a001f
org.levimc.launcher:style/Widget.Material3.MaterialDivider = 0x7f1203bd
org.levimc.launcher:integer/m3_sys_motion_duration_short2 = 0x7f0a001d
org.levimc.launcher:integer/m3_sys_motion_duration_short1 = 0x7f0a001c
org.levimc.launcher:integer/m3_sys_motion_duration_medium3 = 0x7f0a001a
org.levimc.launcher:integer/m3_sys_motion_duration_medium1 = 0x7f0a0018
org.levimc.launcher:integer/m3_sys_motion_duration_long3 = 0x7f0a0016
org.levimc.launcher:style/TextAppearance.Material3.HeadlineMedium = 0x7f1201f6
org.levimc.launcher:style/Base.V21.Theme.MaterialComponents = 0x7f1200ac
org.levimc.launcher:integer/m3_sys_motion_duration_long1 = 0x7f0a0014
org.levimc.launcher:style/Widget.Material3.DrawerLayout = 0x7f12038d
org.levimc.launcher:integer/m3_sys_motion_duration_extra_long4 = 0x7f0a0013
org.levimc.launcher:integer/m3_chip_anim_duration = 0x7f0a000f
org.levimc.launcher:integer/m3_card_anim_duration_ms = 0x7f0a000e
org.levimc.launcher:integer/m3_btn_anim_duration_ms = 0x7f0a000c
org.levimc.launcher:integer/m3_btn_anim_delay_ms = 0x7f0a000b
org.levimc.launcher:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0d0019
org.levimc.launcher:integer/m3_badge_max_number = 0x7f0a000a
org.levimc.launcher:integer/hide_password_duration = 0x7f0a0009
org.levimc.launcher:integer/app_bar_elevation_anim_duration = 0x7f0a0002
org.levimc.launcher:layout/design_navigation_menu = 0x7f0c002b
org.levimc.launcher:integer/abc_config_activityShortDur = 0x7f0a0001
org.levimc.launcher:integer/abc_config_activityDefaultDur = 0x7f0a0000
org.levimc.launcher:id/x_left = 0x7f09022d
org.levimc.launcher:id/wrap_content = 0x7f09022b
org.levimc.launcher:id/withinBounds = 0x7f090229
org.levimc.launcher:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0d00e2
org.levimc.launcher:id/withText = 0x7f090227
org.levimc.launcher:styleable/KeyCycle = 0x7f130041
org.levimc.launcher:id/west = 0x7f090226
org.levimc.launcher:macro/m3_comp_elevated_button_container_color = 0x7f0d0029
org.levimc.launcher:id/visible_removing_fragment_view_tag = 0x7f090225
org.levimc.launcher:string/mtrl_picker_navigate_to_year_description = 0x7f1100b4
org.levimc.launcher:id/visible = 0x7f090224
org.levimc.launcher:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f1201fb
org.levimc.launcher:id/view_tree_view_model_store_owner = 0x7f090223
org.levimc.launcher:id/view_tree_saved_state_registry_owner = 0x7f090222
org.levimc.launcher:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f090221
org.levimc.launcher:style/Widget.Material3.TabLayout = 0x7f1203e2
org.levimc.launcher:id/view_tree_lifecycle_owner = 0x7f090220
org.levimc.launcher:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1203a4
org.levimc.launcher:id/view_transition = 0x7f09021f
org.levimc.launcher:style/Theme.AppCompat.Dialog = 0x7f12021f
org.levimc.launcher:id/vertical_only = 0x7f09021d
org.levimc.launcher:id/useLogo = 0x7f09021b
org.levimc.launcher:id/uniform = 0x7f090218
org.levimc.launcher:id/unchecked = 0x7f090217
org.levimc.launcher:id/tv_version_name_item = 0x7f090216
org.levimc.launcher:id/tv_message = 0x7f090213
org.levimc.launcher:id/tv_big_group_title = 0x7f090212
org.levimc.launcher:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120273
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0d00cc
org.levimc.launcher:id/transition_transform = 0x7f09020f
org.levimc.launcher:styleable/ViewTransition = 0x7f130098
org.levimc.launcher:id/transition_position = 0x7f09020d
org.levimc.launcher:id/transition_layout_save = 0x7f09020b
org.levimc.launcher:style/Widget.AppCompat.ListMenuView = 0x7f120324
org.levimc.launcher:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0d0146
org.levimc.launcher:id/transition_current_scene = 0x7f090209
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1201e5
org.levimc.launcher:id/transitionToStart = 0x7f090207
org.levimc.launcher:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f12021d
org.levimc.launcher:id/touch_outside = 0x7f090205
org.levimc.launcher:id/topPanel = 0x7f090204
org.levimc.launcher:id/titleDividerNoCustom = 0x7f090200
org.levimc.launcher:id/textinput_placeholder = 0x7f0901fb
org.levimc.launcher:id/textinput_error = 0x7f0901f9
org.levimc.launcher:id/text_version_error = 0x7f0901f7
org.levimc.launcher:style/ShapeAppearance.Material3.Corner.Full = 0x7f12017b
org.levimc.launcher:id/text_minecraft_version = 0x7f0901f6
org.levimc.launcher:id/text_input_error_icon = 0x7f0901f4
org.levimc.launcher:styleable/ChipGroup = 0x7f13001e
org.levimc.launcher:macro/m3_comp_search_view_divider_color = 0x7f0d00f2
org.levimc.launcher:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0d0020
org.levimc.launcher:id/textTop = 0x7f0901f2
org.levimc.launcher:id/textStart = 0x7f0901f1
org.levimc.launcher:id/textSpacerNoButtons = 0x7f0901ef
org.levimc.launcher:id/text = 0x7f0901ec
org.levimc.launcher:id/tag_window_insets_animation_callback = 0x7f0901eb
org.levimc.launcher:id/tag_screen_reader_focusable = 0x7f0901e6
org.levimc.launcher:id/tag_on_receive_content_mime_types = 0x7f0901e5
org.levimc.launcher:id/tag_on_receive_content_listener = 0x7f0901e4
org.levimc.launcher:integer/m3_sys_motion_path = 0x7f0a0020
org.levimc.launcher:id/tag_accessibility_heading = 0x7f0901e1
org.levimc.launcher:id/tag_accessibility_clickable_spans = 0x7f0901e0
org.levimc.launcher:id/switch_value = 0x7f0901dd
org.levimc.launcher:id/supportScrollUp = 0x7f0901dc
org.levimc.launcher:id/stop = 0x7f0901d8
org.levimc.launcher:id/startVertical = 0x7f0901d5
org.levimc.launcher:id/startHorizontal = 0x7f0901d3
org.levimc.launcher:id/start = 0x7f0901d2
org.levimc.launcher:id/standard = 0x7f0901d1
org.levimc.launcher:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f120052
org.levimc.launcher:id/src_in = 0x7f0901cf
org.levimc.launcher:id/square = 0x7f0901cd
org.levimc.launcher:id/spring = 0x7f0901cc
org.levimc.launcher:id/spread = 0x7f0901ca
org.levimc.launcher:style/ThemeOverlay.Design.TextInputEditText = 0x7f12028b
org.levimc.launcher:id/spline = 0x7f0901c8
org.levimc.launcher:id/splashscreen_icon_view = 0x7f0901c7
org.levimc.launcher:style/Base.V24.Theme.Material3.Light = 0x7f1200ba
org.levimc.launcher:id/transition_image_transform = 0x7f09020a
org.levimc.launcher:id/south = 0x7f0901c3
org.levimc.launcher:id/snap = 0x7f0901c1
org.levimc.launcher:id/snackbar_text = 0x7f0901c0
org.levimc.launcher:id/skipped = 0x7f0901bd
org.levimc.launcher:id/skipCollapsed = 0x7f0901bc
org.levimc.launcher:id/showHome = 0x7f0901b9
org.levimc.launcher:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f1203f8
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f120190
org.levimc.launcher:id/shortcut = 0x7f0901b7
org.levimc.launcher:id/sharedValueUnset = 0x7f0901b6
org.levimc.launcher:id/settings_title = 0x7f0901b4
org.levimc.launcher:id/settings_items = 0x7f0901b3
org.levimc.launcher:id/selection_type = 0x7f0901b0
org.levimc.launcher:id/select_dialog_listview = 0x7f0901ad
org.levimc.launcher:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f12010c
org.levimc.launcher:id/search_voice_btn = 0x7f0901ac
org.levimc.launcher:style/Platform.V25.AppCompat.Light = 0x7f12014d
org.levimc.launcher:style/Base.Theme.SplashScreen.Light = 0x7f12007e
org.levimc.launcher:id/search_plate = 0x7f0901aa
org.levimc.launcher:id/search_mag_icon = 0x7f0901a9
org.levimc.launcher:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1203e7
org.levimc.launcher:layout/abc_list_menu_item_checkbox = 0x7f0c000e
org.levimc.launcher:id/search_go_btn = 0x7f0901a8
org.levimc.launcher:id/search_close_btn = 0x7f0901a6
org.levimc.launcher:id/search_badge = 0x7f0901a3
org.levimc.launcher:id/scrollable = 0x7f0901a2
org.levimc.launcher:id/scrollIndicatorUp = 0x7f0901a0
org.levimc.launcher:id/scrollIndicatorDown = 0x7f09019f
org.levimc.launcher:id/scroll = 0x7f09019e
org.levimc.launcher:id/scale = 0x7f09019c
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d00d0
org.levimc.launcher:id/root_layout = 0x7f090196
org.levimc.launcher:id/right_icon = 0x7f090194
org.levimc.launcher:id/rightToLeft = 0x7f090193
org.levimc.launcher:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1201ed
org.levimc.launcher:id/right = 0x7f090192
org.levimc.launcher:style/TextAppearance.AppCompat.Headline = 0x7f1201a7
org.levimc.launcher:id/search_edit_frame = 0x7f0901a7
org.levimc.launcher:id/reverseSawtooth = 0x7f090191
org.levimc.launcher:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f12027a
org.levimc.launcher:id/report_drawn = 0x7f090190
org.levimc.launcher:id/text_input_start_icon = 0x7f0901f5
org.levimc.launcher:id/rectangles = 0x7f09018e
org.levimc.launcher:styleable/Constraint = 0x7f130026
org.levimc.launcher:id/ratio = 0x7f09018d
org.levimc.launcher:id/radio = 0x7f09018c
org.levimc.launcher:id/progress_loader = 0x7f09018a
org.levimc.launcher:id/progress_horizontal = 0x7f090189
org.levimc.launcher:id/progress_circular = 0x7f090188
org.levimc.launcher:id/progress_bar = 0x7f090187
org.levimc.launcher:id/pin = 0x7f090183
org.levimc.launcher:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1200ae
org.levimc.launcher:id/percent = 0x7f090182
org.levimc.launcher:style/Widget.Design.NavigationView = 0x7f120344
org.levimc.launcher:id/peekHeight = 0x7f090181
org.levimc.launcher:string/m3_ref_typeface_brand_medium = 0x7f110069
org.levimc.launcher:id/path = 0x7f09017f
org.levimc.launcher:id/password_toggle = 0x7f09017e
org.levimc.launcher:id/parentRelative = 0x7f09017c
org.levimc.launcher:id/parentPanel = 0x7f09017b
org.levimc.launcher:id/parent = 0x7f09017a
org.levimc.launcher:id/packed = 0x7f090178
org.levimc.launcher:id/outline = 0x7f090175
org.levimc.launcher:id/open_search_view_toolbar_container = 0x7f090174
org.levimc.launcher:id/open_search_view_toolbar = 0x7f090173
org.levimc.launcher:id/open_search_view_search_prefix = 0x7f090171
org.levimc.launcher:styleable/ImageFilterView = 0x7f13003e
org.levimc.launcher:id/open_search_view_header_container = 0x7f09016e
org.levimc.launcher:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f120056
org.levimc.launcher:id/open_search_view_dummy_toolbar = 0x7f09016c
org.levimc.launcher:id/open_search_view_content_container = 0x7f09016a
org.levimc.launcher:id/open_search_view_clear_button = 0x7f090169
org.levimc.launcher:id/onInterceptTouchReturnSwipe = 0x7f090166
org.levimc.launcher:style/Base.V21.Theme.AppCompat.Light = 0x7f1200aa
org.levimc.launcher:id/off = 0x7f090164
org.levimc.launcher:style/Widget.MaterialComponents.Toolbar = 0x7f12046e
org.levimc.launcher:string/search_menu_title = 0x7f1100f0
org.levimc.launcher:id/notification_main_column_container = 0x7f090163
org.levimc.launcher:string/confirm = 0x7f110035
org.levimc.launcher:id/notification_main_column = 0x7f090162
org.levimc.launcher:id/notification_background = 0x7f090161
org.levimc.launcher:id/north = 0x7f090160
org.levimc.launcher:id/none = 0x7f09015e
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1203ad
org.levimc.launcher:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0d00f8
org.levimc.launcher:id/noState = 0x7f09015d
org.levimc.launcher:id/noScroll = 0x7f09015c
org.levimc.launcher:id/neverCompleteToStart = 0x7f09015b
org.levimc.launcher:id/neverCompleteToEnd = 0x7f09015a
org.levimc.launcher:id/never = 0x7f090159
org.levimc.launcher:id/navigation_header_container = 0x7f090158
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1202d8
org.levimc.launcher:id/navigation_bar_item_labels_group = 0x7f090155
org.levimc.launcher:id/navigation_bar_item_icon_view = 0x7f090154
org.levimc.launcher:style/TextAppearance.Design.HelperText = 0x7f1201d7
org.levimc.launcher:layout/mtrl_picker_header_toggle = 0x7f0c006d
org.levimc.launcher:id/multiply = 0x7f090151
org.levimc.launcher:id/mtrl_picker_text_input_range_end = 0x7f09014d
org.levimc.launcher:id/mtrl_picker_header_toggle = 0x7f09014b
org.levimc.launcher:id/mtrl_picker_fullscreen = 0x7f090147
org.levimc.launcher:id/mtrl_motion_snapshot_view = 0x7f090146
org.levimc.launcher:id/mtrl_internal_children_alpha_tag = 0x7f090145
org.levimc.launcher:id/mtrl_child_content_container = 0x7f090144
org.levimc.launcher:string/material_motion_easing_decelerated = 0x7f110080
org.levimc.launcher:id/mtrl_card_checked_layer_id = 0x7f090143
org.levimc.launcher:id/mtrl_calendar_text_input_frame = 0x7f090141
org.levimc.launcher:id/mtrl_calendar_selection_frame = 0x7f090140
org.levimc.launcher:id/mtrl_calendar_months = 0x7f09013f
org.levimc.launcher:id/mtrl_calendar_main_pane = 0x7f09013e
org.levimc.launcher:id/mtrl_calendar_days_of_week = 0x7f09013c
org.levimc.launcher:id/mtrl_calendar_day_selector_frame = 0x7f09013b
org.levimc.launcher:id/month_navigation_next = 0x7f090136
org.levimc.launcher:id/month_navigation_fragment_toggle = 0x7f090135
org.levimc.launcher:id/month_navigation_bar = 0x7f090134
org.levimc.launcher:style/Widget.AppCompat.ImageButton = 0x7f12030d
org.levimc.launcher:style/Theme.Material3.Dark.SideSheetDialog = 0x7f120239
org.levimc.launcher:id/mods_title_text = 0x7f090132
org.levimc.launcher:id/mods_recycler = 0x7f090131
org.levimc.launcher:id/mod_order = 0x7f09012f
org.levimc.launcher:id/mod_card = 0x7f09012d
org.levimc.launcher:id/middle = 0x7f09012b
org.levimc.launcher:id/matrix = 0x7f090129
org.levimc.launcher:id/material_timepicker_view = 0x7f090127
org.levimc.launcher:styleable/LinearLayoutCompat_Layout = 0x7f13004a
org.levimc.launcher:id/material_timepicker_ok_button = 0x7f090126
org.levimc.launcher:id/mtrl_picker_title_text = 0x7f09014f
org.levimc.launcher:id/material_timepicker_mode_button = 0x7f090125
org.levimc.launcher:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0d00a2
org.levimc.launcher:id/material_minute_tv = 0x7f090121
org.levimc.launcher:id/material_minute_text_input = 0x7f090120
org.levimc.launcher:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0d0008
org.levimc.launcher:integer/material_motion_duration_medium_1 = 0x7f0a0029
org.levimc.launcher:id/search_bar = 0x7f0901a4
org.levimc.launcher:id/material_label = 0x7f09011f
org.levimc.launcher:id/material_hour_tv = 0x7f09011e
org.levimc.launcher:id/material_clock_period_toggle = 0x7f09011c
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f120396
org.levimc.launcher:id/material_clock_period_pm_button = 0x7f09011b
org.levimc.launcher:id/material_clock_period_am_button = 0x7f09011a
org.levimc.launcher:style/Widget.Material3.Button.ElevatedButton = 0x7f12035d
org.levimc.launcher:integer/design_tab_indicator_anim_duration_ms = 0x7f0a0008
org.levimc.launcher:id/material_clock_level = 0x7f090119
org.levimc.launcher:id/material_clock_hand = 0x7f090118
org.levimc.launcher:id/stretch = 0x7f0901d9
org.levimc.launcher:id/material_clock_face = 0x7f090117
org.levimc.launcher:xml/file_provider_paths = 0x7f140000
org.levimc.launcher:id/material_clock_display_and_toggle = 0x7f090116
org.levimc.launcher:id/material_clock_display = 0x7f090115
org.levimc.launcher:id/masked = 0x7f090112
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f120432
org.levimc.launcher:id/marquee = 0x7f090111
org.levimc.launcher:style/Widget.MaterialComponents.ShapeableImageView = 0x7f12044f
org.levimc.launcher:style/Base.Widget.Material3.FloatingActionButton = 0x7f12010f
org.levimc.launcher:id/m3_side_sheet = 0x7f09010f
org.levimc.launcher:id/list_item = 0x7f09010e
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f12025f
org.levimc.launcher:string/dialog_positive_delete = 0x7f11003a
org.levimc.launcher:id/linear_parent = 0x7f09010c
org.levimc.launcher:id/linear = 0x7f09010b
org.levimc.launcher:id/line3 = 0x7f09010a
org.levimc.launcher:style/Widget.AppCompat.Spinner = 0x7f120335
org.levimc.launcher:id/line1 = 0x7f090109
org.levimc.launcher:id/legacy = 0x7f090108
org.levimc.launcher:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1200f7
org.levimc.launcher:id/leftToRight = 0x7f090107
org.levimc.launcher:id/layout = 0x7f090105
org.levimc.launcher:id/launch_button = 0x7f090104
org.levimc.launcher:id/language_button = 0x7f090103
org.levimc.launcher:id/jumpToStart = 0x7f090101
org.levimc.launcher:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f120241
org.levimc.launcher:id/jumpToEnd = 0x7f090100
org.levimc.launcher:macro/m3_comp_search_bar_input_text_type = 0x7f0d00ea
org.levimc.launcher:id/italic = 0x7f0900fe
org.levimc.launcher:id/info = 0x7f0900fb
org.levimc.launcher:style/Platform.MaterialComponents.Dialog = 0x7f120144
org.levimc.launcher:id/indeterminate = 0x7f0900fa
org.levimc.launcher:id/import_apk_button = 0x7f0900f8
org.levimc.launcher:id/immediateStop = 0x7f0900f7
org.levimc.launcher:id/imgLeaf = 0x7f0900f6
org.levimc.launcher:integer/cancel_button_image_alpha = 0x7f0a0004
org.levimc.launcher:id/image = 0x7f0900f5
org.levimc.launcher:id/ignoreRequest = 0x7f0900f4
org.levimc.launcher:id/ifRoom = 0x7f0900f2
org.levimc.launcher:integer/mtrl_card_anim_delay_ms = 0x7f0a0034
org.levimc.launcher:id/horizontal_only = 0x7f0900ef
org.levimc.launcher:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f120117
org.levimc.launcher:id/home = 0x7f0900eb
org.levimc.launcher:style/ThemeOverlay.AppCompat.DayNight = 0x7f120286
org.levimc.launcher:id/hideable = 0x7f0900ea
org.levimc.launcher:id/header = 0x7f0900e7
org.levimc.launcher:string/bottomsheet_drag_handle_clicked = 0x7f110025
org.levimc.launcher:id/guideline_vertical = 0x7f0900e6
org.levimc.launcher:id/group_divider = 0x7f0900e3
org.levimc.launcher:id/graph = 0x7f0900e1
org.levimc.launcher:id/gone = 0x7f0900e0
org.levimc.launcher:id/ghost_view_holder = 0x7f0900de
org.levimc.launcher:id/ghost_view = 0x7f0900dd
org.levimc.launcher:id/fullscreen_header = 0x7f0900dc
org.levimc.launcher:id/fragment_container_view_tag = 0x7f0900da
org.levimc.launcher:id/forever = 0x7f0900d9
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f12025a
org.levimc.launcher:id/floating = 0x7f0900d8
org.levimc.launcher:id/flip = 0x7f0900d7
org.levimc.launcher:id/fixed = 0x7f0900d6
org.levimc.launcher:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0d001e
org.levimc.launcher:id/fitStart = 0x7f0900d3
org.levimc.launcher:id/fitCenter = 0x7f0900d1
org.levimc.launcher:id/filled = 0x7f0900d0
org.levimc.launcher:id/fill = 0x7f0900cd
org.levimc.launcher:id/exitUntilCollapsed = 0x7f0900c9
org.levimc.launcher:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0d0131
org.levimc.launcher:id/escape = 0x7f0900c8
org.levimc.launcher:id/enterAlways = 0x7f0900c6
org.levimc.launcher:id/endToStart = 0x7f0900c5
org.levimc.launcher:style/Theme.AppCompat.Light = 0x7f120224
org.levimc.launcher:id/end = 0x7f0900c4
org.levimc.launcher:string/storage_permission_message = 0x7f1100fa
org.levimc.launcher:id/settings_button = 0x7f0901b1
org.levimc.launcher:id/elastic = 0x7f0900c2
org.levimc.launcher:id/edit_version_name = 0x7f0900c1
org.levimc.launcher:id/edit_text_id = 0x7f0900bf
org.levimc.launcher:id/easeInOut = 0x7f0900ba
org.levimc.launcher:id/drag_handle = 0x7f0900b7
org.levimc.launcher:id/dragUp = 0x7f0900b6
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1201cb
org.levimc.launcher:id/dragRight = 0x7f0900b4
org.levimc.launcher:id/dragEnd = 0x7f0900b2
org.levimc.launcher:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f120111
org.levimc.launcher:id/dragDown = 0x7f0900b1
org.levimc.launcher:id/dragAnticlockwise = 0x7f0900af
org.levimc.launcher:id/disjoint = 0x7f0900ae
org.levimc.launcher:id/disableScroll = 0x7f0900ad
org.levimc.launcher:id/disablePostScroll = 0x7f0900ac
org.levimc.launcher:id/disableIntraAutoTransition = 0x7f0900ab
org.levimc.launcher:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f120089
org.levimc.launcher:id/disableHome = 0x7f0900aa
org.levimc.launcher:id/dimensions = 0x7f0900a8
org.levimc.launcher:id/dialog_button = 0x7f0900a7
org.levimc.launcher:id/design_navigation_view = 0x7f0900a6
org.levimc.launcher:id/design_menu_item_action_area_stub = 0x7f0900a4
org.levimc.launcher:id/design_bottom_sheet = 0x7f0900a2
org.levimc.launcher:id/dependency_ordering = 0x7f0900a1
org.levimc.launcher:id/deltaRelative = 0x7f0900a0
org.levimc.launcher:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0d016b
org.levimc.launcher:id/delete_version_button = 0x7f09009f
org.levimc.launcher:id/default_activity_button = 0x7f09009e
org.levimc.launcher:id/decor_content_parent = 0x7f09009d
org.levimc.launcher:id/decelerate = 0x7f09009b
org.levimc.launcher:style/Widget.Design.CollapsingToolbar = 0x7f120342
org.levimc.launcher:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0a003f
org.levimc.launcher:id/date_picker_actions = 0x7f09009a
org.levimc.launcher:id/custom = 0x7f090097
org.levimc.launcher:id/navigation_bar_item_small_label_view = 0x7f090157
org.levimc.launcher:id/cradle = 0x7f090095
org.levimc.launcher:id/counterclockwise = 0x7f090094
org.levimc.launcher:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0d0061
org.levimc.launcher:id/content = 0x7f09008e
org.levimc.launcher:id/container = 0x7f09008d
org.levimc.launcher:id/compress = 0x7f09008a
org.levimc.launcher:id/collapseActionView = 0x7f090089
org.levimc.launcher:id/closest = 0x7f090088
org.levimc.launcher:id/clip_vertical = 0x7f090086
org.levimc.launcher:id/clear_text = 0x7f090084
org.levimc.launcher:style/Base.Widget.AppCompat.TextView = 0x7f1200ff
org.levimc.launcher:id/chains = 0x7f09007f
org.levimc.launcher:id/center_vertical = 0x7f09007c
org.levimc.launcher:id/centerInside = 0x7f09007a
org.levimc.launcher:id/center = 0x7f090078
org.levimc.launcher:id/buttonPanel = 0x7f090073
org.levimc.launcher:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f12046f
org.levimc.launcher:id/btn_spacing_neu_pos = 0x7f090072
org.levimc.launcher:id/btn_spacing_neg_neu = 0x7f090071
org.levimc.launcher:id/btn_neutral = 0x7f09006f
org.levimc.launcher:id/btn_negative = 0x7f09006e
org.levimc.launcher:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1200d2
org.levimc.launcher:id/btn_install = 0x7f09006d
org.levimc.launcher:id/btn_cancel = 0x7f09006c
org.levimc.launcher:id/below = 0x7f090063
org.levimc.launcher:id/on = 0x7f090165
org.levimc.launcher:id/barrier = 0x7f09005f
org.levimc.launcher:id/axisRelative = 0x7f09005e
org.levimc.launcher:id/autoCompleteToStart = 0x7f09005d
org.levimc.launcher:id/autoCompleteToEnd = 0x7f09005c
org.levimc.launcher:id/autoComplete = 0x7f09005b
org.levimc.launcher:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f120421
org.levimc.launcher:id/auto = 0x7f09005a
org.levimc.launcher:id/async = 0x7f090059
org.levimc.launcher:id/anticipate = 0x7f090056
org.levimc.launcher:id/antiClockwise = 0x7f090055
org.levimc.launcher:id/animateToEnd = 0x7f090053
org.levimc.launcher:id/allStates = 0x7f090051
org.levimc.launcher:id/aligned = 0x7f09004f
org.levimc.launcher:id/alertTitle = 0x7f09004e
org.levimc.launcher:id/spinner_value = 0x7f0901c6
org.levimc.launcher:id/add_mod_button = 0x7f09004d
org.levimc.launcher:id/add = 0x7f09004c
org.levimc.launcher:id/activity_chooser_view_content = 0x7f09004b
org.levimc.launcher:id/actions = 0x7f09004a
org.levimc.launcher:id/action_text = 0x7f090049
org.levimc.launcher:id/homeAsUp = 0x7f0900ec
org.levimc.launcher:id/action_mode_close_button = 0x7f090047
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f120139
org.levimc.launcher:id/action_mode_bar_stub = 0x7f090046
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f1202e9
org.levimc.launcher:id/action_menu_divider = 0x7f090043
org.levimc.launcher:id/action_image = 0x7f090042
org.levimc.launcher:id/action_divider = 0x7f090040
org.levimc.launcher:id/action_context_bar = 0x7f09003f
org.levimc.launcher:style/Widget.AppCompat.ProgressBar = 0x7f12032c
org.levimc.launcher:id/action_container = 0x7f09003e
org.levimc.launcher:style/Widget.MaterialComponents.ProgressIndicator = 0x7f12044e
org.levimc.launcher:id/action_chinese = 0x7f09003d
org.levimc.launcher:id/action_bar_title = 0x7f09003c
org.levimc.launcher:id/parent_matrix = 0x7f09017d
org.levimc.launcher:id/action_bar_subtitle = 0x7f09003b
org.levimc.launcher:id/bounceStart = 0x7f09006a
org.levimc.launcher:id/action_bar_root = 0x7f090039
org.levimc.launcher:id/action_bar_container = 0x7f090038
org.levimc.launcher:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0d0016
org.levimc.launcher:id/action_menu_presenter = 0x7f090044
org.levimc.launcher:id/action_bar_activity_content = 0x7f090037
org.levimc.launcher:style/Base.Widget.AppCompat.ActionButton = 0x7f1200ce
org.levimc.launcher:id/action_bar = 0x7f090036
org.levimc.launcher:id/actionDownUp = 0x7f090034
org.levimc.launcher:id/centerCrop = 0x7f090079
org.levimc.launcher:id/actionDown = 0x7f090033
org.levimc.launcher:id/callMeasure = 0x7f090075
org.levimc.launcher:id/accessibility_custom_action_9 = 0x7f090032
org.levimc.launcher:id/accessibility_custom_action_6 = 0x7f09002f
org.levimc.launcher:style/Widget.AppCompat.Light.ActionButton = 0x7f120317
org.levimc.launcher:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0d0056
org.levimc.launcher:id/accessibility_custom_action_5 = 0x7f09002e
org.levimc.launcher:macro/m3_comp_filled_button_label_text_color = 0x7f0d0044
org.levimc.launcher:id/accessibility_custom_action_30 = 0x7f09002b
org.levimc.launcher:id/accessibility_custom_action_3 = 0x7f09002a
org.levimc.launcher:id/accessibility_custom_action_27 = 0x7f090027
org.levimc.launcher:id/accessibility_custom_action_25 = 0x7f090025
org.levimc.launcher:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f12037f
org.levimc.launcher:id/textinput_counter = 0x7f0901f8
org.levimc.launcher:id/accessibility_custom_action_22 = 0x7f090022
org.levimc.launcher:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1202be
org.levimc.launcher:id/accessibility_custom_action_21 = 0x7f090021
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar = 0x7f12030e
org.levimc.launcher:id/accessibility_custom_action_20 = 0x7f090020
org.levimc.launcher:id/accessibility_custom_action_2 = 0x7f09001f
org.levimc.launcher:id/accessibility_custom_action_17 = 0x7f09001c
org.levimc.launcher:id/accessibility_custom_action_13 = 0x7f090018
org.levimc.launcher:id/accessibility_custom_action_24 = 0x7f090024
org.levimc.launcher:id/accessibility_custom_action_12 = 0x7f090017
org.levimc.launcher:id/accessibility_custom_action_11 = 0x7f090016
org.levimc.launcher:id/accessibility_custom_action_10 = 0x7f090015
org.levimc.launcher:id/accessibility_custom_action_1 = 0x7f090014
org.levimc.launcher:id/accessibility_action_clickable_span = 0x7f090012
org.levimc.launcher:id/accelerate = 0x7f090011
org.levimc.launcher:id/above = 0x7f090010
org.levimc.launcher:id/abi_label = 0x7f09000e
org.levimc.launcher:id/currentState = 0x7f090096
org.levimc.launcher:id/TOP_START = 0x7f09000d
org.levimc.launcher:id/TOP_END = 0x7f09000c
org.levimc.launcher:id/SYM = 0x7f09000b
org.levimc.launcher:id/SHOW_PATH = 0x7f090009
org.levimc.launcher:id/SHIFT = 0x7f090007
org.levimc.launcher:id/NO_DEBUG = 0x7f090006
org.levimc.launcher:styleable/MaterialCardView = 0x7f130054
org.levimc.launcher:id/META = 0x7f090005
org.levimc.launcher:id/FUNCTION = 0x7f090004
org.levimc.launcher:id/CTRL = 0x7f090003
org.levimc.launcher:drawable/bg_round_gradient = 0x7f070080
org.levimc.launcher:id/ALT = 0x7f090000
org.levimc.launcher:attr/windowSplashScreenIconBackgroundColor = 0x7f0304ee
org.levimc.launcher:font/misans = 0x7f080000
org.levimc.launcher:drawable/tooltip_frame_dark = 0x7f0700fc
org.levimc.launcher:drawable/notification_tile_bg = 0x7f0700f9
org.levimc.launcher:drawable/notification_icon_background = 0x7f0700f5
org.levimc.launcher:string/side_sheet_behavior = 0x7f1100f7
org.levimc.launcher:drawable/notification_bg_normal = 0x7f0700f3
org.levimc.launcher:drawable/notification_bg_low_pressed = 0x7f0700f2
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0d006e
org.levimc.launcher:drawable/notification_bg = 0x7f0700ef
org.levimc.launcher:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f120422
org.levimc.launcher:layout/splash_screen_view = 0x7f0c007b
org.levimc.launcher:drawable/mtrl_switch_thumb_unchecked = 0x7f0700e7
org.levimc.launcher:attr/chipCornerRadius = 0x7f0300bf
org.levimc.launcher:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700e6
org.levimc.launcher:drawable/mtrl_switch_thumb_pressed = 0x7f0700e4
org.levimc.launcher:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0d00db
org.levimc.launcher:attr/colorControlHighlight = 0x7f0300f8
org.levimc.launcher:drawable/mtrl_switch_thumb_checked = 0x7f0700e1
org.levimc.launcher:drawable/mtrl_popupmenu_background = 0x7f0700de
org.levimc.launcher:attr/prefixText = 0x7f03038d
org.levimc.launcher:drawable/mtrl_ic_checkbox_checked = 0x7f0700d9
org.levimc.launcher:style/Widget.AppCompat.TextView = 0x7f120339
org.levimc.launcher:drawable/mtrl_ic_cancel = 0x7f0700d7
org.levimc.launcher:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f120177
org.levimc.launcher:drawable/mtrl_ic_arrow_drop_up = 0x7f0700d6
org.levimc.launcher:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001e
org.levimc.launcher:drawable/mtrl_ic_arrow_drop_down = 0x7f0700d5
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f050188
org.levimc.launcher:drawable/mtrl_dialog_background = 0x7f0700d3
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500c6
org.levimc.launcher:color/m3_simple_item_ripple_color = 0x7f050151
org.levimc.launcher:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700d1
org.levimc.launcher:attr/iconPadding = 0x7f030233
org.levimc.launcher:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700e5
org.levimc.launcher:attr/tickVisible = 0x7f030494
org.levimc.launcher:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700cd
org.levimc.launcher:drawable/mtrl_checkbox_button_icon = 0x7f0700cb
org.levimc.launcher:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1202ae
org.levimc.launcher:drawable/mtrl_checkbox_button = 0x7f0700c9
org.levimc.launcher:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700c8
org.levimc.launcher:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700c7
org.levimc.launcher:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700c6
org.levimc.launcher:color/m3_radiobutton_ripple_tint = 0x7f05009d
org.levimc.launcher:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700c3
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f120043
org.levimc.launcher:drawable/m3_tabs_transparent_background = 0x7f0700bd
org.levimc.launcher:drawable/m3_tabs_background = 0x7f0700ba
org.levimc.launcher:macro/m3_comp_assist_chip_container_shape = 0x7f0d0000
org.levimc.launcher:drawable/m3_selection_control_ripple = 0x7f0700b9
org.levimc.launcher:drawable/abc_list_focused_holo = 0x7f07004e
org.levimc.launcher:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001d
org.levimc.launcher:drawable/m3_bottom_sheet_drag_handle = 0x7f0700b5
org.levimc.launcher:drawable/icon_background = 0x7f0700b1
org.levimc.launcher:id/tvAppName = 0x7f090211
org.levimc.launcher:drawable/ic_settings = 0x7f0700b0
org.levimc.launcher:drawable/ic_mtrl_chip_checked_circle = 0x7f0700ad
org.levimc.launcher:dimen/mtrl_calendar_day_today_stroke = 0x7f060279
org.levimc.launcher:drawable/ic_m3_chip_close = 0x7f0700a9
org.levimc.launcher:string/repair_libs_dialog_title = 0x7f1100e7
org.levimc.launcher:drawable/ic_game_icon = 0x7f0700a1
org.levimc.launcher:drawable/ic_drag_handle = 0x7f0700a0
org.levimc.launcher:drawable/ic_delete = 0x7f07009f
org.levimc.launcher:attr/state_collapsed = 0x7f0303f9
org.levimc.launcher:drawable/ic_check = 0x7f07009c
org.levimc.launcher:id/cut = 0x7f090099
org.levimc.launcher:attr/crossfade = 0x7f03015d
org.levimc.launcher:color/material_dynamic_primary80 = 0x7f050246
org.levimc.launcher:drawable/ic_call_decline_low = 0x7f07009b
org.levimc.launcher:drawable/ic_call_answer_video = 0x7f070098
org.levimc.launcher:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0d0133
org.levimc.launcher:color/m3_sys_color_dark_primary_container = 0x7f05016f
org.levimc.launcher:drawable/ic_call_answer_low = 0x7f070097
org.levimc.launcher:drawable/ic_arrow_down = 0x7f070095
org.levimc.launcher:attr/textInputFilledStyle = 0x7f03046b
org.levimc.launcher:dimen/material_clock_face_margin_bottom = 0x7f060225
org.levimc.launcher:drawable/design_ic_visibility_off = 0x7f070090
org.levimc.launcher:id/actionUp = 0x7f090035
org.levimc.launcher:drawable/btn_radio_off_mtrl = 0x7f070087
org.levimc.launcher:integer/mtrl_chip_anim_duration = 0x7f0a0036
org.levimc.launcher:drawable/btn_checkbox_checked_mtrl = 0x7f070083
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1202d3
org.levimc.launcher:color/material_dynamic_neutral_variant99 = 0x7f05023c
org.levimc.launcher:drawable/bg_item_rounded = 0x7f07007f
org.levimc.launcher:drawable/bg_abi_x86_64 = 0x7f07007e
org.levimc.launcher:drawable/bg_abi_arm64_v8a = 0x7f07007a
org.levimc.launcher:drawable/background_gradient = 0x7f070079
org.levimc.launcher:drawable/avd_hide_password = 0x7f070077
org.levimc.launcher:styleable/CircularProgressIndicator = 0x7f13001f
org.levimc.launcher:color/material_dynamic_neutral40 = 0x7f050228
org.levimc.launcher:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070073
org.levimc.launcher:string/version_prefix = 0x7f11010d
org.levimc.launcher:drawable/abc_textfield_default_mtrl_alpha = 0x7f070072
org.levimc.launcher:drawable/abc_text_select_handle_left_mtrl = 0x7f07006e
org.levimc.launcher:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006c
org.levimc.launcher:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f120452
org.levimc.launcher:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f120124
org.levimc.launcher:drawable/abc_switch_track_mtrl_alpha = 0x7f07006a
org.levimc.launcher:drawable/abc_star_half_black_48dp = 0x7f070068
org.levimc.launcher:string/m3_sys_motion_easing_standard = 0x7f110075
org.levimc.launcher:color/m3_dynamic_default_color_primary_text = 0x7f050082
org.levimc.launcher:drawable/abc_seekbar_track_material = 0x7f070064
org.levimc.launcher:string/abc_menu_meta_shortcut_label = 0x7f11000d
org.levimc.launcher:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0d00ac
org.levimc.launcher:drawable/abc_seekbar_tick_mark_material = 0x7f070063
org.levimc.launcher:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005e
org.levimc.launcher:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005d
org.levimc.launcher:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f120172
org.levimc.launcher:drawable/abc_popup_background_mtrl_mult = 0x7f070059
org.levimc.launcher:drawable/abc_list_selector_disabled_holo_light = 0x7f070055
org.levimc.launcher:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060172
org.levimc.launcher:drawable/abc_list_selector_disabled_holo_dark = 0x7f070054
org.levimc.launcher:id/startToEnd = 0x7f0901d4
org.levimc.launcher:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f06024c
org.levimc.launcher:drawable/abc_list_selector_background_transition_holo_light = 0x7f070053
org.levimc.launcher:drawable/abc_item_background_holo_dark = 0x7f07004a
org.levimc.launcher:drawable/abc_ic_voice_search_api_material = 0x7f070049
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary30 = 0x7f0500e0
org.levimc.launcher:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070045
org.levimc.launcher:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501c2
org.levimc.launcher:dimen/abc_action_button_min_width_material = 0x7f06000e
org.levimc.launcher:drawable/abc_ic_menu_overflow_material = 0x7f070044
org.levimc.launcher:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070043
org.levimc.launcher:drawable/abc_ic_clear_material = 0x7f07003f
org.levimc.launcher:drawable/abc_ic_ab_back_material = 0x7f07003d
org.levimc.launcher:drawable/abc_edit_text_material = 0x7f07003c
org.levimc.launcher:attr/colorSurfaceContainerHigh = 0x7f030123
org.levimc.launcher:drawable/abc_cab_background_internal_bg = 0x7f070037
org.levimc.launcher:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0602bd
org.levimc.launcher:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070034
org.levimc.launcher:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0502da
org.levimc.launcher:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002d
org.levimc.launcher:drawable/ic_add = 0x7f070093
org.levimc.launcher:drawable/abc_btn_check_material_anim = 0x7f07002c
org.levimc.launcher:drawable/abc_action_bar_item_background_material = 0x7f070029
org.levimc.launcher:attr/dividerInsetEnd = 0x7f03017f
org.levimc.launcher:color/m3_ref_palette_secondary100 = 0x7f050137
org.levimc.launcher:attr/keyboardIcon = 0x7f03026d
org.levimc.launcher:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0602b5
org.levimc.launcher:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070027
org.levimc.launcher:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070026
org.levimc.launcher:drawable/abc_vector_test = 0x7f070076
org.levimc.launcher:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070025
org.levimc.launcher:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070020
org.levimc.launcher:string/material_timepicker_text_input_mode_description = 0x7f11008d
org.levimc.launcher:id/spread_inside = 0x7f0901cb
org.levimc.launcher:drawable/notify_panel_notification_icon_bg = 0x7f0700fa
org.levimc.launcher:styleable/PopupWindowBackgroundState = 0x7f130071
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001c
org.levimc.launcher:id/honorRequest = 0x7f0900ed
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001a
org.levimc.launcher:id/edit_value = 0x7f0900c0
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070016
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070011
org.levimc.launcher:string/path_password_eye_mask_strike_through = 0x7f1100e0
org.levimc.launcher:id/open_search_view_root = 0x7f09016f
org.levimc.launcher:drawable/$m3_avd_hide_password__2 = 0x7f070008
org.levimc.launcher:styleable/FontFamily = 0x7f130036
org.levimc.launcher:drawable/$avd_show_password__2 = 0x7f070005
org.levimc.launcher:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f12032a
org.levimc.launcher:attr/textureHeight = 0x7f030479
org.levimc.launcher:drawable/$avd_hide_password__0 = 0x7f070000
org.levimc.launcher:dimen/tooltip_y_offset_touch = 0x7f060329
org.levimc.launcher:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1202b9
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f060202
org.levimc.launcher:dimen/tooltip_vertical_padding = 0x7f060327
org.levimc.launcher:dimen/tooltip_margin = 0x7f060324
org.levimc.launcher:id/progress_text = 0x7f09018b
org.levimc.launcher:drawable/ic_github = 0x7f0700a2
org.levimc.launcher:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f060177
org.levimc.launcher:dimen/tooltip_horizontal_padding = 0x7f060323
org.levimc.launcher:dimen/splashscreen_icon_size = 0x7f06031f
org.levimc.launcher:style/Widget.AppCompat.ButtonBar = 0x7f120305
org.levimc.launcher:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f06031d
org.levimc.launcher:attr/moveWhenScrollAtTop = 0x7f030353
org.levimc.launcher:dimen/splashscreen_icon_mask_size_with_background = 0x7f06031c
org.levimc.launcher:dimen/splashscreen_icon_mask_size_no_background = 0x7f06031b
org.levimc.launcher:color/material_personalized_color_tertiary = 0x7f0502a2
org.levimc.launcher:dimen/m3_bottom_nav_item_padding_top = 0x7f0600c0
org.levimc.launcher:dimen/notification_subtext_size = 0x7f060318
org.levimc.launcher:dimen/notification_right_side_padding_top = 0x7f060315
org.levimc.launcher:dimen/notification_media_narrow_margin = 0x7f060313
org.levimc.launcher:attr/itemBackground = 0x7f03024f
org.levimc.launcher:dimen/notification_main_column_padding_top = 0x7f060312
org.levimc.launcher:attr/customBoolean = 0x7f030162
org.levimc.launcher:dimen/notification_large_icon_height = 0x7f060310
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1202ec
org.levimc.launcher:attr/textAppearanceTitleMedium = 0x7f03045e
org.levimc.launcher:dimen/disabled_alpha_material_light = 0x7f06008f
org.levimc.launcher:dimen/mtrl_tooltip_cornerSize = 0x7f060307
org.levimc.launcher:dimen/abc_text_size_subhead_material = 0x7f06004d
org.levimc.launcher:dimen/mtrl_textinput_start_icon_margin_end = 0x7f060304
org.levimc.launcher:style/TextAppearance.AppCompat.Title = 0x7f1201b8
org.levimc.launcher:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f060303
org.levimc.launcher:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f12038a
org.levimc.launcher:layout/design_text_input_end_icon = 0x7f0c002d
org.levimc.launcher:color/design_dark_default_color_secondary_variant = 0x7f05003d
org.levimc.launcher:dimen/mtrl_textinput_end_icon_margin_start = 0x7f060302
org.levimc.launcher:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f050097
org.levimc.launcher:dimen/mtrl_switch_track_width = 0x7f0602fb
org.levimc.launcher:id/textSpacerNoTitle = 0x7f0901f0
org.levimc.launcher:dimen/mtrl_switch_track_height = 0x7f0602fa
org.levimc.launcher:dimen/mtrl_switch_thumb_elevation = 0x7f0602f7
org.levimc.launcher:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0602f4
org.levimc.launcher:dimen/mtrl_snackbar_margin = 0x7f0602f3
org.levimc.launcher:color/mtrl_scrim_color = 0x7f0502df
org.levimc.launcher:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0602f2
org.levimc.launcher:dimen/mtrl_snackbar_background_corner_radius = 0x7f0602f1
org.levimc.launcher:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0602f0
org.levimc.launcher:dimen/mtrl_slider_track_height = 0x7f0602ed
org.levimc.launcher:dimen/mtrl_slider_tick_radius = 0x7f0602ec
org.levimc.launcher:dimen/mtrl_slider_halo_radius = 0x7f0602e5
org.levimc.launcher:id/btn_positive = 0x7f090070
org.levimc.launcher:dimen/design_textinput_caption_translate_y = 0x7f06008d
org.levimc.launcher:dimen/mtrl_shape_corner_size_small_component = 0x7f0602e4
org.levimc.launcher:dimen/mtrl_progress_track_thickness = 0x7f0602e1
org.levimc.launcher:dimen/mtrl_progress_circular_size_medium = 0x7f0602db
org.levimc.launcher:attr/layout_constraintBaseline_toBottomOf = 0x7f030283
org.levimc.launcher:dimen/mtrl_progress_circular_size_extra_small = 0x7f0602da
org.levimc.launcher:dimen/mtrl_progress_circular_radius = 0x7f0602d8
org.levimc.launcher:styleable/Chip = 0x7f13001d
org.levimc.launcher:id/mtrl_picker_header_title_and_selection = 0x7f09014a
org.levimc.launcher:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0602d5
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1202e4
org.levimc.launcher:dimen/mtrl_navigation_rail_icon_size = 0x7f0602d0
org.levimc.launcher:dimen/mtrl_navigation_rail_default_width = 0x7f0602cd
org.levimc.launcher:attr/animateRelativeTo = 0x7f030034
org.levimc.launcher:dimen/mtrl_slider_thumb_radius = 0x7f0602ea
org.levimc.launcher:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0602c9
org.levimc.launcher:macro/m3_comp_time_picker_headline_color = 0x7f0d0150
org.levimc.launcher:dimen/mtrl_textinput_counter_margin_start = 0x7f060301
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0d008e
org.levimc.launcher:dimen/mtrl_navigation_item_icon_size = 0x7f0602c8
org.levimc.launcher:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0d005f
org.levimc.launcher:dimen/design_snackbar_elevation = 0x7f060081
org.levimc.launcher:dimen/mtrl_navigation_item_icon_padding = 0x7f0602c7
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0500ef
org.levimc.launcher:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f060149
org.levimc.launcher:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0602c3
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1202dd
org.levimc.launcher:dimen/mtrl_min_touch_target_size = 0x7f0602c2
org.levimc.launcher:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0d0126
org.levimc.launcher:attr/navigationMode = 0x7f030358
org.levimc.launcher:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0602c1
org.levimc.launcher:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0602c0
org.levimc.launcher:style/Theme.AppCompat.Light.NoActionBar = 0x7f12022a
org.levimc.launcher:dimen/design_snackbar_padding_vertical_2lines = 0x7f060087
org.levimc.launcher:dimen/mtrl_low_ripple_default_alpha = 0x7f0602be
org.levimc.launcher:attr/motionEffect_end = 0x7f030344
org.levimc.launcher:dimen/mtrl_navigation_elevation = 0x7f0602c5
org.levimc.launcher:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0602bc
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f120398
org.levimc.launcher:string/import_confirmation_title = 0x7f11005a
org.levimc.launcher:dimen/mtrl_high_ripple_focused_alpha = 0x7f0602bb
org.levimc.launcher:dimen/mtrl_high_ripple_default_alpha = 0x7f0602ba
org.levimc.launcher:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
org.levimc.launcher:drawable/ic_m3_chip_checked_circle = 0x7f0700a8
org.levimc.launcher:dimen/material_clock_size = 0x7f06022f
org.levimc.launcher:color/m3_ref_palette_neutral17 = 0x7f050107
org.levimc.launcher:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0602b8
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001b
org.levimc.launcher:dimen/mtrl_fab_min_touch_target = 0x7f0602b7
org.levimc.launcher:attr/upDuration = 0x7f0304cc
org.levimc.launcher:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f060239
org.levimc.launcher:dimen/mtrl_extended_fab_min_width = 0x7f0602af
org.levimc.launcher:attr/titleMarginTop = 0x7f0304a0
org.levimc.launcher:dimen/mtrl_extended_fab_min_height = 0x7f0602ae
org.levimc.launcher:dimen/mtrl_extended_fab_icon_size = 0x7f0602ac
org.levimc.launcher:attr/layout_scrollFlags = 0x7f0302bc
org.levimc.launcher:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0602ab
org.levimc.launcher:attr/boxCornerRadiusBottomStart = 0x7f030082
org.levimc.launcher:dimen/mtrl_extended_fab_end_padding = 0x7f0602aa
org.levimc.launcher:attr/animateNavigationIcon = 0x7f030033
org.levimc.launcher:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0602a5
org.levimc.launcher:attr/layout_constraintHeight_default = 0x7f030292
org.levimc.launcher:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0602a4
org.levimc.launcher:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0602a3
org.levimc.launcher:dimen/mtrl_card_spacing = 0x7f0602a0
org.levimc.launcher:attr/horizontalOffset = 0x7f03022d
org.levimc.launcher:attr/tabSecondaryStyle = 0x7f03042f
org.levimc.launcher:drawable/abc_textfield_search_material = 0x7f070075
org.levimc.launcher:style/TextAppearance.Design.Counter.Overflow = 0x7f1201d5
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f07000f
org.levimc.launcher:attr/flow_horizontalBias = 0x7f0301ed
org.levimc.launcher:color/m3_ref_palette_tertiary10 = 0x7f050143
org.levimc.launcher:dimen/mtrl_card_corner_radius = 0x7f06029d
org.levimc.launcher:id/view_offset_helper = 0x7f09021e
org.levimc.launcher:dimen/mtrl_card_checked_icon_size = 0x7f06029c
org.levimc.launcher:dimen/mtrl_card_checked_icon_margin = 0x7f06029b
org.levimc.launcher:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0d0147
org.levimc.launcher:dimen/mtrl_calendar_year_vertical_padding = 0x7f060299
org.levimc.launcher:dimen/mtrl_calendar_year_horizontal_padding = 0x7f060298
org.levimc.launcher:id/open_search_view_scrim = 0x7f090170
org.levimc.launcher:dimen/mtrl_calendar_year_corner = 0x7f060296
org.levimc.launcher:attr/layoutDuringTransition = 0x7f030278
org.levimc.launcher:color/m3_timepicker_display_ripple_color = 0x7f05020e
org.levimc.launcher:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f060295
org.levimc.launcher:dimen/mtrl_calendar_title_baseline_to_top = 0x7f060294
org.levimc.launcher:integer/mtrl_btn_anim_delay_ms = 0x7f0a002f
org.levimc.launcher:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f060292
org.levimc.launcher:layout/abc_activity_chooser_view = 0x7f0c0006
org.levimc.launcher:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060290
org.levimc.launcher:string/abc_shareactionprovider_share_with_application = 0x7f110019
org.levimc.launcher:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f06028f
org.levimc.launcher:style/ThemeOverlay.Material3.Snackbar = 0x7f1202c3
org.levimc.launcher:dimen/m3_searchbar_padding_start = 0x7f0601df
org.levimc.launcher:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f06028e
org.levimc.launcher:dimen/mtrl_calendar_month_vertical_padding = 0x7f06028a
org.levimc.launcher:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f060285
org.levimc.launcher:dimen/mtrl_calendar_header_selection_line_height = 0x7f060283
org.levimc.launcher:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f120296
org.levimc.launcher:attr/materialTimePickerTitleStyle = 0x7f03030b
org.levimc.launcher:attr/textAppearanceBodySmall = 0x7f03043f
org.levimc.launcher:dimen/mtrl_calendar_dialog_background_inset = 0x7f06027d
org.levimc.launcher:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1200e1
org.levimc.launcher:attr/maxAcceleration = 0x7f03030c
org.levimc.launcher:attr/viewTransitionMode = 0x7f0304d4
org.levimc.launcher:color/m3_textfield_filled_background_color = 0x7f050204
org.levimc.launcher:dimen/mtrl_calendar_day_vertical_padding = 0x7f06027a
org.levimc.launcher:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0d001c
org.levimc.launcher:dimen/mtrl_calendar_day_corner = 0x7f060276
org.levimc.launcher:dimen/mtrl_calendar_bottom_padding = 0x7f060274
org.levimc.launcher:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003
org.levimc.launcher:dimen/mtrl_btn_z = 0x7f060270
org.levimc.launcher:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0d005a
org.levimc.launcher:dimen/mtrl_btn_text_size = 0x7f06026f
org.levimc.launcher:style/TextAppearance.AppCompat.Large = 0x7f1201a9
org.levimc.launcher:dimen/mtrl_btn_text_btn_padding_left = 0x7f06026d
org.levimc.launcher:dimen/mtrl_btn_text_btn_icon_padding = 0x7f06026c
org.levimc.launcher:drawable/ic_m3_chip_check = 0x7f0700a7
org.levimc.launcher:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0501a4
org.levimc.launcher:dimen/mtrl_btn_padding_right = 0x7f060267
org.levimc.launcher:string/m3_ref_typeface_brand_regular = 0x7f11006a
org.levimc.launcher:dimen/mtrl_btn_inset = 0x7f060262
org.levimc.launcher:dimen/mtrl_btn_icon_padding = 0x7f060261
org.levimc.launcher:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070028
org.levimc.launcher:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060260
org.levimc.launcher:dimen/mtrl_btn_hovered_z = 0x7f06025f
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f12013a
org.levimc.launcher:attr/editTextBackground = 0x7f030199
org.levimc.launcher:dimen/mtrl_btn_dialog_btn_min_width = 0x7f06025a
org.levimc.launcher:dimen/tooltip_corner_radius = 0x7f060322
org.levimc.launcher:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f060253
org.levimc.launcher:string/ignore_this_version = 0x7f110056
org.levimc.launcher:dimen/mtrl_badge_with_text_size = 0x7f060252
org.levimc.launcher:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060250
org.levimc.launcher:style/ThemeOverlay.Material3 = 0x7f12028c
org.levimc.launcher:dimen/mtrl_badge_text_size = 0x7f06024f
org.levimc.launcher:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f06024e
org.levimc.launcher:dimen/cardview_default_radius = 0x7f060054
org.levimc.launcher:attr/thumbTint = 0x7f030488
org.levimc.launcher:dimen/mtrl_badge_size = 0x7f06024d
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f12019d
org.levimc.launcher:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f06024a
org.levimc.launcher:dimen/mtrl_alert_dialog_background_inset_end = 0x7f060247
org.levimc.launcher:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0d00e4
org.levimc.launcher:dimen/material_textinput_min_width = 0x7f060243
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f120158
org.levimc.launcher:attr/backgroundInsetStart = 0x7f03004b
org.levimc.launcher:dimen/m3_searchview_divider_size = 0x7f0601e2
org.levimc.launcher:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060240
org.levimc.launcher:dimen/material_helper_text_font_1_3_padding_top = 0x7f06023f
org.levimc.launcher:dimen/material_helper_text_default_padding_top = 0x7f06023d
org.levimc.launcher:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f06023c
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Display3 = 0x7f120020
org.levimc.launcher:integer/m3_card_anim_delay_ms = 0x7f0a000d
org.levimc.launcher:dimen/material_emphasis_medium = 0x7f060236
org.levimc.launcher:attr/extraMultilineHeightEnabled = 0x7f0301c9
org.levimc.launcher:attr/actionModeCutDrawable = 0x7f030017
org.levimc.launcher:dimen/material_clock_period_toggle_vertical_gap = 0x7f06022d
org.levimc.launcher:integer/mtrl_switch_thumb_motion_duration = 0x7f0a0037
org.levimc.launcher:dimen/material_clock_period_toggle_horizontal_gap = 0x7f06022c
org.levimc.launcher:dimen/material_clock_period_toggle_height = 0x7f06022b
org.levimc.launcher:id/slide = 0x7f0901be
org.levimc.launcher:drawable/ic_clock_black_24dp = 0x7f07009e
org.levimc.launcher:styleable/CustomAttribute = 0x7f13002e
org.levimc.launcher:dimen/m3_toolbar_text_size_title = 0x7f060220
org.levimc.launcher:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0d0119
org.levimc.launcher:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f06013d
org.levimc.launcher:dimen/m3_timepicker_window_elevation = 0x7f06021f
org.levimc.launcher:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060126
org.levimc.launcher:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f06021d
org.levimc.launcher:style/Theme.AppCompat.Dialog.MinWidth = 0x7f120221
org.levimc.launcher:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f06021b
org.levimc.launcher:id/month_navigation_previous = 0x7f090137
org.levimc.launcher:color/primary_text_disabled_material_light = 0x7f050300
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f060219
org.levimc.launcher:layout/item_version_big_group = 0x7f0c003d
org.levimc.launcher:color/material_personalized_color_on_primary = 0x7f050284
org.levimc.launcher:drawable/material_cursor_drawable = 0x7f0700be
org.levimc.launcher:attr/collapsedTitleTextColor = 0x7f0300ec
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f060218
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f060215
org.levimc.launcher:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0d0065
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f060214
org.levimc.launcher:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700d2
org.levimc.launcher:attr/actionBarWidgetTheme = 0x7f03000c
org.levimc.launcher:attr/customDimension = 0x7f030165
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f060212
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f060211
org.levimc.launcher:color/secondary_text_disabled_material_light = 0x7f050307
org.levimc.launcher:color/material_personalized_color_background = 0x7f05027b
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060210
org.levimc.launcher:style/Base.Widget.AppCompat.SearchView = 0x7f1200f9
org.levimc.launcher:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0d00c4
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f06020f
org.levimc.launcher:styleable/AppCompatImageView = 0x7f13000e
org.levimc.launcher:color/m3_ref_palette_secondary99 = 0x7f050141
org.levimc.launcher:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f06020d
org.levimc.launcher:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f06020c
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f12039c
org.levimc.launcher:attr/alertDialogTheme = 0x7f03002c
org.levimc.launcher:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f06020b
org.levimc.launcher:attr/goIcon = 0x7f03020d
org.levimc.launcher:drawable/ic_mtrl_checked_circle = 0x7f0700ab
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f060207
org.levimc.launcher:dimen/mtrl_btn_corner_radius = 0x7f060259
org.levimc.launcher:attr/homeLayout = 0x7f03022c
org.levimc.launcher:attr/circularflow_viewCenter = 0x7f0300d8
org.levimc.launcher:attr/fastScrollHorizontalTrackDrawable = 0x7f0301d5
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f060206
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f060203
org.levimc.launcher:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f12011b
org.levimc.launcher:dimen/abc_control_inset_material = 0x7f060019
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f060201
org.levimc.launcher:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0601fc
org.levimc.launcher:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0301e4
org.levimc.launcher:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0601fb
org.levimc.launcher:string/material_hour_selection = 0x7f11007b
org.levimc.launcher:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0601fa
org.levimc.launcher:color/m3_ref_palette_dynamic_primary0 = 0x7f0500cf
org.levimc.launcher:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0601f8
org.levimc.launcher:string/m3_sys_motion_easing_legacy_accelerate = 0x7f110072
org.levimc.launcher:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0601f7
org.levimc.launcher:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0601f6
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f120263
org.levimc.launcher:drawable/$m3_avd_show_password__1 = 0x7f07000a
org.levimc.launcher:dimen/m3_sys_elevation_level4 = 0x7f0601f4
org.levimc.launcher:string/mtrl_picker_announce_current_selection = 0x7f1100a6
org.levimc.launcher:drawable/abc_ratingbar_material = 0x7f07005b
org.levimc.launcher:dimen/m3_sys_elevation_level3 = 0x7f0601f3
org.levimc.launcher:id/icon = 0x7f0900f0
org.levimc.launcher:dimen/m3_sys_elevation_level0 = 0x7f0601f0
org.levimc.launcher:dimen/m3_snackbar_margin = 0x7f0601ef
org.levimc.launcher:dimen/m3_snackbar_action_text_color_alpha = 0x7f0601ee
org.levimc.launcher:dimen/m3_small_fab_max_image_size = 0x7f0601ec
org.levimc.launcher:id/time = 0x7f0901fe
org.levimc.launcher:dimen/m3_slider_thumb_elevation = 0x7f0601eb
org.levimc.launcher:dimen/m3_simple_item_color_selected_alpha = 0x7f0601ea
org.levimc.launcher:dimen/m3_simple_item_color_hovered_alpha = 0x7f0601e9
org.levimc.launcher:dimen/m3_side_sheet_modal_elevation = 0x7f0601e6
org.levimc.launcher:dimen/mtrl_btn_pressed_z = 0x7f060269
org.levimc.launcher:attr/layout_constraintHeight = 0x7f030291
org.levimc.launcher:attr/drawerLayoutCornerSize = 0x7f030192
org.levimc.launcher:dimen/m3_searchview_elevation = 0x7f0601e3
org.levimc.launcher:attr/drawableLeftCompat = 0x7f03018a
org.levimc.launcher:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f06031e
org.levimc.launcher:string/abc_menu_alt_shortcut_label = 0x7f110008
org.levimc.launcher:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0d00bc
org.levimc.launcher:attr/layout_constraintWidth_min = 0x7f0302ab
org.levimc.launcher:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601e0
org.levimc.launcher:dimen/mtrl_fab_elevation = 0x7f0602b6
org.levimc.launcher:dimen/m3_searchbar_outlined_stroke_width = 0x7f0601de
org.levimc.launcher:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0602de
org.levimc.launcher:dimen/m3_searchbar_height = 0x7f0601db
org.levimc.launcher:color/m3_sys_color_dark_secondary_container = 0x7f050171
org.levimc.launcher:dimen/m3_ripple_hovered_alpha = 0x7f0601d7
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0d0086
org.levimc.launcher:attr/boxStrokeWidth = 0x7f030087
org.levimc.launcher:dimen/m3_ripple_focused_alpha = 0x7f0601d6
org.levimc.launcher:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601d3
org.levimc.launcher:style/Base.Widget.AppCompat.ProgressBar = 0x7f1200f4
org.levimc.launcher:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0601d0
org.levimc.launcher:drawable/mtrl_switch_thumb = 0x7f0700e0
org.levimc.launcher:dimen/m3_navigation_rail_icon_size = 0x7f0601cb
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0d0090
org.levimc.launcher:drawable/mtrl_popupmenu_background_overlay = 0x7f0700df
org.levimc.launcher:dimen/m3_navigation_rail_elevation = 0x7f0601ca
org.levimc.launcher:dimen/material_emphasis_disabled = 0x7f060233
org.levimc.launcher:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0601c8
org.levimc.launcher:layout/dialog_game_version_select = 0x7f0c0030
org.levimc.launcher:attr/endIconTintMode = 0x7f0301a9
org.levimc.launcher:dimen/m3_navigation_item_vertical_padding = 0x7f0601c6
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0d00fe
org.levimc.launcher:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f0600fe
org.levimc.launcher:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f06018e
org.levimc.launcher:dimen/m3_navigation_item_icon_padding = 0x7f0601c1
org.levimc.launcher:id/cache_measures = 0x7f090074
org.levimc.launcher:drawable/abc_switch_thumb_material = 0x7f070069
org.levimc.launcher:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f120165
org.levimc.launcher:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0601bb
org.levimc.launcher:dimen/m3_large_fab_max_image_size = 0x7f0601b9
org.levimc.launcher:styleable/PropertySet = 0x7f130072
org.levimc.launcher:dimen/m3_fab_translation_z_pressed = 0x7f0601b8
org.levimc.launcher:dimen/m3_extended_fab_top_padding = 0x7f0601b4
org.levimc.launcher:style/Base.Theme.AppCompat = 0x7f12004e
org.levimc.launcher:dimen/m3_extended_fab_min_height = 0x7f0601b2
org.levimc.launcher:dimen/m3_extended_fab_bottom_padding = 0x7f0601af
org.levimc.launcher:dimen/m3_divider_heavy_thickness = 0x7f0601ae
org.levimc.launcher:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0601ab
org.levimc.launcher:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0601aa
org.levimc.launcher:drawable/mtrl_ic_error = 0x7f0700db
org.levimc.launcher:dimen/mtrl_calendar_year_width = 0x7f06029a
org.levimc.launcher:id/left = 0x7f090106
org.levimc.launcher:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601a6
org.levimc.launcher:style/ThemeOverlay.Material3.Search = 0x7f1202c1
org.levimc.launcher:integer/m3_sys_motion_duration_medium2 = 0x7f0a0019
org.levimc.launcher:attr/sizePercent = 0x7f0303dd
org.levimc.launcher:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0601a5
org.levimc.launcher:layout/material_radial_view_group = 0x7f0c004b
org.levimc.launcher:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0601a4
org.levimc.launcher:color/material_slider_inactive_track_color = 0x7f0502b1
org.levimc.launcher:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0601a3
org.levimc.launcher:layout/mtrl_search_bar = 0x7f0c0070
org.levimc.launcher:attr/quantizeMotionSteps = 0x7f030396
org.levimc.launcher:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0601a2
org.levimc.launcher:attr/dividerHorizontal = 0x7f03017e
org.levimc.launcher:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0601a1
org.levimc.launcher:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f060199
org.levimc.launcher:dimen/m3_comp_switch_track_width = 0x7f060198
org.levimc.launcher:id/mtrl_anchor_parent = 0x7f09013a
org.levimc.launcher:dimen/m3_comp_switch_track_height = 0x7f060197
org.levimc.launcher:attr/thickness = 0x7f03047c
org.levimc.launcher:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060196
org.levimc.launcher:id/bottom = 0x7f090066
org.levimc.launcher:id/action_english = 0x7f090041
org.levimc.launcher:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f060195
org.levimc.launcher:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f060190
org.levimc.launcher:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f06018f
org.levimc.launcher:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1203fe
org.levimc.launcher:layout/ime_secondary_split_test_activity = 0x7f0c0036
org.levimc.launcher:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f06018c
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0501b7
org.levimc.launcher:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f06018b
org.levimc.launcher:dimen/m3_comp_snackbar_container_elevation = 0x7f060189
org.levimc.launcher:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f060184
org.levimc.launcher:dimen/m3_comp_sheet_side_docked_container_width = 0x7f06017e
org.levimc.launcher:macro/m3_comp_switch_selected_icon_color = 0x7f0d0129
org.levimc.launcher:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060176
org.levimc.launcher:dimen/m3_comp_search_view_docked_header_container_height = 0x7f060174
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f120315
org.levimc.launcher:attr/listPreferredItemHeightSmall = 0x7f0302d0
org.levimc.launcher:dimen/m3_comp_search_view_container_elevation = 0x7f060173
org.levimc.launcher:dimen/m3_comp_search_bar_container_elevation = 0x7f06016f
org.levimc.launcher:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501c5
org.levimc.launcher:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06016c
org.levimc.launcher:color/m3_text_button_foreground_color_selector = 0x7f050202
org.levimc.launcher:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f060169
org.levimc.launcher:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f060168
org.levimc.launcher:dimen/abc_control_corner_material = 0x7f060018
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f0601ff
org.levimc.launcher:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060165
org.levimc.launcher:id/ignore = 0x7f0900f3
org.levimc.launcher:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f06023e
org.levimc.launcher:drawable/m3_password_eye = 0x7f0700b6
org.levimc.launcher:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f06015f
org.levimc.launcher:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f06015e
org.levimc.launcher:color/material_dynamic_secondary70 = 0x7f050252
org.levimc.launcher:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f06015d
org.levimc.launcher:style/Widget.Material3.NavigationRailView = 0x7f1203c8
org.levimc.launcher:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06015c
org.levimc.launcher:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06015b
org.levimc.launcher:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06015a
org.levimc.launcher:style/Base.Theme.MaterialComponents.Bridge = 0x7f12006a
org.levimc.launcher:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f060158
org.levimc.launcher:dimen/m3_comp_navigation_drawer_container_width = 0x7f06013e
org.levimc.launcher:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f060157
org.levimc.launcher:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060156
org.levimc.launcher:id/continuousVelocity = 0x7f090091
org.levimc.launcher:dimen/m3_comp_outlined_card_outline_width = 0x7f060153
org.levimc.launcher:dimen/m3_comp_outlined_button_outline_width = 0x7f06014f
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f12002f
org.levimc.launcher:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06014c
org.levimc.launcher:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f120409
org.levimc.launcher:dimen/m3_comp_outlined_card_container_elevation = 0x7f060150
org.levimc.launcher:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f060145
org.levimc.launcher:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f060144
org.levimc.launcher:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f060142
org.levimc.launcher:styleable/ScrollingViewBehavior_Layout = 0x7f130078
org.levimc.launcher:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060140
org.levimc.launcher:style/Base.Animation.AppCompat.Tooltip = 0x7f120012
org.levimc.launcher:id/mtrl_picker_header_selection_text = 0x7f090149
org.levimc.launcher:id/direct = 0x7f0900a9
org.levimc.launcher:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06013b
org.levimc.launcher:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06013a
org.levimc.launcher:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f060137
org.levimc.launcher:attr/chipMinTouchTargetSize = 0x7f0300c8
org.levimc.launcher:dimen/m3_comp_menu_container_elevation = 0x7f060135
org.levimc.launcher:color/highlighted_text_material_light = 0x7f050060
org.levimc.launcher:color/mtrl_popupmenu_overlay_color = 0x7f0502de
org.levimc.launcher:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060134
org.levimc.launcher:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1200cc
org.levimc.launcher:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060133
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0d0168
org.levimc.launcher:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f06012f
org.levimc.launcher:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f120066
org.levimc.launcher:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f06012d
org.levimc.launcher:id/row_index_key = 0x7f090198
org.levimc.launcher:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06012a
org.levimc.launcher:attr/layout_collapseMode = 0x7f03027d
org.levimc.launcher:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0601c2
org.levimc.launcher:dimen/m3_comp_filled_card_icon_size = 0x7f060128
org.levimc.launcher:dimen/m3_comp_filled_button_container_elevation = 0x7f060122
org.levimc.launcher:color/m3_checkbox_button_tint = 0x7f050070
org.levimc.launcher:dimen/m3_comp_fab_primary_small_icon_size = 0x7f060120
org.levimc.launcher:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1202a2
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070017
org.levimc.launcher:color/dim_foreground_material_light = 0x7f050059
org.levimc.launcher:dimen/material_textinput_default_width = 0x7f060241
org.levimc.launcher:style/Theme.SplashScreen = 0x7f12027f
org.levimc.launcher:dimen/m3_comp_fab_primary_large_icon_size = 0x7f06011c
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f120026
org.levimc.launcher:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0d00e7
org.levimc.launcher:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f060119
org.levimc.launcher:macro/m3_comp_input_chip_label_text_type = 0x7f0d005c
org.levimc.launcher:attr/selectableItemBackground = 0x7f0303b8
org.levimc.launcher:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0501cc
org.levimc.launcher:dimen/m3_comp_fab_primary_container_height = 0x7f060116
org.levimc.launcher:dimen/m3_comp_fab_primary_container_elevation = 0x7f060115
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060113
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060110
org.levimc.launcher:drawable/ic_mtrl_chip_checked_black = 0x7f0700ac
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f06010f
org.levimc.launcher:dimen/m3_comp_elevated_card_icon_size = 0x7f06010b
org.levimc.launcher:color/material_personalized_color_outline = 0x7f05028d
org.levimc.launcher:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f060109
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0d00fb
org.levimc.launcher:dimen/m3_comp_elevated_button_container_elevation = 0x7f060108
org.levimc.launcher:dimen/m3_comp_divider_thickness = 0x7f060107
org.levimc.launcher:style/Theme.MaterialComponents.Light.Bridge = 0x7f12026f
org.levimc.launcher:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060106
org.levimc.launcher:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f060101
org.levimc.launcher:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0602c4
org.levimc.launcher:color/primary_text_default_material_light = 0x7f0502fe
org.levimc.launcher:dimen/m3_card_elevated_dragged_z = 0x7f0600e7
org.levimc.launcher:dimen/m3_comp_badge_size = 0x7f060100
org.levimc.launcher:dimen/fastscroll_margin = 0x7f060091
org.levimc.launcher:dimen/m3_comp_badge_large_size = 0x7f0600ff
org.levimc.launcher:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f0600fd
org.levimc.launcher:layout/mtrl_navigation_rail_item = 0x7f0c0065
org.levimc.launcher:dimen/m3_chip_dragged_translation_z = 0x7f0600f6
org.levimc.launcher:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1203cd
org.levimc.launcher:dimen/m3_chip_disabled_translation_z = 0x7f0600f5
org.levimc.launcher:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f120269
org.levimc.launcher:drawable/$m3_avd_hide_password__0 = 0x7f070006
org.levimc.launcher:drawable/abc_list_pressed_holo_light = 0x7f070051
org.levimc.launcher:dimen/m3_fab_translation_z_hovered_focused = 0x7f0601b7
org.levimc.launcher:id/material_textinput_timepicker = 0x7f090122
org.levimc.launcher:dimen/m3_carousel_small_item_size_max = 0x7f0600f1
org.levimc.launcher:id/snapMargins = 0x7f0901c2
org.levimc.launcher:attr/grid_spans = 0x7f030215
org.levimc.launcher:drawable/mtrl_navigation_bar_item_background = 0x7f0700dd
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f120198
org.levimc.launcher:dimen/m3_carousel_gone_size = 0x7f0600ef
org.levimc.launcher:attr/motionPathRotate = 0x7f03034d
org.levimc.launcher:dimen/m3_carousel_extra_small_item_size = 0x7f0600ee
org.levimc.launcher:dimen/m3_card_elevation = 0x7f0600ea
org.levimc.launcher:attr/guidelineUseRtl = 0x7f030219
org.levimc.launcher:dimen/abc_text_size_display_1_material = 0x7f060043
org.levimc.launcher:dimen/m3_card_elevated_hovered_z = 0x7f0600e9
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f1200a0
org.levimc.launcher:id/textEnd = 0x7f0901ee
org.levimc.launcher:dimen/mtrl_tooltip_minWidth = 0x7f060309
org.levimc.launcher:dimen/m3_card_dragged_z = 0x7f0600e5
org.levimc.launcher:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1202ad
org.levimc.launcher:id/material_timepicker_cancel_button = 0x7f090123
org.levimc.launcher:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700da
org.levimc.launcher:attr/paddingBottomNoButtons = 0x7f03036a
org.levimc.launcher:dimen/m3_card_disabled_z = 0x7f0600e4
org.levimc.launcher:dimen/tooltip_y_offset_non_touch = 0x7f060328
org.levimc.launcher:dimen/m3_btn_translation_z_hovered = 0x7f0600e3
org.levimc.launcher:color/m3_ref_palette_tertiary70 = 0x7f05014a
org.levimc.launcher:dimen/m3_btn_translation_z_base = 0x7f0600e2
org.levimc.launcher:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0d0106
org.levimc.launcher:dimen/m3_btn_text_btn_padding_right = 0x7f0600e1
org.levimc.launcher:style/Theme.Material3.Light.NoActionBar = 0x7f12024e
org.levimc.launcher:color/m3_ref_palette_neutral_variant10 = 0x7f05011c
org.levimc.launcher:dimen/mtrl_textinput_box_corner_radius_small = 0x7f0602fd
org.levimc.launcher:dimen/m3_btn_text_btn_padding_left = 0x7f0600e0
org.levimc.launcher:color/m3_ref_palette_error40 = 0x7f0500fb
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary20 = 0x7f0500df
org.levimc.launcher:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600df
org.levimc.launcher:attr/materialSearchBarStyle = 0x7f030302
org.levimc.launcher:dimen/m3_btn_padding_left = 0x7f0600da
org.levimc.launcher:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120271
org.levimc.launcher:dimen/m3_btn_padding_bottom = 0x7f0600d9
org.levimc.launcher:integer/m3_sys_motion_duration_extra_long1 = 0x7f0a0010
org.levimc.launcher:dimen/m3_btn_icon_btn_padding_right = 0x7f0600d2
org.levimc.launcher:dimen/m3_btn_icon_btn_padding_left = 0x7f0600d1
org.levimc.launcher:attr/SharedValueId = 0x7f030001
org.levimc.launcher:dimen/mtrl_extended_fab_elevation = 0x7f0602a9
org.levimc.launcher:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f070088
org.levimc.launcher:string/mtrl_switch_track_decoration_path = 0x7f1100ce
org.levimc.launcher:id/labeled = 0x7f090102
org.levimc.launcher:attr/cornerFamilyTopRight = 0x7f030150
org.levimc.launcher:dimen/m3_comp_navigation_rail_container_elevation = 0x7f060147
org.levimc.launcher:dimen/m3_btn_elevated_btn_elevation = 0x7f0600cf
org.levimc.launcher:dimen/m3_btn_disabled_elevation = 0x7f0600cd
org.levimc.launcher:styleable/ExtendedFloatingActionButton = 0x7f130031
org.levimc.launcher:attr/chipMinHeight = 0x7f0300c7
org.levimc.launcher:dimen/m3_btn_dialog_btn_spacing = 0x7f0600cc
org.levimc.launcher:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f11006f
org.levimc.launcher:dimen/m3_btn_dialog_btn_min_width = 0x7f0600cb
org.levimc.launcher:attr/textureBlurFactor = 0x7f030477
org.levimc.launcher:color/m3_ref_palette_neutral_variant30 = 0x7f05011f
org.levimc.launcher:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600c8
org.levimc.launcher:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600c5
org.levimc.launcher:style/Widget.MaterialComponents.ActionMode = 0x7f1203f9
org.levimc.launcher:dimen/m3_bottom_sheet_elevation = 0x7f0600c3
org.levimc.launcher:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c2
org.levimc.launcher:dimen/m3_bottom_nav_min_height = 0x7f0600c1
org.levimc.launcher:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0601fd
org.levimc.launcher:dimen/m3_comp_slider_active_handle_leading_space = 0x7f060182
org.levimc.launcher:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0502a8
org.levimc.launcher:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600be
org.levimc.launcher:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f120168
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0d00fc
org.levimc.launcher:drawable/abc_btn_borderless_material = 0x7f07002a
org.levimc.launcher:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600bc
org.levimc.launcher:dimen/m3_badge_with_text_vertical_padding = 0x7f0600bb
org.levimc.launcher:color/m3_sys_color_on_secondary_fixed = 0x7f0501f1
org.levimc.launcher:dimen/m3_badge_with_text_size = 0x7f0600b9
org.levimc.launcher:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600b7
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120075
org.levimc.launcher:dimen/mtrl_tooltip_arrowSize = 0x7f060306
org.levimc.launcher:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0602df
org.levimc.launcher:dimen/m3_badge_vertical_offset = 0x7f0600b6
org.levimc.launcher:layout/material_clockface_view = 0x7f0c004a
org.levimc.launcher:attr/touchAnchorId = 0x7f0304b1
org.levimc.launcher:color/m3_ref_palette_tertiary30 = 0x7f050146
org.levimc.launcher:dimen/m3_badge_size = 0x7f0600b5
org.levimc.launcher:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0d0149
org.levimc.launcher:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600af
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker = 0x7f1203bf
org.levimc.launcher:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1200e9
org.levimc.launcher:string/call_notification_ongoing_text = 0x7f11002c
org.levimc.launcher:color/m3_ref_palette_primary80 = 0x7f050131
org.levimc.launcher:color/m3_dark_primary_text_disable_only = 0x7f05007a
org.levimc.launcher:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600ad
org.levimc.launcher:id/textinput_suffix_text = 0x7f0901fd
org.levimc.launcher:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600ac
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f120312
org.levimc.launcher:attr/visibilityMode = 0x7f0304d8
org.levimc.launcher:dimen/mtrl_tooltip_padding = 0x7f06030a
org.levimc.launcher:dimen/m3_appbar_size_large = 0x7f0600aa
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070012
org.levimc.launcher:style/ShapeAppearance.Material3.Corner.Small = 0x7f12017f
org.levimc.launcher:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a5
org.levimc.launcher:style/Base.Widget.AppCompat.Button = 0x7f1200d4
org.levimc.launcher:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a3
org.levimc.launcher:layout/abc_screen_toolbar = 0x7f0c0017
org.levimc.launcher:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f060151
org.levimc.launcher:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0d004e
org.levimc.launcher:dimen/m3_alert_dialog_icon_margin = 0x7f0600a1
org.levimc.launcher:attr/startIconContentDescription = 0x7f0303f1
org.levimc.launcher:dimen/m3_alert_dialog_elevation = 0x7f0600a0
org.levimc.launcher:id/tag_accessibility_pane_title = 0x7f0901e2
org.levimc.launcher:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
org.levimc.launcher:dimen/m3_alert_dialog_action_top_padding = 0x7f06009e
org.levimc.launcher:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009c
org.levimc.launcher:id/tag_state_description = 0x7f0901e7
org.levimc.launcher:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009b
org.levimc.launcher:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1202c7
org.levimc.launcher:drawable/notification_template_icon_bg = 0x7f0700f7
org.levimc.launcher:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009a
org.levimc.launcher:string/unknown_sources_permission_title = 0x7f110104
org.levimc.launcher:attr/textAppearanceLineHeightEnabled = 0x7f030452
org.levimc.launcher:dimen/hint_pressed_alpha_material_light = 0x7f060099
org.levimc.launcher:dimen/hint_pressed_alpha_material_dark = 0x7f060098
org.levimc.launcher:dimen/notification_big_circle_margin = 0x7f06030e
org.levimc.launcher:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f06014d
org.levimc.launcher:dimen/hint_alpha_material_light = 0x7f060097
org.levimc.launcher:attr/spanCount = 0x7f0303e2
org.levimc.launcher:dimen/hint_alpha_material_dark = 0x7f060096
org.levimc.launcher:dimen/highlight_alpha_material_dark = 0x7f060094
org.levimc.launcher:dimen/m3_navigation_item_shape_inset_end = 0x7f0601c3
org.levimc.launcher:dimen/disabled_alpha_material_dark = 0x7f06008e
org.levimc.launcher:color/mtrl_navigation_bar_ripple_color = 0x7f0502d6
org.levimc.launcher:dimen/design_tab_scrollable_min_width = 0x7f06008a
org.levimc.launcher:string/user_cancelled = 0x7f110108
org.levimc.launcher:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004
org.levimc.launcher:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f060180
org.levimc.launcher:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060155
org.levimc.launcher:dimen/design_snackbar_text_size = 0x7f060088
org.levimc.launcher:anim/slide_in_top = 0x7f01002c
org.levimc.launcher:color/m3_sys_color_dark_on_primary = 0x7f050164
org.levimc.launcher:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f06020a
org.levimc.launcher:color/m3_primary_text_disable_only = 0x7f05009b
org.levimc.launcher:dimen/design_snackbar_padding_vertical = 0x7f060086
org.levimc.launcher:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0502ee
org.levimc.launcher:dimen/design_snackbar_padding_horizontal = 0x7f060085
org.levimc.launcher:dimen/design_snackbar_max_width = 0x7f060083
org.levimc.launcher:drawable/ic_call_answer = 0x7f070096
org.levimc.launcher:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060082
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary50 = 0x7f0500e2
org.levimc.launcher:dimen/design_snackbar_background_corner_radius = 0x7f060080
org.levimc.launcher:styleable/TextEffects = 0x7f13008b
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f12009b
org.levimc.launcher:attr/itemSpacing = 0x7f030263
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500c5
org.levimc.launcher:dimen/design_snackbar_action_text_color_alpha = 0x7f06007f
org.levimc.launcher:dimen/design_navigation_separator_vertical_padding = 0x7f06007d
org.levimc.launcher:style/Widget.Design.TextInputEditText = 0x7f120348
org.levimc.launcher:dimen/design_navigation_max_width = 0x7f06007b
org.levimc.launcher:layout/activity_splash = 0x7f0c001d
org.levimc.launcher:dimen/mtrl_btn_padding_bottom = 0x7f060265
org.levimc.launcher:dimen/design_navigation_item_vertical_padding = 0x7f06007a
org.levimc.launcher:id/transition_clip = 0x7f090208
org.levimc.launcher:dimen/design_navigation_item_horizontal_padding = 0x7f060078
org.levimc.launcher:dimen/design_navigation_icon_size = 0x7f060077
org.levimc.launcher:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1200a9
org.levimc.launcher:dimen/mtrl_tooltip_minHeight = 0x7f060308
org.levimc.launcher:dimen/design_navigation_icon_padding = 0x7f060076
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f12025b
org.levimc.launcher:attr/actionModeTheme = 0x7f03001f
org.levimc.launcher:color/tertiary = 0x7f05030f
org.levimc.launcher:dimen/design_fab_image_size = 0x7f060070
org.levimc.launcher:dimen/design_fab_elevation = 0x7f06006f
org.levimc.launcher:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f12004b
org.levimc.launcher:dimen/design_fab_border_width = 0x7f06006e
org.levimc.launcher:styleable/TextInputLayout = 0x7f13008d
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f120195
org.levimc.launcher:dimen/design_bottom_sheet_peek_height_min = 0x7f06006d
org.levimc.launcher:dimen/design_bottom_navigation_item_max_width = 0x7f060065
org.levimc.launcher:attr/chipIconTint = 0x7f0300c5
org.levimc.launcher:dimen/design_bottom_navigation_icon_size = 0x7f060064
org.levimc.launcher:dimen/design_bottom_navigation_height = 0x7f060063
org.levimc.launcher:attr/fontVariationSettings = 0x7f030205
org.levimc.launcher:anim/mtrl_card_lowers_interpolator = 0x7f01002b
org.levimc.launcher:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070023
org.levimc.launcher:color/material_on_surface_disabled = 0x7f050275
org.levimc.launcher:dimen/design_bottom_navigation_elevation = 0x7f060062
org.levimc.launcher:dimen/design_bottom_navigation_active_text_size = 0x7f060061
org.levimc.launcher:drawable/abc_list_divider_mtrl_alpha = 0x7f07004d
org.levimc.launcher:drawable/mtrl_switch_track = 0x7f0700ea
org.levimc.launcher:dimen/m3_btn_padding_right = 0x7f0600db
org.levimc.launcher:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1202aa
org.levimc.launcher:attr/carousel_previousState = 0x7f0300a9
org.levimc.launcher:attr/windowMinWidthMajor = 0x7f0304e8
org.levimc.launcher:dimen/design_appbar_elevation = 0x7f06005e
org.levimc.launcher:mipmap/ic_launcher_adaptive_back = 0x7f0f0001
org.levimc.launcher:attr/itemTextAppearanceActiveBoldEnabled = 0x7f030268
org.levimc.launcher:dimen/def_drawer_elevation = 0x7f06005d
org.levimc.launcher:attr/onPositiveCross = 0x7f030364
org.levimc.launcher:drawable/bg_abi_x86 = 0x7f07007d
org.levimc.launcher:attr/windowSplashScreenAnimatedIcon = 0x7f0304eb
org.levimc.launcher:color/material_dynamic_neutral_variant10 = 0x7f050231
org.levimc.launcher:dimen/compat_notification_large_icon_max_width = 0x7f06005c
org.levimc.launcher:dimen/compat_control_corner_material = 0x7f06005a
org.levimc.launcher:string/mtrl_picker_end_date_description = 0x7f1100ae
org.levimc.launcher:layout/mtrl_calendar_month = 0x7f0c005d
org.levimc.launcher:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f06021a
org.levimc.launcher:attr/buttonBarStyle = 0x7f03008e
org.levimc.launcher:dimen/compat_button_padding_vertical_material = 0x7f060059
org.levimc.launcher:drawable/abc_control_background_material = 0x7f07003a
org.levimc.launcher:dimen/m3_side_sheet_standard_elevation = 0x7f0601e7
org.levimc.launcher:dimen/compat_button_inset_horizontal_material = 0x7f060056
org.levimc.launcher:attr/layout_constraintLeft_toRightOf = 0x7f03029b
org.levimc.launcher:dimen/clock_face_margin_start = 0x7f060055
org.levimc.launcher:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f120093
org.levimc.launcher:style/Base.TextAppearance.MaterialComponents.Button = 0x7f120048
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0d0159
org.levimc.launcher:layout/notification_template_part_time = 0x7f0c0077
org.levimc.launcher:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
org.levimc.launcher:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f12008b
org.levimc.launcher:attr/fontProviderPackage = 0x7f030201
org.levimc.launcher:dimen/abc_text_size_small_material = 0x7f06004c
org.levimc.launcher:color/material_slider_active_tick_marks_color = 0x7f0502ad
org.levimc.launcher:dimen/abc_text_size_menu_material = 0x7f06004b
org.levimc.launcher:dimen/abc_text_size_menu_header_material = 0x7f06004a
org.levimc.launcher:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1200d7
org.levimc.launcher:dimen/abc_text_size_headline_material = 0x7f060047
org.levimc.launcher:dimen/abc_text_size_display_4_material = 0x7f060046
org.levimc.launcher:attr/motionProgress = 0x7f03034e
org.levimc.launcher:dimen/abc_text_size_display_3_material = 0x7f060045
org.levimc.launcher:macro/m3_comp_dialog_container_color = 0x7f0d0022
org.levimc.launcher:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070021
org.levimc.launcher:dimen/mtrl_calendar_days_of_week_height = 0x7f06027c
org.levimc.launcher:macro/m3_comp_time_picker_container_shape = 0x7f0d014f
org.levimc.launcher:dimen/abc_text_size_caption_material = 0x7f060042
org.levimc.launcher:dimen/design_bottom_sheet_elevation = 0x7f06006b
org.levimc.launcher:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0601f9
org.levimc.launcher:id/tag_transition_group = 0x7f0901e8
org.levimc.launcher:dimen/abc_text_size_body_1_material = 0x7f06003f
org.levimc.launcher:dimen/abc_switch_padding = 0x7f06003e
org.levimc.launcher:dimen/abc_star_small = 0x7f06003d
org.levimc.launcher:attr/toolbarNavigationButtonStyle = 0x7f0304a9
org.levimc.launcher:dimen/abc_star_medium = 0x7f06003c
org.levimc.launcher:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
org.levimc.launcher:dimen/abc_seekbar_track_background_height_material = 0x7f060038
org.levimc.launcher:dimen/abc_panel_menu_list_width = 0x7f060034
org.levimc.launcher:string/abc_capital_on = 0x7f110007
org.levimc.launcher:macro/m3_comp_search_bar_leading_icon_color = 0x7f0d00eb
org.levimc.launcher:anim/design_snackbar_in = 0x7f01001a
org.levimc.launcher:attr/arrowShaftLength = 0x7f03003a
org.levimc.launcher:dimen/abc_floating_window_z = 0x7f06002f
org.levimc.launcher:string/install_failed = 0x7f11005d
org.levimc.launcher:drawable/abc_ic_go_search_api_material = 0x7f070041
org.levimc.launcher:dimen/abc_edit_text_inset_top_material = 0x7f06002e
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0d0165
org.levimc.launcher:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0d0130
org.levimc.launcher:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0d0062
org.levimc.launcher:layout/abc_action_mode_close_item_material = 0x7f0c0005
org.levimc.launcher:drawable/material_ic_clear_black_24dp = 0x7f0700c0
org.levimc.launcher:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f06023b
org.levimc.launcher:dimen/abc_star_big = 0x7f06003b
org.levimc.launcher:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
org.levimc.launcher:attr/autoSizeMinTextSize = 0x7f030041
org.levimc.launcher:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070035
org.levimc.launcher:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0d0064
org.levimc.launcher:id/accessibility_custom_action_15 = 0x7f09001a
org.levimc.launcher:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
org.levimc.launcher:dimen/abc_dropdownitem_icon_width = 0x7f060029
org.levimc.launcher:dimen/abc_disabled_alpha_material_light = 0x7f060028
org.levimc.launcher:dimen/abc_dialog_title_divider_material = 0x7f060026
org.levimc.launcher:dimen/abc_dialog_padding_top_material = 0x7f060025
org.levimc.launcher:dimen/m3_timepicker_display_stroke_width = 0x7f06021e
org.levimc.launcher:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500aa
org.levimc.launcher:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
org.levimc.launcher:dimen/abc_dialog_corner_radius_material = 0x7f06001b
org.levimc.launcher:dimen/m3_carousel_small_item_size_min = 0x7f0600f2
org.levimc.launcher:dimen/abc_config_prefDialogWidth = 0x7f060017
org.levimc.launcher:dimen/abc_button_padding_vertical_material = 0x7f060015
org.levimc.launcher:dimen/abc_button_padding_horizontal_material = 0x7f060014
org.levimc.launcher:attr/listChoiceIndicatorSingleAnimated = 0x7f0302c8
org.levimc.launcher:dimen/abc_button_inset_vertical_material = 0x7f060013
org.levimc.launcher:attr/colorOutlineVariant = 0x7f030112
org.levimc.launcher:dimen/abc_button_inset_horizontal_material = 0x7f060012
org.levimc.launcher:style/Widget.Material3.TabLayout.Secondary = 0x7f1203e4
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Button = 0x7f12001c
org.levimc.launcher:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
org.levimc.launcher:layout/dialog_install_progress = 0x7f0c0031
org.levimc.launcher:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
org.levimc.launcher:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
org.levimc.launcher:dimen/m3_navigation_item_shape_inset_top = 0x7f0601c5
org.levimc.launcher:dimen/abc_action_bar_elevation_material = 0x7f060005
org.levimc.launcher:attr/autoSizeTextType = 0x7f030044
org.levimc.launcher:attr/boxBackgroundMode = 0x7f03007f
org.levimc.launcher:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
org.levimc.launcher:color/m3_sys_color_light_surface_dim = 0x7f0501eb
org.levimc.launcher:color/tooltip_background_dark = 0x7f050310
org.levimc.launcher:color/switch_thumb_normal_material_dark = 0x7f05030d
org.levimc.launcher:layout/material_clock_display_divider = 0x7f0c0046
org.levimc.launcher:dimen/m3_navigation_rail_item_padding_top = 0x7f0601d2
org.levimc.launcher:color/switch_thumb_material_light = 0x7f05030c
org.levimc.launcher:color/switch_thumb_material_dark = 0x7f05030b
org.levimc.launcher:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600ae
org.levimc.launcher:color/switch_thumb_disabled_material_light = 0x7f05030a
org.levimc.launcher:dimen/tooltip_precise_anchor_extra_offset = 0x7f060325
org.levimc.launcher:color/switch_thumb_disabled_material_dark = 0x7f050309
org.levimc.launcher:attr/telltales_tailColor = 0x7f030437
org.levimc.launcher:color/surface = 0x7f050308
org.levimc.launcher:color/secondary_text_default_material_light = 0x7f050305
org.levimc.launcher:color/ripple_material_light = 0x7f050302
org.levimc.launcher:color/ripple_material_dark = 0x7f050301
org.levimc.launcher:id/sharedValueSet = 0x7f0901b5
org.levimc.launcher:dimen/m3_ripple_default_alpha = 0x7f0601d5
org.levimc.launcher:style/Theme.Material3.DayNight.Dialog = 0x7f12023c
org.levimc.launcher:color/primary_text_default_material_dark = 0x7f0502fd
org.levimc.launcher:style/TextAppearance.AppCompat.Caption = 0x7f1201a2
org.levimc.launcher:attr/colorOnErrorContainer = 0x7f030100
org.levimc.launcher:color/primary_material_light = 0x7f0502fc
org.levimc.launcher:color/m3_fab_efab_foreground_color_selector = 0x7f05008a
org.levimc.launcher:color/primary_dark_material_light = 0x7f0502fa
org.levimc.launcher:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0d0041
org.levimc.launcher:id/grouping = 0x7f0900e4
org.levimc.launcher:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0601d9
org.levimc.launcher:string/update_question = 0x7f110107
org.levimc.launcher:layout/m3_alert_dialog_title = 0x7f0c0041
org.levimc.launcher:attr/trackColorInactive = 0x7f0304b7
org.levimc.launcher:color/design_dark_default_color_on_surface = 0x7f050038
org.levimc.launcher:color/primary_dark_material_dark = 0x7f0502f9
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f060205
org.levimc.launcher:layout/item_settings_spinner = 0x7f0c003a
org.levimc.launcher:color/on_surface = 0x7f0502f5
org.levimc.launcher:layout/notification_template_custom_big = 0x7f0c0074
org.levimc.launcher:id/title = 0x7f0901ff
org.levimc.launcher:color/on_background = 0x7f0502f1
org.levimc.launcher:color/material_personalized_color_surface_dim = 0x7f05029f
org.levimc.launcher:drawable/m3_tabs_rounded_line_indicator = 0x7f0700bc
org.levimc.launcher:color/m3_sys_color_dark_inverse_on_surface = 0x7f05015e
org.levimc.launcher:color/notification_icon_bg_color = 0x7f0502f0
org.levimc.launcher:id/triangle = 0x7f090210
org.levimc.launcher:color/mtrl_textinput_filled_box_default_background_color = 0x7f0502ec
org.levimc.launcher:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0d0012
org.levimc.launcher:attr/motionEffect_strict = 0x7f030347
org.levimc.launcher:attr/subheaderColor = 0x7f030407
org.levimc.launcher:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601d1
org.levimc.launcher:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070046
org.levimc.launcher:attr/tabBackground = 0x7f030419
org.levimc.launcher:color/mtrl_textinput_default_box_stroke_color = 0x7f0502ea
org.levimc.launcher:color/mtrl_text_btn_text_color_selector = 0x7f0502e9
org.levimc.launcher:color/mtrl_tabs_ripple_color = 0x7f0502e8
org.levimc.launcher:color/mtrl_tabs_legacy_text_color_selector = 0x7f0502e7
org.levimc.launcher:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f120298
org.levimc.launcher:dimen/m3_comp_suggestion_chip_container_height = 0x7f06018a
org.levimc.launcher:color/mtrl_tabs_icon_color_selector_colored = 0x7f0502e6
org.levimc.launcher:color/mtrl_tabs_icon_color_selector = 0x7f0502e5
org.levimc.launcher:attr/motionInterpolator = 0x7f03034b
org.levimc.launcher:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060257
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0500bd
org.levimc.launcher:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
org.levimc.launcher:color/mtrl_switch_track_decoration_tint = 0x7f0502e2
org.levimc.launcher:color/mtrl_switch_thumb_tint = 0x7f0502e1
org.levimc.launcher:color/mtrl_outlined_stroke_color = 0x7f0502dd
org.levimc.launcher:attr/layout_editor_absoluteY = 0x7f0302af
org.levimc.launcher:color/mtrl_outlined_icon_tint = 0x7f0502dc
org.levimc.launcher:color/mtrl_on_surface_ripple_color = 0x7f0502db
org.levimc.launcher:color/mtrl_navigation_item_icon_tint = 0x7f0502d8
org.levimc.launcher:color/m3_sys_color_light_surface = 0x7f0501e4
org.levimc.launcher:dimen/m3_card_elevated_elevation = 0x7f0600e8
org.levimc.launcher:styleable/BaseProgressIndicator = 0x7f130014
org.levimc.launcher:style/Widget.AppCompat.ListPopupWindow = 0x7f120325
org.levimc.launcher:color/mtrl_navigation_bar_colored_item_tint = 0x7f0502d3
org.levimc.launcher:color/mtrl_fab_ripple_color = 0x7f0502ce
org.levimc.launcher:string/minecraft = 0x7f11008e
org.levimc.launcher:color/mtrl_fab_bg_color_selector = 0x7f0502cc
org.levimc.launcher:attr/reverseLayout = 0x7f0303a9
org.levimc.launcher:color/mtrl_error = 0x7f0502cb
org.levimc.launcher:id/textinput_prefix_text = 0x7f0901fc
org.levimc.launcher:drawable/mtrl_switch_track_decoration = 0x7f0700eb
org.levimc.launcher:color/mtrl_choice_chip_text_color = 0x7f0502ca
org.levimc.launcher:dimen/notification_small_icon_size_as_large = 0x7f060317
org.levimc.launcher:color/mtrl_choice_chip_ripple_color = 0x7f0502c9
org.levimc.launcher:color/m3_ref_palette_tertiary60 = 0x7f050149
org.levimc.launcher:color/mtrl_chip_close_icon_tint = 0x7f0502c5
org.levimc.launcher:color/mtrl_chip_background_color = 0x7f0502c4
org.levimc.launcher:color/primary = 0x7f0502f8
org.levimc.launcher:color/mtrl_card_view_foreground = 0x7f0502c2
org.levimc.launcher:id/dragClockwise = 0x7f0900b0
org.levimc.launcher:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601a7
org.levimc.launcher:attr/onHide = 0x7f030362
org.levimc.launcher:color/mtrl_btn_transparent_bg_color = 0x7f0502bf
org.levimc.launcher:dimen/abc_dialog_min_width_major = 0x7f060022
org.levimc.launcher:dimen/m3_datepicker_elevation = 0x7f0601ad
org.levimc.launcher:color/mtrl_btn_text_color_selector = 0x7f0502be
org.levimc.launcher:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f060192
org.levimc.launcher:color/mtrl_btn_text_color_disabled = 0x7f0502bd
org.levimc.launcher:color/mtrl_btn_text_btn_ripple_color = 0x7f0502bc
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f12003d
org.levimc.launcher:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f060163
org.levimc.launcher:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f120383
org.levimc.launcher:color/mtrl_btn_stroke_color_selector = 0x7f0502ba
org.levimc.launcher:color/mtrl_btn_ripple_color = 0x7f0502b9
org.levimc.launcher:dimen/compat_button_inset_vertical_material = 0x7f060057
org.levimc.launcher:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f060136
org.levimc.launcher:color/mtrl_btn_bg_color_selector = 0x7f0502b8
org.levimc.launcher:style/Base.Theme.MaterialComponents = 0x7f120069
org.levimc.launcher:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f06026a
org.levimc.launcher:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
org.levimc.launcher:color/material_timepicker_modebutton_tint = 0x7f0502b7
org.levimc.launcher:drawable/abc_list_longpressed_holo = 0x7f07004f
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f060217
org.levimc.launcher:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f120059
org.levimc.launcher:color/material_timepicker_clockface = 0x7f0502b6
org.levimc.launcher:color/material_timepicker_button_background = 0x7f0502b3
org.levimc.launcher:attr/fabAnimationMode = 0x7f0301cd
org.levimc.launcher:attr/state_with_icon = 0x7f030400
org.levimc.launcher:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501ca
org.levimc.launcher:color/material_slider_thumb_color = 0x7f0502b2
org.levimc.launcher:color/material_personalized_primary_text_disable_only = 0x7f0502ac
org.levimc.launcher:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000d
org.levimc.launcher:color/material_personalized_primary_inverse_text_disable_only = 0x7f0502ab
org.levimc.launcher:dimen/mtrl_low_ripple_focused_alpha = 0x7f0602bf
org.levimc.launcher:color/material_personalized_hint_foreground = 0x7f0502a9
org.levimc.launcher:style/Widget.Material3.Chip.Assist.Elevated = 0x7f120374
org.levimc.launcher:color/material_personalized_color_text_primary_inverse = 0x7f0502a5
org.levimc.launcher:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f060129
org.levimc.launcher:color/material_personalized_color_tertiary_container = 0x7f0502a3
org.levimc.launcher:dimen/mtrl_calendar_header_divider_thickness = 0x7f060280
org.levimc.launcher:style/Widget.Material3.Button.TextButton.Dialog = 0x7f120367
org.levimc.launcher:color/material_personalized_color_surface_inverse = 0x7f0502a0
org.levimc.launcher:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0602a8
org.levimc.launcher:string/mtrl_picker_text_input_date_hint = 0x7f1100bd
org.levimc.launcher:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0d0042
org.levimc.launcher:macro/m3_comp_fab_tertiary_container_color = 0x7f0d003f
org.levimc.launcher:dimen/m3_side_sheet_width = 0x7f0601e8
org.levimc.launcher:color/material_personalized_color_surface_container_lowest = 0x7f05029e
org.levimc.launcher:color/material_personalized_color_surface_container_low = 0x7f05029d
org.levimc.launcher:color/material_personalized_color_surface_container_highest = 0x7f05029c
org.levimc.launcher:drawable/abc_dialog_material_background = 0x7f07003b
org.levimc.launcher:color/material_timepicker_clock_text_color = 0x7f0502b5
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1203c2
org.levimc.launcher:color/material_personalized_color_surface_container = 0x7f05029a
org.levimc.launcher:dimen/material_clock_period_toggle_width = 0x7f06022e
org.levimc.launcher:color/mtrl_card_view_ripple = 0x7f0502c3
org.levimc.launcher:color/m3_ref_palette_neutral90 = 0x7f050114
org.levimc.launcher:color/material_personalized_color_surface_bright = 0x7f050299
org.levimc.launcher:id/enterAlwaysCollapsed = 0x7f0900c7
org.levimc.launcher:attr/behavior_autoHide = 0x7f030067
org.levimc.launcher:color/material_personalized_color_surface = 0x7f050298
org.levimc.launcher:dimen/m3_searchbar_margin_horizontal = 0x7f0601dc
org.levimc.launcher:color/m3_sys_color_light_on_secondary_container = 0x7f0501d9
org.levimc.launcher:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700c5
org.levimc.launcher:color/material_personalized_color_secondary_text_inverse = 0x7f050297
org.levimc.launcher:style/ThemeOverlay.Material3.Dialog = 0x7f1202a5
org.levimc.launcher:attr/drawableTint = 0x7f03018e
org.levimc.launcher:dimen/material_textinput_max_width = 0x7f060242
org.levimc.launcher:style/Widget.AppCompat.PopupMenu = 0x7f120329
org.levimc.launcher:dimen/material_emphasis_disabled_background = 0x7f060234
org.levimc.launcher:color/material_personalized_color_primary_text_inverse = 0x7f050293
org.levimc.launcher:drawable/abc_ratingbar_small_material = 0x7f07005c
org.levimc.launcher:attr/tickMark = 0x7f03048f
org.levimc.launcher:color/material_personalized_color_on_surface_variant = 0x7f05028a
org.levimc.launcher:color/material_personalized_color_on_surface = 0x7f050288
org.levimc.launcher:macro/m3_comp_menu_container_color = 0x7f0d005d
org.levimc.launcher:color/material_personalized_color_on_error = 0x7f050282
org.levimc.launcher:dimen/design_tab_text_size_2line = 0x7f06008c
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f12003a
org.levimc.launcher:color/material_personalized_color_on_background = 0x7f050281
org.levimc.launcher:color/material_personalized_color_error = 0x7f05027f
org.levimc.launcher:style/Base.Widget.Material3.Snackbar = 0x7f120114
org.levimc.launcher:attr/actionLayout = 0x7f03000f
org.levimc.launcher:attr/cursorColor = 0x7f03015f
org.levimc.launcher:color/material_personalized_color_control_normal = 0x7f05027e
org.levimc.launcher:color/material_personalized_color_control_activated = 0x7f05027c
org.levimc.launcher:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1200a1
org.levimc.launcher:id/baseline = 0x7f090060
org.levimc.launcher:color/material_personalized__highlighted_text_inverse = 0x7f05027a
org.levimc.launcher:color/material_on_background_emphasis_high_type = 0x7f050270
org.levimc.launcher:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Display1 = 0x7f12001e
org.levimc.launcher:attr/textAppearanceHeadline3 = 0x7f030447
org.levimc.launcher:color/material_dynamic_tertiary99 = 0x7f050263
org.levimc.launcher:color/material_personalized__highlighted_text = 0x7f050279
org.levimc.launcher:color/material_on_surface_stroke = 0x7f050278
org.levimc.launcher:color/material_on_surface_emphasis_medium = 0x7f050277
org.levimc.launcher:color/material_on_surface_emphasis_high_type = 0x7f050276
org.levimc.launcher:style/Widget.Material3.BottomNavigationView = 0x7f120357
org.levimc.launcher:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0d00c6
org.levimc.launcher:color/material_on_background_emphasis_medium = 0x7f050271
org.levimc.launcher:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0d009a
org.levimc.launcher:dimen/m3_comp_navigation_bar_container_elevation = 0x7f060138
org.levimc.launcher:styleable/Slider = 0x7f13007e
org.levimc.launcher:dimen/design_fab_size_normal = 0x7f060072
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f120029
org.levimc.launcher:id/SHOW_PROGRESS = 0x7f09000a
org.levimc.launcher:attr/fontProviderAuthority = 0x7f0301fd
org.levimc.launcher:color/material_on_background_disabled = 0x7f05026f
org.levimc.launcher:color/material_harmonized_color_on_error_container = 0x7f05026e
org.levimc.launcher:color/m3_icon_button_icon_color_selector = 0x7f05008f
org.levimc.launcher:color/material_grey_900 = 0x7f05026a
org.levimc.launcher:color/material_grey_850 = 0x7f050269
org.levimc.launcher:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f12005a
org.levimc.launcher:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0601a9
org.levimc.launcher:color/material_grey_800 = 0x7f050268
org.levimc.launcher:attr/navigationIconTint = 0x7f030357
org.levimc.launcher:dimen/mtrl_progress_circular_inset_small = 0x7f0602d7
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f120199
org.levimc.launcher:color/material_grey_600 = 0x7f050267
org.levimc.launcher:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1203da
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f120155
org.levimc.launcher:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f06011e
org.levimc.launcher:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1200d3
org.levimc.launcher:color/material_grey_50 = 0x7f050266
org.levimc.launcher:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f07008a
org.levimc.launcher:color/material_grey_300 = 0x7f050265
org.levimc.launcher:styleable/MaterialCalendar = 0x7f130052
org.levimc.launcher:string/clear_text_end_icon_content_description = 0x7f110034
org.levimc.launcher:color/material_on_primary_disabled = 0x7f050272
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500b6
org.levimc.launcher:color/material_dynamic_tertiary95 = 0x7f050262
org.levimc.launcher:color/material_dynamic_tertiary90 = 0x7f050261
org.levimc.launcher:color/material_dynamic_tertiary80 = 0x7f050260
org.levimc.launcher:color/material_slider_halo_color = 0x7f0502af
org.levimc.launcher:drawable/abc_seekbar_thumb_material = 0x7f070062
org.levimc.launcher:color/material_dynamic_tertiary60 = 0x7f05025e
org.levimc.launcher:layout/abc_popup_menu_item_layout = 0x7f0c0013
org.levimc.launcher:dimen/mtrl_slider_track_side_padding = 0x7f0602ee
org.levimc.launcher:color/material_dynamic_tertiary20 = 0x7f05025a
org.levimc.launcher:style/TextAppearance.Design.Hint = 0x7f1201d8
org.levimc.launcher:color/material_dynamic_tertiary100 = 0x7f050259
org.levimc.launcher:dimen/m3_menu_elevation = 0x7f0601bc
org.levimc.launcher:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f060123
org.levimc.launcher:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f12016e
org.levimc.launcher:attr/imagePanY = 0x7f03023d
org.levimc.launcher:attr/attributeName = 0x7f03003b
org.levimc.launcher:color/material_dynamic_tertiary0 = 0x7f050257
org.levimc.launcher:string/abc_menu_enter_shortcut_label = 0x7f11000b
org.levimc.launcher:attr/titleCollapseMode = 0x7f03049a
org.levimc.launcher:attr/titleMarginStart = 0x7f03049f
org.levimc.launcher:color/material_dynamic_secondary80 = 0x7f050253
org.levimc.launcher:attr/drawableEndCompat = 0x7f030189
org.levimc.launcher:color/material_dynamic_secondary60 = 0x7f050251
org.levimc.launcher:color/material_dynamic_secondary50 = 0x7f050250
org.levimc.launcher:color/material_dynamic_secondary40 = 0x7f05024f
org.levimc.launcher:color/material_dynamic_secondary30 = 0x7f05024e
org.levimc.launcher:id/staticPostLayout = 0x7f0901d7
org.levimc.launcher:color/m3_calendar_item_stroke_color = 0x7f05006b
org.levimc.launcher:color/material_dynamic_secondary100 = 0x7f05024c
org.levimc.launcher:color/material_dynamic_secondary10 = 0x7f05024b
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f06010e
org.levimc.launcher:style/TextAppearance.Material3.TitleMedium = 0x7f120200
org.levimc.launcher:color/material_dynamic_primary99 = 0x7f050249
org.levimc.launcher:color/m3_ref_palette_secondary30 = 0x7f050139
org.levimc.launcher:color/material_dynamic_primary90 = 0x7f050247
org.levimc.launcher:color/material_dynamic_primary70 = 0x7f050245
org.levimc.launcher:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f060237
org.levimc.launcher:style/Widget.MaterialComponents.ChipGroup = 0x7f12041c
org.levimc.launcher:string/about_title = 0x7f11001b
org.levimc.launcher:color/material_dynamic_primary60 = 0x7f050244
org.levimc.launcher:style/Base.Theme.AppCompat.Dialog = 0x7f120050
org.levimc.launcher:dimen/mtrl_calendar_month_horizontal_padding = 0x7f060289
org.levimc.launcher:style/TextAppearance.Compat.Notification.Title = 0x7f1201d2
org.levimc.launcher:color/material_dynamic_primary50 = 0x7f050243
org.levimc.launcher:color/material_dynamic_primary40 = 0x7f050242
org.levimc.launcher:attr/bottomSheetStyle = 0x7f03007d
org.levimc.launcher:color/material_dynamic_primary30 = 0x7f050241
org.levimc.launcher:dimen/notification_action_text_size = 0x7f06030d
org.levimc.launcher:color/material_dynamic_primary20 = 0x7f050240
org.levimc.launcher:attr/listPopupWindowStyle = 0x7f0302cd
org.levimc.launcher:color/material_dynamic_neutral_variant90 = 0x7f05023a
org.levimc.launcher:color/material_dynamic_primary10 = 0x7f05023e
org.levimc.launcher:color/m3_sys_color_light_on_background = 0x7f0501d3
org.levimc.launcher:color/mtrl_calendar_selected_range = 0x7f0502c1
org.levimc.launcher:color/material_dynamic_neutral_variant80 = 0x7f050239
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f120025
org.levimc.launcher:drawable/ic_call_decline = 0x7f07009a
org.levimc.launcher:dimen/abc_progress_bar_height_material = 0x7f060035
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.Button = 0x7f12018b
org.levimc.launcher:style/Base.Widget.AppCompat.ListMenuView = 0x7f1200ec
org.levimc.launcher:id/overshoot = 0x7f090177
org.levimc.launcher:dimen/mtrl_btn_padding_left = 0x7f060266
org.levimc.launcher:attr/fabAnchorMode = 0x7f0301cc
org.levimc.launcher:attr/itemShapeAppearance = 0x7f03025c
org.levimc.launcher:color/material_dynamic_neutral_variant40 = 0x7f050235
org.levimc.launcher:color/material_dynamic_neutral_variant20 = 0x7f050233
org.levimc.launcher:style/ShapeAppearance.Material3.Tooltip = 0x7f120184
org.levimc.launcher:color/material_dynamic_neutral_variant0 = 0x7f050230
org.levimc.launcher:attr/textAllCaps = 0x7f03043a
org.levimc.launcher:color/material_dynamic_neutral90 = 0x7f05022d
org.levimc.launcher:attr/shapeAppearanceCornerSmall = 0x7f0303c2
org.levimc.launcher:color/material_dynamic_neutral80 = 0x7f05022c
org.levimc.launcher:id/bestChoice = 0x7f090064
org.levimc.launcher:attr/tooltipText = 0x7f0304af
org.levimc.launcher:color/material_dynamic_neutral70 = 0x7f05022b
org.levimc.launcher:style/Widget.MaterialComponents.Badge = 0x7f120401
org.levimc.launcher:string/install_done = 0x7f11005c
org.levimc.launcher:color/material_dynamic_neutral50 = 0x7f050229
org.levimc.launcher:color/material_dynamic_neutral10 = 0x7f050224
org.levimc.launcher:color/material_dynamic_neutral0 = 0x7f050223
org.levimc.launcher:style/Theme.MaterialComponents = 0x7f120250
org.levimc.launcher:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700cf
org.levimc.launcher:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0602b1
org.levimc.launcher:id/material_timepicker_container = 0x7f090124
org.levimc.launcher:dimen/abc_dialog_fixed_width_major = 0x7f06001e
org.levimc.launcher:color/material_dynamic_color_light_on_error_container = 0x7f050222
org.levimc.launcher:color/material_dynamic_color_light_on_error = 0x7f050221
org.levimc.launcher:attr/actionBarTabTextStyle = 0x7f03000a
org.levimc.launcher:attr/preserveIconSpacing = 0x7f030390
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500a3
org.levimc.launcher:dimen/design_snackbar_min_width = 0x7f060084
org.levimc.launcher:color/material_dynamic_color_light_error_container = 0x7f050220
org.levimc.launcher:string/mtrl_picker_range_header_title = 0x7f1100b9
org.levimc.launcher:layout/dialog_loading = 0x7f0c0033
org.levimc.launcher:id/unlabeled = 0x7f090219
org.levimc.launcher:color/material_dynamic_color_light_error = 0x7f05021f
org.levimc.launcher:color/material_dynamic_color_dark_on_error_container = 0x7f05021e
org.levimc.launcher:color/material_divider_color = 0x7f05021a
org.levimc.launcher:dimen/abc_text_size_title_material_toolbar = 0x7f060050
org.levimc.launcher:dimen/m3_carousel_debug_keyline_width = 0x7f0600ed
org.levimc.launcher:attr/autoAdjustToWithinGrandparentBounds = 0x7f03003c
org.levimc.launcher:color/material_deep_teal_200 = 0x7f050218
org.levimc.launcher:id/material_hour_text_input = 0x7f09011d
org.levimc.launcher:drawable/ic_leaf_logo = 0x7f0700a6
org.levimc.launcher:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f060117
org.levimc.launcher:color/material_cursor_color = 0x7f050217
org.levimc.launcher:color/material_personalized_color_primary_inverse = 0x7f050291
org.levimc.launcher:dimen/mtrl_bottomappbar_height = 0x7f060258
org.levimc.launcher:color/material_blue_grey_900 = 0x7f050215
org.levimc.launcher:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0301e3
org.levimc.launcher:dimen/m3_comp_filled_card_container_elevation = 0x7f060124
org.levimc.launcher:attr/checkedIconMargin = 0x7f0300b8
org.levimc.launcher:attr/keyPositionType = 0x7f03026c
org.levimc.launcher:dimen/mtrl_btn_focused_z = 0x7f06025e
org.levimc.launcher:dimen/design_bottom_navigation_active_item_max_width = 0x7f06005f
org.levimc.launcher:color/m3_tonal_button_ripple_color_selector = 0x7f050213
org.levimc.launcher:color/material_personalized_color_on_primary_container = 0x7f050285
org.levimc.launcher:color/m3_timepicker_time_input_stroke_color = 0x7f050212
org.levimc.launcher:drawable/notification_bg_low_normal = 0x7f0700f1
org.levimc.launcher:styleable/OnClick = 0x7f13006e
org.levimc.launcher:color/m3_timepicker_clock_text_color = 0x7f05020c
org.levimc.launcher:color/m3_textfield_stroke_color = 0x7f050208
org.levimc.launcher:color/m3_textfield_indicator_text_color = 0x7f050205
org.levimc.launcher:attr/textInputLayoutFocusedRectEnabled = 0x7f03046c
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070013
org.levimc.launcher:styleable/ClockHandView = 0x7f130021
org.levimc.launcher:color/m3_text_button_ripple_color_selector = 0x7f050203
org.levimc.launcher:attr/deltaPolarRadius = 0x7f030176
org.levimc.launcher:color/m3_sys_color_dark_surface_bright = 0x7f050173
org.levimc.launcher:color/mtrl_navigation_bar_item_tint = 0x7f0502d5
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0500cd
org.levimc.launcher:color/m3_tabs_text_color_secondary = 0x7f050200
org.levimc.launcher:layout/design_text_input_start_icon = 0x7f0c002e
org.levimc.launcher:attr/layout_goneMarginBottom = 0x7f0302b1
org.levimc.launcher:color/m3_tabs_text_color = 0x7f0501ff
org.levimc.launcher:color/material_personalized_color_control_highlight = 0x7f05027d
org.levimc.launcher:attr/actionOverflowButtonStyle = 0x7f030021
org.levimc.launcher:dimen/notification_top_pad_large_text = 0x7f06031a
org.levimc.launcher:color/m3_tabs_ripple_color = 0x7f0501fd
org.levimc.launcher:color/m3_sys_color_secondary_fixed_dim = 0x7f0501f8
org.levimc.launcher:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f06012e
org.levimc.launcher:string/password_toggle_content_description = 0x7f1100de
org.levimc.launcher:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f06021c
org.levimc.launcher:color/m3_sys_color_secondary_fixed = 0x7f0501f7
org.levimc.launcher:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0501f4
org.levimc.launcher:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0501f2
org.levimc.launcher:dimen/m3_bottomappbar_height = 0x7f0600c9
org.levimc.launcher:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0602fc
org.levimc.launcher:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f060291
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0d0162
org.levimc.launcher:color/m3_sys_color_on_primary_fixed = 0x7f0501ef
org.levimc.launcher:color/design_box_stroke_color = 0x7f050031
org.levimc.launcher:drawable/m3_avd_hide_password = 0x7f0700b3
org.levimc.launcher:color/m3_sys_color_light_tertiary = 0x7f0501ed
org.levimc.launcher:color/m3_sys_color_light_surface_container_low = 0x7f0501e9
org.levimc.launcher:color/m3_sys_color_light_surface_container_highest = 0x7f0501e8
org.levimc.launcher:dimen/design_navigation_padding_bottom = 0x7f06007c
org.levimc.launcher:color/m3_sys_color_light_surface_container_high = 0x7f0501e7
org.levimc.launcher:color/m3_sys_color_light_surface_bright = 0x7f0501e5
org.levimc.launcher:style/Widget.AppCompat.Button.Colored = 0x7f120303
org.levimc.launcher:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f06019b
org.levimc.launcher:color/m3_tabs_icon_color_secondary = 0x7f0501fc
org.levimc.launcher:color/m3_sys_color_light_secondary = 0x7f0501e2
org.levimc.launcher:color/m3_sys_color_light_on_tertiary_container = 0x7f0501dd
org.levimc.launcher:color/m3_sys_color_light_on_tertiary = 0x7f0501dc
org.levimc.launcher:attr/searchViewStyle = 0x7f0303b6
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070015
org.levimc.launcher:dimen/m3_comp_switch_disabled_track_opacity = 0x7f060191
org.levimc.launcher:color/m3_sys_color_light_on_surface_variant = 0x7f0501db
org.levimc.launcher:layout/material_clock_display = 0x7f0c0045
org.levimc.launcher:color/material_dynamic_secondary90 = 0x7f050254
org.levimc.launcher:color/m3_ref_palette_primary50 = 0x7f05012e
org.levimc.launcher:color/m3_sys_color_light_on_primary_container = 0x7f0501d7
org.levimc.launcher:dimen/m3_card_stroke_width = 0x7f0600ec
org.levimc.launcher:color/m3_sys_color_light_on_error_container = 0x7f0501d5
org.levimc.launcher:attr/viewTransitionOnPositiveCross = 0x7f0304d7
org.levimc.launcher:color/m3_sys_color_light_on_error = 0x7f0501d4
org.levimc.launcher:dimen/compat_button_padding_horizontal_material = 0x7f060058
org.levimc.launcher:layout/design_layout_tab_text = 0x7f0c0025
org.levimc.launcher:attr/minSeparation = 0x7f03031d
org.levimc.launcher:color/m3_sys_color_light_inverse_primary = 0x7f0501d1
org.levimc.launcher:macro/m3_comp_filled_card_container_color = 0x7f0d0046
org.levimc.launcher:id/accessibility_custom_action_4 = 0x7f09002d
org.levimc.launcher:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f060167
org.levimc.launcher:color/material_personalized_color_secondary_container = 0x7f050295
org.levimc.launcher:attr/divider = 0x7f03017c
org.levimc.launcher:color/m3_sys_color_light_inverse_on_surface = 0x7f0501d0
org.levimc.launcher:color/m3_sys_color_light_background = 0x7f0501cd
org.levimc.launcher:id/open_search_bar_text_view = 0x7f090167
org.levimc.launcher:color/m3_ref_palette_neutral_variant70 = 0x7f050123
org.levimc.launcher:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0501c9
org.levimc.launcher:dimen/abc_text_size_body_2_material = 0x7f060040
org.levimc.launcher:dimen/mtrl_calendar_day_horizontal_padding = 0x7f060278
org.levimc.launcher:attr/controlBackground = 0x7f030149
org.levimc.launcher:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501c8
org.levimc.launcher:color/m3_sys_color_dynamic_primary_fixed = 0x7f0501c7
org.levimc.launcher:attr/nestedScrollable = 0x7f03035d
org.levimc.launcher:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501c4
org.levimc.launcher:id/clip_horizontal = 0x7f090085
org.levimc.launcher:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501c3
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f120259
org.levimc.launcher:attr/alertDialogStyle = 0x7f03002b
org.levimc.launcher:color/m3_sys_color_dynamic_light_tertiary = 0x7f0501bf
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0d0078
org.levimc.launcher:id/src_over = 0x7f0901d0
org.levimc.launcher:color/outline = 0x7f0502f7
org.levimc.launcher:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f060251
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0501be
org.levimc.launcher:style/TextAppearance.Material3.DisplaySmall = 0x7f1201f4
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0501bd
org.levimc.launcher:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600f3
org.levimc.launcher:dimen/material_clock_hand_padding = 0x7f060228
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0501bb
org.levimc.launcher:id/fill_vertical = 0x7f0900cf
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0501b9
org.levimc.launcher:drawable/abc_list_selector_holo_dark = 0x7f070056
org.levimc.launcher:id/action_russian = 0x7f090048
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface_container = 0x7f0501b8
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface = 0x7f0501b6
org.levimc.launcher:color/m3_sys_color_dynamic_light_secondary = 0x7f0501b4
org.levimc.launcher:id/included = 0x7f0900f9
org.levimc.launcher:color/m3_sys_color_dynamic_light_primary_container = 0x7f0501b3
org.levimc.launcher:layout/design_layout_snackbar_include = 0x7f0c0023
org.levimc.launcher:dimen/mtrl_calendar_content_padding = 0x7f060275
org.levimc.launcher:color/m3_sys_color_dynamic_light_primary = 0x7f0501b2
org.levimc.launcher:dimen/splashscreen_icon_size_with_background = 0x7f060321
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0501af
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1203a0
org.levimc.launcher:string/launch_now = 0x7f110066
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0501ae
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0501ad
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_surface = 0x7f0501ac
org.levimc.launcher:style/Widget.Material3.Chip.Suggestion = 0x7f12037b
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0d0071
org.levimc.launcher:integer/m3_sys_motion_duration_extra_long3 = 0x7f0a0012
org.levimc.launcher:attr/windowSplashScreenAnimationDuration = 0x7f0304ec
org.levimc.launcher:color/m3_sys_color_light_on_primary = 0x7f0501d6
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0501ab
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0501a7
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_background = 0x7f0501a5
org.levimc.launcher:color/material_on_primary_emphasis_medium = 0x7f050274
org.levimc.launcher:attr/elevationOverlayAccentColor = 0x7f03019d
org.levimc.launcher:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0501a3
org.levimc.launcher:dimen/mtrl_calendar_header_height_fullscreen = 0x7f060282
org.levimc.launcher:integer/material_motion_duration_long_1 = 0x7f0a0027
org.levimc.launcher:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0501a2
org.levimc.launcher:attr/tabPaddingTop = 0x7f03042d
org.levimc.launcher:color/m3_sys_color_dynamic_light_error_container = 0x7f0501a1
org.levimc.launcher:attr/spinnerDropDownItemStyle = 0x7f0303e4
org.levimc.launcher:id/BOTTOM_END = 0x7f090001
org.levimc.launcher:style/Base.Widget.AppCompat.Toolbar = 0x7f120101
org.levimc.launcher:id/chain2 = 0x7f09007e
org.levimc.launcher:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f05019e
org.levimc.launcher:styleable/KeyTimeCycle = 0x7f130046
org.levimc.launcher:style/Widget.Material3.Slider = 0x7f1203db
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1202d4
org.levimc.launcher:id/open_search_view_background = 0x7f090168
org.levimc.launcher:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
org.levimc.launcher:dimen/m3_extended_fab_icon_padding = 0x7f0601b1
org.levimc.launcher:color/m3_sys_color_dynamic_dark_tertiary = 0x7f05019d
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500a2
org.levimc.launcher:dimen/m3_navigation_item_shape_inset_start = 0x7f0601c4
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f05019c
org.levimc.launcher:id/dragStart = 0x7f0900b5
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f050198
org.levimc.launcher:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f12021e
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface_container = 0x7f050196
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f050195
org.levimc.launcher:style/Widget.AppCompat.ActionBar.TabBar = 0x7f1202f6
org.levimc.launcher:color/m3_sys_color_dynamic_light_background = 0x7f05019f
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070014
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface = 0x7f050194
org.levimc.launcher:color/m3_sys_color_dynamic_dark_secondary = 0x7f050192
org.levimc.launcher:color/m3_sys_color_dynamic_dark_primary = 0x7f050190
org.levimc.launcher:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f05018f
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500a4
org.levimc.launcher:color/m3_sys_color_dynamic_dark_outline = 0x7f05018e
org.levimc.launcher:style/TextAppearance.Design.Suffix = 0x7f1201dc
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f05018c
org.levimc.launcher:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f12026e
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_surface = 0x7f05018a
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f050187
org.levimc.launcher:attr/actionViewClass = 0x7f030025
org.levimc.launcher:attr/textAppearanceLabelSmall = 0x7f030450
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f050185
org.levimc.launcher:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f12037a
org.levimc.launcher:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f120215
org.levimc.launcher:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700e2
org.levimc.launcher:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1202a4
org.levimc.launcher:string/abc_menu_shift_shortcut_label = 0x7f11000e
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_background = 0x7f050183
org.levimc.launcher:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050182
org.levimc.launcher:dimen/mtrl_navigation_rail_elevation = 0x7f0602ce
org.levimc.launcher:macro/m3_comp_snackbar_container_color = 0x7f0d0113
org.levimc.launcher:id/confirm_button = 0x7f09008b
org.levimc.launcher:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
org.levimc.launcher:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f050181
org.levimc.launcher:id/vertical = 0x7f09021c
org.levimc.launcher:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f050180
org.levimc.launcher:color/m3_sys_color_dynamic_dark_error = 0x7f05017e
org.levimc.launcher:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060103
org.levimc.launcher:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0502a4
org.levimc.launcher:styleable/CheckedTextView = 0x7f13001c
org.levimc.launcher:attr/floatingActionButtonPrimaryStyle = 0x7f0301de
org.levimc.launcher:color/m3_sys_color_dark_tertiary_container = 0x7f05017c
org.levimc.launcher:color/material_timepicker_button_stroke = 0x7f0502b4
org.levimc.launcher:style/Widget.MaterialComponents.Chip.Choice = 0x7f120419
org.levimc.launcher:color/m3_sys_color_dark_tertiary = 0x7f05017b
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0d00c7
org.levimc.launcher:color/m3_sys_color_dark_surface_variant = 0x7f05017a
org.levimc.launcher:color/m3_sys_color_dark_surface_dim = 0x7f050179
org.levimc.launcher:color/m3_sys_color_dark_surface_container_low = 0x7f050177
org.levimc.launcher:style/Widget.Material3.Button = 0x7f12035c
org.levimc.launcher:interpolator/mtrl_fast_out_linear_in = 0x7f0b000e
org.levimc.launcher:id/bounceEnd = 0x7f090069
org.levimc.launcher:color/m3_sys_color_dark_surface_container = 0x7f050174
org.levimc.launcher:macro/m3_comp_outlined_text_field_outline_color = 0x7f0d00c3
org.levimc.launcher:color/m3_sys_color_dark_surface = 0x7f050172
org.levimc.launcher:color/m3_sys_color_dark_secondary = 0x7f050170
org.levimc.launcher:color/m3_sys_color_dark_outline = 0x7f05016c
org.levimc.launcher:attr/warmth = 0x7f0304da
org.levimc.launcher:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0601d4
org.levimc.launcher:color/m3_sys_color_dark_on_tertiary_container = 0x7f05016b
org.levimc.launcher:color/m3_sys_color_dark_on_tertiary = 0x7f05016a
org.levimc.launcher:styleable/KeyPosition = 0x7f130045
org.levimc.launcher:color/m3_sys_color_dark_on_secondary = 0x7f050166
org.levimc.launcher:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1200be
org.levimc.launcher:color/m3_sys_color_dark_on_primary_container = 0x7f050165
org.levimc.launcher:id/expanded_menu = 0x7f0900cb
org.levimc.launcher:drawable/m3_tabs_line_indicator = 0x7f0700bb
org.levimc.launcher:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f120148
org.levimc.launcher:dimen/mtrl_calendar_day_height = 0x7f060277
org.levimc.launcher:dimen/m3_comp_fab_primary_large_container_height = 0x7f06011b
org.levimc.launcher:color/m3_sys_color_dark_on_error = 0x7f050162
org.levimc.launcher:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f03046e
org.levimc.launcher:color/m3_sys_color_dark_on_background = 0x7f050161
org.levimc.launcher:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0d0170
org.levimc.launcher:color/design_default_color_primary_dark = 0x7f050047
org.levimc.launcher:color/m3_sys_color_dark_inverse_primary = 0x7f05015f
org.levimc.launcher:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1203ce
org.levimc.launcher:attr/boxCornerRadiusBottomEnd = 0x7f030081
org.levimc.launcher:attr/dividerColor = 0x7f03017d
org.levimc.launcher:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700ce
org.levimc.launcher:color/m3_timepicker_button_ripple_color = 0x7f05020a
org.levimc.launcher:attr/actionButtonStyle = 0x7f03000d
org.levimc.launcher:dimen/m3_comp_navigation_bar_icon_size = 0x7f06013c
org.levimc.launcher:interpolator/mtrl_linear = 0x7f0b0010
org.levimc.launcher:attr/subtitle = 0x7f03040c
org.levimc.launcher:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0501c0
org.levimc.launcher:color/m3_sys_color_dark_error_container = 0x7f05015d
org.levimc.launcher:dimen/abc_dialog_min_width_minor = 0x7f060023
org.levimc.launcher:color/m3_sys_color_dark_error = 0x7f05015c
org.levimc.launcher:drawable/abc_text_select_handle_right_mtrl = 0x7f070070
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f06020e
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0500eb
org.levimc.launcher:color/m3_sys_color_dark_background = 0x7f05015b
org.levimc.launcher:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1200c5
org.levimc.launcher:color/m3_switch_thumb_tint = 0x7f050159
org.levimc.launcher:color/m3_slider_thumb_color_legacy = 0x7f050158
org.levimc.launcher:attr/tabIndicatorAnimationMode = 0x7f030420
org.levimc.launcher:color/m3_slider_thumb_color = 0x7f050157
org.levimc.launcher:styleable/Transform = 0x7f130091
org.levimc.launcher:style/Widget.Material3.Toolbar = 0x7f1203f1
org.levimc.launcher:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f12033a
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0d00d1
org.levimc.launcher:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0d009d
org.levimc.launcher:color/m3_slider_inactive_track_color_legacy = 0x7f050156
org.levimc.launcher:drawable/abc_spinner_mtrl_am_alpha = 0x7f070065
org.levimc.launcher:color/m3_slider_inactive_track_color = 0x7f050155
org.levimc.launcher:color/mtrl_navigation_item_background_color = 0x7f0502d7
org.levimc.launcher:dimen/mtrl_calendar_year_height = 0x7f060297
org.levimc.launcher:color/m3_slider_halo_color_legacy = 0x7f050154
org.levimc.launcher:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
org.levimc.launcher:color/m3_slider_active_track_color = 0x7f050152
org.levimc.launcher:color/m3_selection_control_ripple_color_selector = 0x7f050150
org.levimc.launcher:attr/windowActionBarOverlay = 0x7f0304e2
org.levimc.launcher:color/m3_ref_palette_tertiary99 = 0x7f05014e
org.levimc.launcher:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f120166
org.levimc.launcher:attr/tabRippleColor = 0x7f03042e
org.levimc.launcher:color/m3_ref_palette_tertiary95 = 0x7f05014d
org.levimc.launcher:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700e8
org.levimc.launcher:dimen/mtrl_navigation_rail_active_text_size = 0x7f0602cb
org.levimc.launcher:color/m3_ref_palette_tertiary90 = 0x7f05014c
org.levimc.launcher:style/AppFullScreenTheme = 0x7f12000b
org.levimc.launcher:anim/m3_bottom_sheet_slide_out = 0x7f010022
org.levimc.launcher:attr/colorOnSecondaryFixedVariant = 0x7f030109
org.levimc.launcher:color/m3_ref_palette_tertiary80 = 0x7f05014b
org.levimc.launcher:color/m3_sys_color_dark_surface_container_highest = 0x7f050176
org.levimc.launcher:color/m3_ref_palette_tertiary50 = 0x7f050148
org.levimc.launcher:color/m3_ref_palette_tertiary40 = 0x7f050147
org.levimc.launcher:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700c4
org.levimc.launcher:dimen/abc_action_bar_default_height_material = 0x7f060002
org.levimc.launcher:color/m3_ref_palette_tertiary20 = 0x7f050145
org.levimc.launcher:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f060162
org.levimc.launcher:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0601a8
org.levimc.launcher:color/m3_ref_palette_secondary95 = 0x7f050140
org.levimc.launcher:attr/carousel_nextState = 0x7f0300a8
org.levimc.launcher:dimen/notification_large_icon_width = 0x7f060311
org.levimc.launcher:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a6
org.levimc.launcher:drawable/mtrl_ic_indeterminate = 0x7f0700dc
org.levimc.launcher:style/Theme.SplashScreen.IconBackground = 0x7f120281
org.levimc.launcher:id/frost = 0x7f0900db
org.levimc.launcher:color/m3_ref_palette_secondary50 = 0x7f05013b
org.levimc.launcher:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700c2
org.levimc.launcher:color/m3_ref_palette_secondary20 = 0x7f050138
org.levimc.launcher:color/m3_ref_palette_secondary10 = 0x7f050136
org.levimc.launcher:color/m3_ref_palette_secondary0 = 0x7f050135
org.levimc.launcher:string/mtrl_picker_text_input_year_abbr = 0x7f1100c2
org.levimc.launcher:color/m3_ref_palette_primary99 = 0x7f050134
org.levimc.launcher:color/m3_ref_palette_primary95 = 0x7f050133
org.levimc.launcher:color/m3_ref_palette_primary90 = 0x7f050132
org.levimc.launcher:layout/select_dialog_multichoice_material = 0x7f0c0079
org.levimc.launcher:attr/indicatorInset = 0x7f030245
org.levimc.launcher:attr/checkedTextViewStyle = 0x7f0300bd
org.levimc.launcher:drawable/abc_list_pressed_holo_dark = 0x7f070050
org.levimc.launcher:styleable/ActionMenuItemView = 0x7f130002
org.levimc.launcher:color/m3_ref_palette_primary60 = 0x7f05012f
org.levimc.launcher:attr/drawableSize = 0x7f03018c
org.levimc.launcher:color/m3_ref_palette_primary40 = 0x7f05012d
org.levimc.launcher:attr/subtitleCentered = 0x7f03040d
org.levimc.launcher:attr/yearTodayStyle = 0x7f0304f1
org.levimc.launcher:color/m3_ref_palette_primary30 = 0x7f05012c
org.levimc.launcher:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f12038e
org.levimc.launcher:dimen/m3_btn_max_width = 0x7f0600d8
org.levimc.launcher:color/m3_ref_palette_primary100 = 0x7f05012a
org.levimc.launcher:color/notification_action_color_filter = 0x7f0502ef
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Caption = 0x7f12001d
org.levimc.launcher:color/m3_ref_palette_primary10 = 0x7f050129
org.levimc.launcher:layout/design_layout_snackbar = 0x7f0c0022
org.levimc.launcher:attr/errorAccessibilityLiveRegion = 0x7f0301ae
org.levimc.launcher:color/material_grey_100 = 0x7f050264
org.levimc.launcher:color/m3_ref_palette_neutral_variant95 = 0x7f050126
org.levimc.launcher:color/m3_ref_palette_neutral_variant90 = 0x7f050125
org.levimc.launcher:macro/m3_comp_outlined_card_outline_color = 0x7f0d00ae
org.levimc.launcher:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07005f
org.levimc.launcher:color/material_dynamic_neutral_variant30 = 0x7f050234
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0d0160
org.levimc.launcher:color/m3_tabs_icon_color = 0x7f0501fb
org.levimc.launcher:color/m3_ref_palette_neutral_variant40 = 0x7f050120
org.levimc.launcher:dimen/abc_text_size_display_2_material = 0x7f060044
org.levimc.launcher:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000c
org.levimc.launcher:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f12026c
org.levimc.launcher:color/mtrl_indicator_text_color = 0x7f0502d2
org.levimc.launcher:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f050098
org.levimc.launcher:color/m3_ref_palette_neutral_variant20 = 0x7f05011e
org.levimc.launcher:id/with_icon = 0x7f090228
org.levimc.launcher:id/position = 0x7f090184
org.levimc.launcher:color/m3_ref_palette_neutral_variant100 = 0x7f05011d
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1202d0
org.levimc.launcher:color/m3_ref_palette_neutral_variant0 = 0x7f05011b
org.levimc.launcher:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602b4
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1202e2
org.levimc.launcher:string/abc_capital_off = 0x7f110006
org.levimc.launcher:dimen/mtrl_calendar_day_width = 0x7f06027b
org.levimc.launcher:color/m3_ref_palette_neutral99 = 0x7f05011a
org.levimc.launcher:color/m3_ref_palette_neutral98 = 0x7f050119
org.levimc.launcher:color/secondary = 0x7f050303
org.levimc.launcher:color/m3_ref_palette_neutral96 = 0x7f050118
org.levimc.launcher:color/m3_ref_palette_neutral95 = 0x7f050117
org.levimc.launcher:macro/m3_comp_filled_icon_button_container_color = 0x7f0d0048
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500ab
org.levimc.launcher:color/m3_ref_palette_neutral94 = 0x7f050116
org.levimc.launcher:dimen/m3_sys_elevation_level2 = 0x7f0601f2
org.levimc.launcher:attr/colorOnTertiaryFixedVariant = 0x7f030110
org.levimc.launcher:color/m3_ref_palette_neutral92 = 0x7f050115
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1203ab
org.levimc.launcher:color/m3_ref_palette_neutral80 = 0x7f050112
org.levimc.launcher:color/m3_ref_palette_neutral70 = 0x7f050111
org.levimc.launcher:color/mtrl_filled_background_color = 0x7f0502cf
org.levimc.launcher:color/m3_ref_palette_neutral60 = 0x7f050110
org.levimc.launcher:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f120308
org.levimc.launcher:dimen/m3_comp_fab_primary_small_container_height = 0x7f06011f
org.levimc.launcher:color/m3_ref_palette_neutral6 = 0x7f05010f
org.levimc.launcher:attr/behavior_skipCollapsed = 0x7f030072
org.levimc.launcher:color/m3_ref_palette_neutral50 = 0x7f05010e
org.levimc.launcher:styleable/TabItem = 0x7f130088
org.levimc.launcher:color/m3_ref_palette_neutral30 = 0x7f05010b
org.levimc.launcher:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f12020f
org.levimc.launcher:color/m3_ref_palette_neutral24 = 0x7f05010a
org.levimc.launcher:color/m3_ref_palette_neutral0 = 0x7f050103
org.levimc.launcher:color/m3_timepicker_secondary_text_button_text_color = 0x7f050211
org.levimc.launcher:attr/cornerSizeTopLeft = 0x7f030155
org.levimc.launcher:color/m3_ref_palette_error99 = 0x7f050102
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1202db
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0d0081
org.levimc.launcher:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
org.levimc.launcher:color/m3_ref_palette_error90 = 0x7f050100
org.levimc.launcher:style/TextAppearance.Compat.Notification.Info = 0x7f1201cf
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500a9
org.levimc.launcher:color/m3_ref_palette_error80 = 0x7f0500ff
org.levimc.launcher:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f120096
org.levimc.launcher:attr/itemVerticalPadding = 0x7f03026b
org.levimc.launcher:color/m3_ref_palette_error70 = 0x7f0500fe
org.levimc.launcher:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1200ab
org.levimc.launcher:color/mtrl_filled_stroke_color = 0x7f0502d1
org.levimc.launcher:color/m3_ref_palette_error50 = 0x7f0500fc
org.levimc.launcher:attr/rangeFillColor = 0x7f03039b
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_container_height = 0x7f06010d
org.levimc.launcher:style/Widget.AppCompat.PopupWindow = 0x7f12032b
org.levimc.launcher:color/m3_ref_palette_error30 = 0x7f0500fa
org.levimc.launcher:dimen/m3_comp_filter_chip_container_height = 0x7f06012b
org.levimc.launcher:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0601bf
org.levimc.launcher:color/m3_ref_palette_error20 = 0x7f0500f9
org.levimc.launcher:color/m3_ref_palette_error100 = 0x7f0500f8
org.levimc.launcher:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0d004f
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0500f0
org.levimc.launcher:dimen/m3_badge_with_text_vertical_offset = 0x7f0600ba
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0500ed
org.levimc.launcher:string/install = 0x7f11005b
org.levimc.launcher:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f060193
org.levimc.launcher:color/m3_sys_color_dynamic_dark_error_container = 0x7f05017f
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0500ea
org.levimc.launcher:attr/materialCardViewStyle = 0x7f0302f8
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0500e9
org.levimc.launcher:style/Widget.Material3.SearchView.Prefix = 0x7f1203d5
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary90 = 0x7f0500e6
org.levimc.launcher:id/listMode = 0x7f09010d
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary70 = 0x7f0500e4
org.levimc.launcher:attr/passwordToggleDrawable = 0x7f030377
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary40 = 0x7f0500e1
org.levimc.launcher:attr/grid_columnWeights = 0x7f03020e
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary100 = 0x7f0500de
org.levimc.launcher:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f120175
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary0 = 0x7f0500dc
org.levimc.launcher:id/spacer = 0x7f0901c4
org.levimc.launcher:attr/flow_firstHorizontalStyle = 0x7f0301e9
org.levimc.launcher:color/button_material_light = 0x7f050029
org.levimc.launcher:color/material_dynamic_tertiary50 = 0x7f05025d
org.levimc.launcher:color/m3_dynamic_dark_highlighted_text = 0x7f05007f
org.levimc.launcher:color/m3_ref_palette_dynamic_primary90 = 0x7f0500d9
org.levimc.launcher:color/m3_ref_palette_dynamic_primary50 = 0x7f0500d5
org.levimc.launcher:style/TextAppearance.Design.Snackbar.Message = 0x7f1201db
org.levimc.launcher:drawable/design_password_eye = 0x7f070091
org.levimc.launcher:attr/singleSelection = 0x7f0303dc
org.levimc.launcher:styleable/RangeSlider = 0x7f130074
org.levimc.launcher:color/m3_ref_palette_dynamic_primary40 = 0x7f0500d4
org.levimc.launcher:styleable/TextInputEditText = 0x7f13008c
org.levimc.launcher:string/call_notification_hang_up_action = 0x7f11002a
org.levimc.launcher:id/clockwise = 0x7f090087
org.levimc.launcher:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
org.levimc.launcher:macro/m3_comp_date_picker_modal_container_shape = 0x7f0d000e
org.levimc.launcher:color/m3_ref_palette_dynamic_primary100 = 0x7f0500d1
org.levimc.launcher:macro/m3_comp_filter_chip_label_text_type = 0x7f0d0058
org.levimc.launcher:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06012c
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500ce
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f12042d
org.levimc.launcher:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0d0091
org.levimc.launcher:attr/textAppearanceHeadlineLarge = 0x7f03044b
org.levimc.launcher:attr/editTextStyle = 0x7f03019b
org.levimc.launcher:style/TextAppearance.AppCompat.Medium = 0x7f1201af
org.levimc.launcher:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f060171
org.levimc.launcher:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1200eb
org.levimc.launcher:attr/circularflow_radiusInDP = 0x7f0300d7
org.levimc.launcher:dimen/fastscroll_minimum_range = 0x7f060092
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0500c9
org.levimc.launcher:color/m3_sys_color_light_on_surface = 0x7f0501da
org.levimc.launcher:macro/m3_comp_extended_fab_primary_container_color = 0x7f0d002c
org.levimc.launcher:attr/backHandlingEnabled = 0x7f030046
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500c2
org.levimc.launcher:dimen/notification_content_margin_start = 0x7f06030f
org.levimc.launcher:attr/buttonStyleSmall = 0x7f030097
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500c1
org.levimc.launcher:color/m3_ref_palette_tertiary100 = 0x7f050144
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Subhead = 0x7f120030
org.levimc.launcher:attr/chainUseRtl = 0x7f0300ae
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500bf
org.levimc.launcher:dimen/mtrl_slider_label_square_side = 0x7f0602e8
org.levimc.launcher:attr/allowStacking = 0x7f03002d
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0500be
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0500bb
org.levimc.launcher:attr/endIconContentDescription = 0x7f0301a3
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0500ba
org.levimc.launcher:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002
org.levimc.launcher:anim/m3_side_sheet_exit_to_right = 0x7f010028
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500ae
org.levimc.launcher:attr/materialAlertDialogTitleTextStyle = 0x7f0302e2
org.levimc.launcher:string/mtrl_picker_navigate_to_current_year_description = 0x7f1100b3
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500ac
org.levimc.launcher:string/call_notification_answer_action = 0x7f110027
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500a5
org.levimc.launcher:dimen/material_clock_number_text_size = 0x7f06022a
org.levimc.launcher:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1100c6
org.levimc.launcher:string/material_motion_easing_emphasized = 0x7f110081
org.levimc.launcher:attr/buttonIconDimen = 0x7f030092
org.levimc.launcher:attr/switchTextAppearance = 0x7f030418
org.levimc.launcher:dimen/m3_comp_outlined_text_field_outline_width = 0x7f060159
org.levimc.launcher:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0d011c
org.levimc.launcher:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f060246
org.levimc.launcher:color/material_dynamic_color_dark_error_container = 0x7f05021c
org.levimc.launcher:style/ThemeOverlay.MaterialComponents = 0x7f1202cc
org.levimc.launcher:attr/elevation = 0x7f03019c
org.levimc.launcher:color/material_personalized_color_on_tertiary_container = 0x7f05028c
org.levimc.launcher:attr/helperTextTextColor = 0x7f030221
org.levimc.launcher:color/m3_sys_color_light_primary_container = 0x7f0501e1
org.levimc.launcher:style/ThemeOverlay.Material3.BottomAppBar = 0x7f120293
org.levimc.launcher:id/accessibility_custom_action_31 = 0x7f09002c
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral10 = 0x7f0500a0
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f120041
org.levimc.launcher:color/m3_ref_palette_black = 0x7f05009e
org.levimc.launcher:dimen/m3_appbar_size_medium = 0x7f0600ab
org.levimc.launcher:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1203d1
org.levimc.launcher:attr/materialClockStyle = 0x7f0302fa
org.levimc.launcher:attr/startIconMinSize = 0x7f0303f3
org.levimc.launcher:attr/startIconScaleType = 0x7f0303f4
org.levimc.launcher:color/m3_radiobutton_button_tint = 0x7f05009c
org.levimc.launcher:color/m3_ref_palette_neutral87 = 0x7f050113
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500a8
org.levimc.launcher:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f120188
org.levimc.launcher:dimen/material_time_picker_minimum_screen_width = 0x7f060245
org.levimc.launcher:attr/textAppearanceTitleLarge = 0x7f03045d
org.levimc.launcher:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300ee
org.levimc.launcher:layout/material_textinput_timepicker = 0x7f0c004c
org.levimc.launcher:id/graph_wrap = 0x7f0900e2
org.levimc.launcher:dimen/m3_chip_elevated_elevation = 0x7f0600f7
org.levimc.launcher:color/design_default_color_secondary = 0x7f050049
org.levimc.launcher:attr/maxActionInlineWidth = 0x7f03030d
org.levimc.launcher:color/m3_popupmenu_overlay_color = 0x7f05009a
org.levimc.launcher:style/Base.v21.Theme.SplashScreen = 0x7f120126
org.levimc.launcher:color/m3_navigation_rail_ripple_color_selector = 0x7f050099
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0d007f
org.levimc.launcher:attr/isMaterial3DynamicColorApplied = 0x7f03024b
org.levimc.launcher:color/m3_navigation_item_ripple_color = 0x7f050095
org.levimc.launcher:integer/mtrl_btn_anim_duration_ms = 0x7f0a0030
org.levimc.launcher:attr/badgeWithTextHeight = 0x7f03005e
org.levimc.launcher:attr/buttonPanelSideLayout = 0x7f030095
org.levimc.launcher:id/btn_action = 0x7f09006b
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_primary = 0x7f0501a8
org.levimc.launcher:layout/abc_alert_dialog_title_material = 0x7f0c000a
org.levimc.launcher:color/m3_fab_ripple_color_selector = 0x7f05008b
org.levimc.launcher:dimen/m3_card_elevated_disabled_z = 0x7f0600e6
org.levimc.launcher:dimen/notification_top_pad = 0x7f060319
org.levimc.launcher:attr/backgroundInsetBottom = 0x7f030049
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500b1
org.levimc.launcher:drawable/card_background = 0x7f07008b
org.levimc.launcher:attr/popupMenuBackground = 0x7f030388
org.levimc.launcher:attr/fabCradleRoundedCornerRadius = 0x7f0301cf
org.levimc.launcher:attr/flow_lastHorizontalStyle = 0x7f0301f1
org.levimc.launcher:color/m3_dynamic_primary_text_disable_only = 0x7f050086
org.levimc.launcher:attr/prefixTextColor = 0x7f03038f
org.levimc.launcher:attr/suffixTextAppearance = 0x7f030412
org.levimc.launcher:color/m3_dynamic_dark_primary_text_disable_only = 0x7f050081
org.levimc.launcher:attr/maxLines = 0x7f030312
org.levimc.launcher:attr/checkedIconGravity = 0x7f0300b7
org.levimc.launcher:attr/thumbIconTintMode = 0x7f030483
org.levimc.launcher:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f120087
org.levimc.launcher:color/m3_dynamic_dark_default_color_secondary_text = 0x7f05007e
org.levimc.launcher:animator/fragment_close_exit = 0x7f020004
org.levimc.launcher:color/m3_dynamic_dark_default_color_primary_text = 0x7f05007d
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Body2 = 0x7f12001b
org.levimc.launcher:integer/mtrl_calendar_header_orientation = 0x7f0a0031
org.levimc.launcher:attr/tabPaddingStart = 0x7f03042c
org.levimc.launcher:attr/useMaterialThemeColors = 0x7f0304cf
org.levimc.launcher:color/m3_default_color_secondary_text = 0x7f05007c
org.levimc.launcher:color/m3_dark_hint_foreground = 0x7f050079
org.levimc.launcher:dimen/mtrl_fab_translation_z_pressed = 0x7f0602b9
org.levimc.launcher:id/mod_switch = 0x7f090130
org.levimc.launcher:color/m3_dark_default_color_secondary_text = 0x7f050077
org.levimc.launcher:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0d0173
org.levimc.launcher:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0301e1
org.levimc.launcher:color/m3_dark_default_color_primary_text = 0x7f050076
org.levimc.launcher:color/m3_timepicker_button_text_color = 0x7f05020b
org.levimc.launcher:id/easeOut = 0x7f0900bb
org.levimc.launcher:color/m3_sys_color_dark_primary = 0x7f05016e
org.levimc.launcher:style/TextAppearance.MaterialComponents.Headline5 = 0x7f12020c
org.levimc.launcher:attr/grid_useRtl = 0x7f030216
org.levimc.launcher:color/m3_chip_ripple_color = 0x7f050073
org.levimc.launcher:dimen/mtrl_btn_padding_top = 0x7f060268
org.levimc.launcher:dimen/m3_alert_dialog_icon_size = 0x7f0600a2
org.levimc.launcher:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1202ba
org.levimc.launcher:style/ShapeAppearance.Material3.Corner.Medium = 0x7f12017d
org.levimc.launcher:color/m3_chip_assist_text_color = 0x7f050071
org.levimc.launcher:macro/m3_comp_fab_surface_icon_color = 0x7f0d003e
org.levimc.launcher:layout/mtrl_alert_select_dialog_multichoice = 0x7f0c0056
org.levimc.launcher:color/m3_card_stroke_color = 0x7f05006e
org.levimc.launcher:string/mtrl_picker_range_header_only_start_selected = 0x7f1100b7
org.levimc.launcher:attr/homeAsUpIndicator = 0x7f03022b
org.levimc.launcher:color/m3_card_foreground_color = 0x7f05006c
org.levimc.launcher:string/russian = 0x7f1100ef
org.levimc.launcher:string/mtrl_picker_text_input_month_abbr = 0x7f1100c1
org.levimc.launcher:dimen/mtrl_btn_disabled_z = 0x7f06025c
org.levimc.launcher:color/m3_button_ripple_color_selector = 0x7f050069
org.levimc.launcher:color/m3_button_foreground_color_selector = 0x7f050066
org.levimc.launcher:color/m3_timepicker_display_background_color = 0x7f05020d
org.levimc.launcher:attr/windowActionBar = 0x7f0304e1
org.levimc.launcher:color/m3_bottom_sheet_drag_handle_color = 0x7f050064
org.levimc.launcher:dimen/m3_btn_elevation = 0x7f0600d0
org.levimc.launcher:dimen/m3_comp_elevated_card_container_elevation = 0x7f06010a
org.levimc.launcher:dimen/material_clock_hand_center_dot_radius = 0x7f060227
org.levimc.launcher:attr/viewTransitionOnNegativeCross = 0x7f0304d6
org.levimc.launcher:color/m3_assist_chip_stroke_color = 0x7f050063
org.levimc.launcher:id/wrap = 0x7f09022a
org.levimc.launcher:dimen/notification_right_icon_size = 0x7f060314
org.levimc.launcher:attr/colorSurfaceContainerLowest = 0x7f030126
org.levimc.launcher:color/m3_highlighted_text = 0x7f05008d
org.levimc.launcher:drawable/abc_btn_radio_material = 0x7f070031
org.levimc.launcher:id/wrap_content_constrained = 0x7f09022c
org.levimc.launcher:drawable/$m3_avd_show_password__2 = 0x7f07000b
org.levimc.launcher:color/material_dynamic_primary100 = 0x7f05023f
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary10 = 0x7f0500dd
org.levimc.launcher:color/m3_assist_chip_icon_tint_color = 0x7f050062
org.levimc.launcher:id/pressed = 0x7f090186
org.levimc.launcher:color/highlighted_text_material_dark = 0x7f05005f
org.levimc.launcher:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1200f5
org.levimc.launcher:id/fill_horizontal = 0x7f0900ce
org.levimc.launcher:attr/layout_anchor = 0x7f03027a
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f12039f
org.levimc.launcher:string/mtrl_picker_day_of_week_column_header = 0x7f1100ad
org.levimc.launcher:color/foreground_material_dark = 0x7f05005d
org.levimc.launcher:attr/bottomAppBarStyle = 0x7f030078
org.levimc.launcher:attr/lineSpacing = 0x7f0302c4
org.levimc.launcher:color/error_color_material_light = 0x7f05005c
org.levimc.launcher:animator/mtrl_btn_state_list_anim = 0x7f020015
org.levimc.launcher:color/dim_foreground_disabled_material_dark = 0x7f050056
org.levimc.launcher:color/design_fab_stroke_top_inner_color = 0x7f050052
org.levimc.launcher:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f120429
org.levimc.launcher:attr/cornerSize = 0x7f030152
org.levimc.launcher:attr/textInputOutlinedStyle = 0x7f03046f
org.levimc.launcher:color/design_fab_stroke_end_outer_color = 0x7f050051
org.levimc.launcher:attr/mock_showDiagonals = 0x7f030324
org.levimc.launcher:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f06019c
org.levimc.launcher:color/design_snackbar_background_color = 0x7f050055
org.levimc.launcher:string/font_license = 0x7f110052
org.levimc.launcher:string/error_versions = 0x7f110047
org.levimc.launcher:anim/abc_slide_in_bottom = 0x7f010006
org.levimc.launcher:dimen/m3_searchview_height = 0x7f0601e4
org.levimc.launcher:color/design_fab_shadow_start_color = 0x7f05004f
org.levimc.launcher:style/Platform.ThemeOverlay.AppCompat = 0x7f120147
org.levimc.launcher:style/Base.Widget.MaterialComponents.Chip = 0x7f12011a
org.levimc.launcher:color/design_default_color_primary_variant = 0x7f050048
org.levimc.launcher:color/design_default_color_on_primary = 0x7f050043
org.levimc.launcher:color/design_dark_default_color_surface = 0x7f05003e
org.levimc.launcher:color/m3_sys_color_light_tertiary_container = 0x7f0501ee
org.levimc.launcher:style/Widget.Material3.BottomAppBar = 0x7f120353
org.levimc.launcher:dimen/mtrl_progress_circular_inset = 0x7f0602d4
org.levimc.launcher:attr/layout_constraintWidth_default = 0x7f0302a9
org.levimc.launcher:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0d001b
org.levimc.launcher:attr/layout_constraintBaseline_toTopOf = 0x7f030284
org.levimc.launcher:color/design_dark_default_color_primary = 0x7f050039
org.levimc.launcher:dimen/m3_navigation_item_horizontal_padding = 0x7f0601c0
org.levimc.launcher:color/m3_sys_color_light_inverse_surface = 0x7f0501d2
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1202ed
org.levimc.launcher:attr/itemHorizontalTranslationEnabled = 0x7f030252
org.levimc.launcher:color/design_dark_default_color_error = 0x7f050033
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0d0070
org.levimc.launcher:color/design_dark_default_color_background = 0x7f050032
org.levimc.launcher:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f1203fc
org.levimc.launcher:color/design_bottom_navigation_shadow_color = 0x7f050030
org.levimc.launcher:color/m3_sys_color_light_surface_container = 0x7f0501e6
org.levimc.launcher:color/cardview_shadow_end_color = 0x7f05002e
org.levimc.launcher:string/material_slider_range_start = 0x7f110085
org.levimc.launcher:color/abc_btn_colored_text_material = 0x7f050003
org.levimc.launcher:color/error_color_material_dark = 0x7f05005b
org.levimc.launcher:color/call_notification_answer_color = 0x7f05002a
org.levimc.launcher:color/bright_foreground_material_light = 0x7f050027
org.levimc.launcher:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1200d0
org.levimc.launcher:id/asConfigured = 0x7f090058
org.levimc.launcher:attr/perpendicularPath_percent = 0x7f030381
org.levimc.launcher:color/bright_foreground_disabled_material_light = 0x7f050023
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f120035
org.levimc.launcher:layout/activity_main = 0x7f0c001c
org.levimc.launcher:color/material_blue_grey_950 = 0x7f050216
org.levimc.launcher:color/bright_foreground_disabled_material_dark = 0x7f050022
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0d008b
org.levimc.launcher:attr/colorTertiaryFixedDim = 0x7f03012e
org.levimc.launcher:attr/percentX = 0x7f03037f
org.levimc.launcher:color/background_floating_material_light = 0x7f05001f
org.levimc.launcher:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0602a7
org.levimc.launcher:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f12006e
org.levimc.launcher:color/background_floating_material_dark = 0x7f05001e
org.levimc.launcher:color/secondary_text_default_material_dark = 0x7f050304
org.levimc.launcher:color/material_personalized_color_on_surface_inverse = 0x7f050289
org.levimc.launcher:color/accent_material_light = 0x7f05001a
org.levimc.launcher:style/TextAppearance.Design.Prefix = 0x7f1201da
org.levimc.launcher:attr/carousel_touchUp_velocityThreshold = 0x7f0300ac
org.levimc.launcher:attr/materialDividerStyle = 0x7f0302fd
org.levimc.launcher:attr/materialTimePickerStyle = 0x7f030309
org.levimc.launcher:color/accent_material_dark = 0x7f050019
org.levimc.launcher:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f120173
org.levimc.launcher:dimen/abc_text_size_large_material = 0x7f060048
org.levimc.launcher:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600a8
org.levimc.launcher:color/abc_tint_switch_track = 0x7f050018
org.levimc.launcher:drawable/$avd_hide_password__1 = 0x7f070001
org.levimc.launcher:style/TextAppearance.Material3.DisplayMedium = 0x7f1201f3
org.levimc.launcher:id/embed = 0x7f0900c3
org.levimc.launcher:attr/listLayout = 0x7f0302cb
org.levimc.launcher:dimen/mtrl_calendar_action_height = 0x7f060272
org.levimc.launcher:dimen/abc_disabled_alpha_material_dark = 0x7f060027
org.levimc.launcher:color/abc_tint_spinner = 0x7f050017
org.levimc.launcher:layout/alert_dialog_custom = 0x7f0c001e
org.levimc.launcher:attr/path_percent = 0x7f03037c
org.levimc.launcher:color/material_personalized_hint_foreground_inverse = 0x7f0502aa
org.levimc.launcher:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0d013c
org.levimc.launcher:drawable/ic_launch = 0x7f0700a5
org.levimc.launcher:color/abc_tint_seek_thumb = 0x7f050016
org.levimc.launcher:color/abc_tint_default = 0x7f050014
org.levimc.launcher:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0d0011
org.levimc.launcher:color/abc_secondary_text_material_light = 0x7f050012
org.levimc.launcher:attr/textAppearanceListItemSmall = 0x7f030455
org.levimc.launcher:drawable/notification_action_background = 0x7f0700ee
org.levimc.launcher:style/MaterialAlertDialog.Material3.Animation = 0x7f12012e
org.levimc.launcher:color/abc_search_url_text_pressed = 0x7f05000f
org.levimc.launcher:color/abc_primary_text_material_light = 0x7f05000c
org.levimc.launcher:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0d00af
org.levimc.launcher:dimen/mtrl_btn_elevation = 0x7f06025d
org.levimc.launcher:id/right_side = 0x7f090195
org.levimc.launcher:color/abc_primary_text_disable_only_material_dark = 0x7f050009
org.levimc.launcher:attr/clearsTag = 0x7f0300d9
org.levimc.launcher:styleable/ScrimInsetsFrameLayout = 0x7f130077
org.levimc.launcher:attr/fontProviderQuery = 0x7f030202
org.levimc.launcher:color/abc_color_highlight_material = 0x7f050004
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_error = 0x7f050184
org.levimc.launcher:drawable/$m3_avd_hide_password__1 = 0x7f070007
org.levimc.launcher:color/abc_btn_colored_borderless_text_material = 0x7f050002
org.levimc.launcher:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0d00c2
org.levimc.launcher:dimen/highlight_alpha_material_colored = 0x7f060093
org.levimc.launcher:anim/m3_bottom_sheet_slide_in = 0x7f010021
org.levimc.launcher:attr/curveFit = 0x7f030161
org.levimc.launcher:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
org.levimc.launcher:layout/mtrl_picker_header_selection_text = 0x7f0c006b
org.levimc.launcher:bool/abc_action_bar_embed_tabs = 0x7f040000
org.levimc.launcher:attr/errorIconDrawable = 0x7f0301b1
org.levimc.launcher:attr/carousel_touchUp_dampeningFactor = 0x7f0300ab
org.levimc.launcher:dimen/m3_comp_slider_active_handle_height = 0x7f060181
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f120036
org.levimc.launcher:color/m3_ref_palette_error0 = 0x7f0500f6
org.levimc.launcher:style/Widget.Material3.NavigationRailView.Badge = 0x7f1203ca
org.levimc.launcher:attr/layout_constraintBottom_toBottomOf = 0x7f030286
org.levimc.launcher:attr/windowNoTitle = 0x7f0304ea
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1203c7
org.levimc.launcher:attr/elevationOverlayEnabled = 0x7f03019f
org.levimc.launcher:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
org.levimc.launcher:style/TextAppearance.MaterialComponents.Tooltip = 0x7f120212
org.levimc.launcher:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f12015f
org.levimc.launcher:attr/windowFixedHeightMinor = 0x7f0304e5
org.levimc.launcher:style/Theme.Material3.Dark.NoActionBar = 0x7f120238
org.levimc.launcher:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f060146
org.levimc.launcher:attr/staggered = 0x7f0303ef
org.levimc.launcher:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f120323
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0500ca
org.levimc.launcher:attr/windowActionModeOverlay = 0x7f0304e3
org.levimc.launcher:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0d00df
org.levimc.launcher:attr/layout_wrapBehaviorInParent = 0x7f0302be
org.levimc.launcher:attr/waveVariesBy = 0x7f0304e0
org.levimc.launcher:attr/wavePhase = 0x7f0304de
org.levimc.launcher:color/design_error = 0x7f05004c
org.levimc.launcher:styleable/Tooltip = 0x7f130090
org.levimc.launcher:drawable/$avd_show_password__1 = 0x7f070004
org.levimc.launcher:attr/listPreferredItemPaddingRight = 0x7f0302d3
org.levimc.launcher:attr/voiceIcon = 0x7f0304d9
org.levimc.launcher:string/mtrl_switch_thumb_path_unchecked = 0x7f1100cd
org.levimc.launcher:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0d00bf
org.levimc.launcher:id/tag_accessibility_actions = 0x7f0901df
org.levimc.launcher:id/mod_name = 0x7f09012e
org.levimc.launcher:attr/scaleFromTextSize = 0x7f0303af
org.levimc.launcher:attr/viewInflaterClass = 0x7f0304d3
org.levimc.launcher:attr/titleMargin = 0x7f03049c
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0500c7
org.levimc.launcher:attr/motionDurationShort3 = 0x7f030335
org.levimc.launcher:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0d0018
org.levimc.launcher:attr/listMenuViewStyle = 0x7f0302cc
org.levimc.launcher:attr/useDrawerArrowDrawable = 0x7f0304ce
org.levimc.launcher:attr/triggerSlack = 0x7f0304ca
org.levimc.launcher:attr/constraintSetStart = 0x7f030134
org.levimc.launcher:attr/title = 0x7f030498
org.levimc.launcher:attr/logo = 0x7f0302d5
org.levimc.launcher:styleable/AppCompatTextView = 0x7f130011
org.levimc.launcher:style/Widget.AppCompat.ActionBar.TabView = 0x7f1202f8
org.levimc.launcher:attr/tabTextAppearance = 0x7f030433
org.levimc.launcher:attr/triggerId = 0x7f0304c8
org.levimc.launcher:attr/textLocale = 0x7f030471
org.levimc.launcher:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070033
org.levimc.launcher:attr/colorBackgroundFloating = 0x7f0300f4
org.levimc.launcher:id/material_value_index = 0x7f090128
org.levimc.launcher:attr/actionBarTabBarStyle = 0x7f030008
org.levimc.launcher:id/snackbar_action = 0x7f0901bf
org.levimc.launcher:attr/transitionPathRotate = 0x7f0304c6
org.levimc.launcher:macro/m3_comp_fab_secondary_container_color = 0x7f0d003b
org.levimc.launcher:attr/sliderStyle = 0x7f0303de
org.levimc.launcher:attr/transitionFlags = 0x7f0304c5
org.levimc.launcher:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f060255
org.levimc.launcher:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0600fb
org.levimc.launcher:color/abc_hint_foreground_material_dark = 0x7f050007
org.levimc.launcher:color/material_dynamic_neutral95 = 0x7f05022e
org.levimc.launcher:attr/transitionEasing = 0x7f0304c4
org.levimc.launcher:attr/transitionDisable = 0x7f0304c3
org.levimc.launcher:string/bottomsheet_action_expand_halfway = 0x7f110024
org.levimc.launcher:attr/trackTintMode = 0x7f0304c1
org.levimc.launcher:attr/touchRegionId = 0x7f0304b3
org.levimc.launcher:dimen/m3_btn_stroke_size = 0x7f0600dd
org.levimc.launcher:drawable/navigation_empty_icon = 0x7f0700ed
org.levimc.launcher:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f12014f
org.levimc.launcher:attr/tabMinWidth = 0x7f030427
org.levimc.launcher:attr/layout_scrollInterpolator = 0x7f0302bd
org.levimc.launcher:attr/trackTint = 0x7f0304c0
org.levimc.launcher:style/Base.Widget.Material3.CardView = 0x7f120107
org.levimc.launcher:dimen/m3_comp_slider_stop_indicator_size = 0x7f060188
org.levimc.launcher:attr/trackThickness = 0x7f0304bf
org.levimc.launcher:color/m3_ref_palette_secondary60 = 0x7f05013c
org.levimc.launcher:dimen/mtrl_toolbar_default_height = 0x7f060305
org.levimc.launcher:dimen/abc_dialog_padding_material = 0x7f060024
org.levimc.launcher:dimen/mtrl_btn_text_btn_padding_right = 0x7f06026e
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary95 = 0x7f0500e7
org.levimc.launcher:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
org.levimc.launcher:attr/flow_maxElementsWrap = 0x7f0301f4
org.levimc.launcher:id/accessibility_custom_action_23 = 0x7f090023
org.levimc.launcher:attr/trackInsideCornerSize = 0x7f0304bd
org.levimc.launcher:styleable/NavigationRailView = 0x7f13006c
org.levimc.launcher:attr/transformPivotTarget = 0x7f0304c2
org.levimc.launcher:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060186
org.levimc.launcher:style/Theme.MaterialComponents.Dialog.Alert = 0x7f120265
org.levimc.launcher:color/design_default_color_on_surface = 0x7f050045
org.levimc.launcher:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f120095
org.levimc.launcher:attr/trackCornerRadius = 0x7f0304b8
org.levimc.launcher:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1202b3
org.levimc.launcher:attr/trackColorActive = 0x7f0304b6
org.levimc.launcher:dimen/m3_extended_fab_end_padding = 0x7f0601b0
org.levimc.launcher:attr/trackColor = 0x7f0304b5
org.levimc.launcher:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0602d2
org.levimc.launcher:attr/track = 0x7f0304b4
org.levimc.launcher:attr/gapBetweenBars = 0x7f03020b
org.levimc.launcher:attr/flow_lastHorizontalBias = 0x7f0301f0
org.levimc.launcher:attr/touchAnchorSide = 0x7f0304b2
org.levimc.launcher:color/design_fab_shadow_mid_color = 0x7f05004e
org.levimc.launcher:color/material_personalized_color_outline_variant = 0x7f05028e
org.levimc.launcher:attr/materialCalendarStyle = 0x7f0302f2
org.levimc.launcher:color/m3_ref_palette_tertiary0 = 0x7f050142
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f12018f
org.levimc.launcher:attr/spinnerStyle = 0x7f0303e5
org.levimc.launcher:attr/topInsetScrimEnabled = 0x7f0304b0
org.levimc.launcher:attr/duration = 0x7f030197
org.levimc.launcher:drawable/abc_btn_default_mtrl_shape = 0x7f070030
org.levimc.launcher:color/m3_dynamic_dark_hint_foreground = 0x7f050080
org.levimc.launcher:attr/tooltipStyle = 0x7f0304ae
org.levimc.launcher:attr/tabIndicator = 0x7f03041e
org.levimc.launcher:attr/tooltipForegroundColor = 0x7f0304ac
org.levimc.launcher:macro/m3_comp_top_app_bar_small_container_color = 0x7f0d016f
org.levimc.launcher:dimen/m3_chip_corner_size = 0x7f0600f4
org.levimc.launcher:color/abc_background_cache_hint_selector_material_light = 0x7f050001
org.levimc.launcher:attr/lineHeight = 0x7f0302c3
org.levimc.launcher:attr/toolbarStyle = 0x7f0304aa
org.levimc.launcher:attr/compatShadowEnabled = 0x7f030130
org.levimc.launcher:color/m3_calendar_item_disabled_text = 0x7f05006a
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060114
org.levimc.launcher:color/design_default_color_background = 0x7f05003f
org.levimc.launcher:attr/grid_verticalGaps = 0x7f030218
org.levimc.launcher:dimen/notification_action_icon_size = 0x7f06030c
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f12030f
org.levimc.launcher:id/github_icon = 0x7f0900df
org.levimc.launcher:dimen/material_bottom_sheet_max_width = 0x7f060221
org.levimc.launcher:attr/motionDurationMedium2 = 0x7f030330
org.levimc.launcher:attr/motionEffect_translationX = 0x7f030348
org.levimc.launcher:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f120266
org.levimc.launcher:attr/titleMarginEnd = 0x7f03049e
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral100 = 0x7f0500a1
org.levimc.launcher:attr/contentPaddingStart = 0x7f030145
org.levimc.launcher:attr/titleMarginBottom = 0x7f03049d
org.levimc.launcher:drawable/abc_list_divider_material = 0x7f07004c
org.levimc.launcher:dimen/mtrl_calendar_header_text_padding = 0x7f060284
org.levimc.launcher:attr/layout_constraintRight_creator = 0x7f03029c
org.levimc.launcher:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600c6
org.levimc.launcher:string/mtrl_checkbox_state_description_unchecked = 0x7f11009f
org.levimc.launcher:attr/titleCentered = 0x7f030499
org.levimc.launcher:style/Widget.Material3.Button.TonalButton = 0x7f12036c
org.levimc.launcher:attr/panelMenuListTheme = 0x7f030374
org.levimc.launcher:attr/textAppearanceSearchResultSubtitle = 0x7f030458
org.levimc.launcher:attr/textAppearanceListItemSecondary = 0x7f030454
org.levimc.launcher:attr/tintNavigationIcon = 0x7f030497
org.levimc.launcher:dimen/m3_comp_navigation_rail_container_width = 0x7f060148
org.levimc.launcher:animator/m3_appbar_state_list_animator = 0x7f020009
org.levimc.launcher:attr/textAppearanceHeadline4 = 0x7f030448
org.levimc.launcher:layout/abc_screen_simple = 0x7f0c0015
org.levimc.launcher:attr/colorOnPrimaryContainer = 0x7f030102
org.levimc.launcher:attr/tint = 0x7f030495
org.levimc.launcher:style/AlertDialog.AppCompat.Light = 0x7f120001
org.levimc.launcher:id/pathRelative = 0x7f090180
org.levimc.launcher:attr/cardPreventCornerOverlap = 0x7f03009f
org.levimc.launcher:attr/colorOnTertiaryContainer = 0x7f03010e
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500cb
org.levimc.launcher:drawable/abc_list_selector_holo_light = 0x7f070057
org.levimc.launcher:attr/tickRadiusInactive = 0x7f030493
org.levimc.launcher:anim/design_snackbar_out = 0x7f01001b
org.levimc.launcher:attr/tickMarkTintMode = 0x7f030491
org.levimc.launcher:attr/textInputFilledDenseStyle = 0x7f030469
org.levimc.launcher:attr/tickMarkTint = 0x7f030490
org.levimc.launcher:attr/flow_horizontalStyle = 0x7f0301ef
org.levimc.launcher:attr/layout_constraintStart_toEndOf = 0x7f03029f
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1202e1
org.levimc.launcher:id/transition_pause_alpha = 0x7f09020c
org.levimc.launcher:attr/tickColorInactive = 0x7f03048e
org.levimc.launcher:string/path_password_eye_mask_visible = 0x7f1100e1
org.levimc.launcher:attr/navigationViewStyle = 0x7f03035a
org.levimc.launcher:attr/tickColorActive = 0x7f03048d
org.levimc.launcher:attr/thumbWidth = 0x7f03048b
org.levimc.launcher:id/horizontal = 0x7f0900ee
org.levimc.launcher:color/abc_hint_foreground_material_light = 0x7f050008
org.levimc.launcher:attr/thumbIconTint = 0x7f030482
org.levimc.launcher:color/m3_ref_palette_neutral_variant50 = 0x7f050121
org.levimc.launcher:dimen/m3_btn_icon_only_min_width = 0x7f0600d6
org.levimc.launcher:attr/alertDialogButtonGroupStyle = 0x7f030029
org.levimc.launcher:attr/imageButtonStyle = 0x7f03023b
org.levimc.launcher:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f12008f
org.levimc.launcher:id/accessibility_custom_action_0 = 0x7f090013
org.levimc.launcher:attr/thumbElevation = 0x7f03047e
org.levimc.launcher:style/Widget.Material3.CollapsingToolbar.Large = 0x7f120387
org.levimc.launcher:drawable/abc_btn_check_material = 0x7f07002b
org.levimc.launcher:dimen/m3_small_fab_size = 0x7f0601ed
org.levimc.launcher:color/abc_tint_edittext = 0x7f050015
org.levimc.launcher:attr/lStar = 0x7f03026f
org.levimc.launcher:attr/textStartPadding = 0x7f030476
org.levimc.launcher:attr/textPanY = 0x7f030475
org.levimc.launcher:color/mtrl_fab_icon_text_color_selector = 0x7f0502cd
org.levimc.launcher:attr/textPanX = 0x7f030474
org.levimc.launcher:attr/materialAlertDialogTitleIconStyle = 0x7f0302e0
org.levimc.launcher:attr/textColorSearchUrl = 0x7f030466
org.levimc.launcher:drawable/ic_mtrl_chip_close_circle = 0x7f0700ae
org.levimc.launcher:attr/textColorAlertDialogListItem = 0x7f030465
org.levimc.launcher:color/material_personalized_color_on_secondary_container = 0x7f050287
org.levimc.launcher:id/decelerateAndComplete = 0x7f09009c
org.levimc.launcher:attr/contentPaddingBottom = 0x7f030141
org.levimc.launcher:attr/buttonBarNeutralButtonStyle = 0x7f03008c
org.levimc.launcher:attr/textBackgroundPanX = 0x7f030461
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0d015c
org.levimc.launcher:layout/support_simple_spinner_dropdown_item = 0x7f0c007c
org.levimc.launcher:dimen/m3_appbar_size_compact = 0x7f0600a9
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0501a9
org.levimc.launcher:color/m3_sys_color_dynamic_dark_background = 0x7f05017d
org.levimc.launcher:attr/textAppearanceSubtitle2 = 0x7f03045c
org.levimc.launcher:attr/textAppearanceSearchResultTitle = 0x7f030459
org.levimc.launcher:attr/suggestionRowLayout = 0x7f030414
org.levimc.launcher:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f120060
org.levimc.launcher:attr/textAppearancePopupMenuHeader = 0x7f030457
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070010
org.levimc.launcher:attr/textAppearanceListItem = 0x7f030453
org.levimc.launcher:attr/textAppearanceLabelLarge = 0x7f03044e
org.levimc.launcher:attr/tabSelectedTextAppearance = 0x7f030430
org.levimc.launcher:string/call_notification_decline_action = 0x7f110029
org.levimc.launcher:attr/textAppearanceHeadlineSmall = 0x7f03044d
org.levimc.launcher:attr/textAppearanceHeadline5 = 0x7f030449
org.levimc.launcher:attr/buttonTintMode = 0x7f030099
org.levimc.launcher:attr/boxCollapsedPaddingTop = 0x7f030080
org.levimc.launcher:attr/checkedIconEnabled = 0x7f0300b6
org.levimc.launcher:attr/tooltipFrameBackground = 0x7f0304ad
org.levimc.launcher:attr/subtitleTextStyle = 0x7f030410
org.levimc.launcher:attr/textAppearanceHeadline1 = 0x7f030445
org.levimc.launcher:attr/textAppearanceDisplaySmall = 0x7f030444
org.levimc.launcher:dimen/m3_comp_navigation_drawer_icon_size = 0x7f060141
org.levimc.launcher:attr/defaultState = 0x7f030174
org.levimc.launcher:attr/textAppearanceDisplayLarge = 0x7f030442
org.levimc.launcher:dimen/tooltip_precise_anchor_threshold = 0x7f060326
org.levimc.launcher:styleable/FloatingActionButton = 0x7f130033
org.levimc.launcher:macro/m3_comp_outlined_button_outline_color = 0x7f0d00a6
org.levimc.launcher:color/m3_sys_color_light_secondary_container = 0x7f0501e3
org.levimc.launcher:attr/motionEasingStandardInterpolator = 0x7f030342
org.levimc.launcher:attr/panelBackground = 0x7f030373
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500a7
org.levimc.launcher:attr/textAppearanceSubtitle1 = 0x7f03045b
org.levimc.launcher:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f060238
org.levimc.launcher:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f120110
org.levimc.launcher:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f120049
org.levimc.launcher:drawable/compat_splash_screen_no_icon_background = 0x7f07008d
org.levimc.launcher:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0d00f9
org.levimc.launcher:attr/useCompatPadding = 0x7f0304cd
org.levimc.launcher:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f120160
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral0 = 0x7f05009f
org.levimc.launcher:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
org.levimc.launcher:attr/textAppearanceBody1 = 0x7f03043b
org.levimc.launcher:id/accessibility_custom_action_19 = 0x7f09001e
org.levimc.launcher:attr/toolbarId = 0x7f0304a8
org.levimc.launcher:attr/dropDownBackgroundTint = 0x7f030194
org.levimc.launcher:attr/contentInsetEnd = 0x7f03013a
org.levimc.launcher:color/androidx_core_ripple_material_light = 0x7f05001b
org.levimc.launcher:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b2
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Body1 = 0x7f12001a
org.levimc.launcher:attr/colorOnPrimary = 0x7f030101
org.levimc.launcher:attr/telltales_velocityMode = 0x7f030439
org.levimc.launcher:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f12029b
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f120192
org.levimc.launcher:attr/popupWindowStyle = 0x7f03038b
org.levimc.launcher:attr/tabTextColor = 0x7f030434
org.levimc.launcher:attr/tabSelectedTextColor = 0x7f030431
org.levimc.launcher:dimen/material_cursor_width = 0x7f060231
org.levimc.launcher:dimen/m3_chip_hovered_translation_z = 0x7f0600f8
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500c4
org.levimc.launcher:attr/colorOnError = 0x7f0300ff
org.levimc.launcher:dimen/mtrl_progress_circular_inset_medium = 0x7f0602d6
org.levimc.launcher:id/blocking = 0x7f090065
org.levimc.launcher:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f060194
org.levimc.launcher:attr/helperTextEnabled = 0x7f03021f
org.levimc.launcher:color/material_harmonized_color_on_error = 0x7f05026d
org.levimc.launcher:id/inward = 0x7f0900fd
org.levimc.launcher:dimen/material_clock_display_width = 0x7f060224
org.levimc.launcher:animator/fragment_open_enter = 0x7f020007
org.levimc.launcher:attr/badgeRadius = 0x7f030054
org.levimc.launcher:string/material_slider_range_end = 0x7f110084
org.levimc.launcher:attr/tabPaddingEnd = 0x7f03042b
org.levimc.launcher:color/material_personalized_color_on_secondary = 0x7f050286
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050186
org.levimc.launcher:style/Widget.Material3.Button.OutlinedButton = 0x7f120364
org.levimc.launcher:attr/selectorSize = 0x7f0303bb
org.levimc.launcher:attr/showDividers = 0x7f0303cd
org.levimc.launcher:style/ShapeAppearance.Material3.SmallComponent = 0x7f120183
org.levimc.launcher:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f06017a
org.levimc.launcher:attr/tabPadding = 0x7f030429
org.levimc.launcher:drawable/m3_avd_show_password = 0x7f0700b4
org.levimc.launcher:attr/tabMode = 0x7f030428
org.levimc.launcher:dimen/m3_comp_fab_primary_icon_size = 0x7f06011a
org.levimc.launcher:style/Widget.Material3.SideSheet.Detached = 0x7f1203d8
org.levimc.launcher:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060104
org.levimc.launcher:dimen/design_tab_max_width = 0x7f060089
org.levimc.launcher:attr/tabInlineLabel = 0x7f030425
org.levimc.launcher:id/accessibility_custom_action_7 = 0x7f090030
org.levimc.launcher:attr/tabIndicatorFullWidth = 0x7f030422
org.levimc.launcher:attr/textBackgroundPanY = 0x7f030462
org.levimc.launcher:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0501c1
org.levimc.launcher:attr/customReference = 0x7f03016a
org.levimc.launcher:attr/colorOnContainer = 0x7f0300fd
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f120260
org.levimc.launcher:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0d014a
org.levimc.launcher:attr/tabIndicatorColor = 0x7f030421
org.levimc.launcher:drawable/$m3_avd_show_password__0 = 0x7f070009
org.levimc.launcher:attr/nestedScrollViewStyle = 0x7f03035c
org.levimc.launcher:attr/tabIndicatorAnimationDuration = 0x7f03041f
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0500c0
org.levimc.launcher:style/Widget.MaterialComponents.Chip.Entry = 0x7f12041a
org.levimc.launcher:attr/tabIconTintMode = 0x7f03041d
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1201c7
org.levimc.launcher:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
org.levimc.launcher:attr/defaultMarginsEnabled = 0x7f030171
org.levimc.launcher:attr/tabGravity = 0x7f03041b
org.levimc.launcher:attr/state_indeterminate = 0x7f0303fd
org.levimc.launcher:dimen/mtrl_shape_corner_size_medium_component = 0x7f0602e3
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1202e3
org.levimc.launcher:attr/switchPadding = 0x7f030416
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500af
org.levimc.launcher:style/Theme.AppCompat.Light.DarkActionBar = 0x7f120225
org.levimc.launcher:attr/switchMinWidth = 0x7f030415
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1202cd
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0d0080
org.levimc.launcher:attr/suffixText = 0x7f030411
org.levimc.launcher:id/text_input_end_icon = 0x7f0901f3
org.levimc.launcher:attr/flow_horizontalAlign = 0x7f0301ec
org.levimc.launcher:attr/subtitleTextAppearance = 0x7f03040e
org.levimc.launcher:attr/fastScrollVerticalThumbDrawable = 0x7f0301d6
org.levimc.launcher:attr/strokeWidth = 0x7f030405
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f120435
org.levimc.launcher:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070022
org.levimc.launcher:integer/mtrl_calendar_year_selector_span = 0x7f0a0033
org.levimc.launcher:attr/listItemLayout = 0x7f0302ca
org.levimc.launcher:attr/statusBarForeground = 0x7f030402
org.levimc.launcher:style/Widget.MaterialComponents.NavigationRailView = 0x7f120444
org.levimc.launcher:dimen/material_time_picker_minimum_screen_height = 0x7f060244
org.levimc.launcher:dimen/abc_list_item_height_large_material = 0x7f060030
org.levimc.launcher:attr/materialButtonOutlinedStyle = 0x7f0302e3
org.levimc.launcher:attr/passwordToggleContentDescription = 0x7f030376
org.levimc.launcher:attr/itemTextAppearanceInactive = 0x7f030269
org.levimc.launcher:attr/statusBarBackground = 0x7f030401
org.levimc.launcher:attr/thumbTintMode = 0x7f030489
org.levimc.launcher:layout/material_timepicker = 0x7f0c004f
org.levimc.launcher:attr/state_error = 0x7f0303fc
org.levimc.launcher:styleable/GradientColor = 0x7f13003b
org.levimc.launcher:attr/state_collapsible = 0x7f0303fa
org.levimc.launcher:color/on_primary = 0x7f0502f3
org.levimc.launcher:integer/material_motion_duration_long_2 = 0x7f0a0028
org.levimc.launcher:attr/stateLabels = 0x7f0303f7
org.levimc.launcher:style/Theme.Material3.DynamicColors.DayNight = 0x7f120244
org.levimc.launcher:layout/abc_tooltip = 0x7f0c001b
org.levimc.launcher:attr/startIconTint = 0x7f0303f5
org.levimc.launcher:id/accessibility_custom_action_8 = 0x7f090031
org.levimc.launcher:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0602dd
org.levimc.launcher:integer/m3_sys_motion_duration_extra_long2 = 0x7f0a0011
org.levimc.launcher:dimen/m3_extended_fab_start_padding = 0x7f0601b3
org.levimc.launcher:layout/material_clockface_textview = 0x7f0c0049
org.levimc.launcher:dimen/design_fab_size_mini = 0x7f060071
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501bc
org.levimc.launcher:attr/stackFromEnd = 0x7f0303ee
org.levimc.launcher:string/repair_completed = 0x7f1100e4
org.levimc.launcher:color/m3_ref_palette_dynamic_primary30 = 0x7f0500d3
org.levimc.launcher:attr/srcCompat = 0x7f0303ed
org.levimc.launcher:string/icon_content_description = 0x7f110055
org.levimc.launcher:attr/state_above_anchor = 0x7f0303f8
org.levimc.launcher:attr/springStopThreshold = 0x7f0303ec
org.levimc.launcher:id/mini = 0x7f09012c
org.levimc.launcher:attr/listPreferredItemHeight = 0x7f0302ce
org.levimc.launcher:id/customPanel = 0x7f090098
org.levimc.launcher:attr/triggerReceiver = 0x7f0304c9
org.levimc.launcher:string/mtrl_picker_invalid_range = 0x7f1100b2
org.levimc.launcher:attr/springStiffness = 0x7f0303eb
org.levimc.launcher:dimen/mtrl_calendar_action_padding = 0x7f060273
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f060209
org.levimc.launcher:attr/springMass = 0x7f0303ea
org.levimc.launcher:attr/springDamping = 0x7f0303e9
org.levimc.launcher:attr/springBoundary = 0x7f0303e8
org.levimc.launcher:styleable/SearchBar = 0x7f130079
org.levimc.launcher:attr/textAppearanceHeadline2 = 0x7f030446
org.levimc.launcher:drawable/ic_search_black_24 = 0x7f0700af
org.levimc.launcher:attr/spinBars = 0x7f0303e3
org.levimc.launcher:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1200cf
org.levimc.launcher:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0a0022
org.levimc.launcher:color/design_dark_default_color_on_error = 0x7f050035
org.levimc.launcher:attr/singleLine = 0x7f0303db
org.levimc.launcher:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700e3
org.levimc.launcher:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f120306
org.levimc.launcher:attr/cornerFamily = 0x7f03014c
org.levimc.launcher:attr/simpleItems = 0x7f0303d9
org.levimc.launcher:attr/simpleItemSelectedRippleColor = 0x7f0303d8
org.levimc.launcher:attr/simpleItemSelectedColor = 0x7f0303d7
org.levimc.launcher:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1203eb
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f12003f
org.levimc.launcher:id/open_search_view_divider = 0x7f09016b
org.levimc.launcher:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f06028b
org.levimc.launcher:attr/simpleItemLayout = 0x7f0303d6
org.levimc.launcher:style/Widget.AppCompat.ActionButton = 0x7f1202f9
org.levimc.launcher:drawable/abc_spinner_textfield_background_material = 0x7f070066
org.levimc.launcher:dimen/m3_comp_scrim_container_opacity = 0x7f06016d
org.levimc.launcher:macro/m3_comp_dialog_headline_color = 0x7f0d0024
org.levimc.launcher:attr/shrinkMotionSpec = 0x7f0303d3
org.levimc.launcher:attr/autoSizePresetSizes = 0x7f030042
org.levimc.launcher:attr/showTitle = 0x7f0303d2
org.levimc.launcher:id/beginOnFirstDraw = 0x7f090061
org.levimc.launcher:attr/showText = 0x7f0303d1
org.levimc.launcher:dimen/m3_sys_elevation_level1 = 0x7f0601f1
org.levimc.launcher:color/abc_decor_view_status_guard = 0x7f050005
org.levimc.launcher:attr/colorContainer = 0x7f0300f6
org.levimc.launcher:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1203e9
org.levimc.launcher:attr/showDelay = 0x7f0303cc
org.levimc.launcher:dimen/mtrl_navigation_rail_margin = 0x7f0602d1
org.levimc.launcher:attr/materialAlertDialogTitlePanelStyle = 0x7f0302e1
org.levimc.launcher:attr/snackbarTextViewStyle = 0x7f0303e1
org.levimc.launcher:color/m3_sys_color_tertiary_fixed_dim = 0x7f0501fa
org.levimc.launcher:attr/showAsAction = 0x7f0303cb
org.levimc.launcher:attr/showAnimationBehavior = 0x7f0303ca
org.levimc.launcher:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f12005b
org.levimc.launcher:id/cancel_button = 0x7f090076
org.levimc.launcher:color/material_dynamic_primary0 = 0x7f05023d
org.levimc.launcher:color/background = 0x7f05001d
org.levimc.launcher:id/search_src_text = 0x7f0901ab
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0500f2
org.levimc.launcher:dimen/m3_btn_icon_only_icon_padding = 0x7f0600d5
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0d007a
org.levimc.launcher:layout/abc_popup_menu_header_item_layout = 0x7f0c0012
org.levimc.launcher:color/material_on_primary_emphasis_high_type = 0x7f050273
org.levimc.launcher:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0d00c1
org.levimc.launcher:attr/shouldRemoveExpandedCorners = 0x7f0303c9
org.levimc.launcher:string/theme_light = 0x7f1100ff
org.levimc.launcher:attr/motionDurationShort4 = 0x7f030336
org.levimc.launcher:color/m3_ref_palette_dynamic_primary70 = 0x7f0500d7
org.levimc.launcher:attr/motionDurationShort2 = 0x7f030334
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070018
org.levimc.launcher:attr/submitBackground = 0x7f03040b
org.levimc.launcher:styleable/StateListDrawableItem = 0x7f130084
org.levimc.launcher:attr/daySelectedStyle = 0x7f03016d
org.levimc.launcher:string/m3_exceed_max_badge_text_suffix = 0x7f110068
org.levimc.launcher:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
org.levimc.launcher:attr/shapeAppearanceSmallComponent = 0x7f0303c6
org.levimc.launcher:attr/shapeAppearanceMediumComponent = 0x7f0303c4
org.levimc.launcher:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f12037c
org.levimc.launcher:attr/paddingTopSystemWindowInsets = 0x7f030372
org.levimc.launcher:attr/shapeAppearanceLargeComponent = 0x7f0303c3
org.levimc.launcher:dimen/design_snackbar_action_inline_max_width = 0x7f06007e
org.levimc.launcher:attr/colorSurfaceContainerLow = 0x7f030125
org.levimc.launcher:color/design_default_color_secondary_variant = 0x7f05004a
org.levimc.launcher:style/Widget.Material3.ActionBar.Solid = 0x7f12034a
org.levimc.launcher:string/material_timepicker_hour = 0x7f110089
org.levimc.launcher:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0d009c
org.levimc.launcher:attr/shapeAppearanceCornerLarge = 0x7f0303c0
org.levimc.launcher:layout/notification_action = 0x7f0c0072
org.levimc.launcher:color/background_material_dark = 0x7f050020
org.levimc.launcher:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1203ea
org.levimc.launcher:attr/shapeAppearanceCornerExtraLarge = 0x7f0303be
org.levimc.launcher:style/TextAppearance.AppCompat = 0x7f12019e
org.levimc.launcher:attr/itemPaddingTop = 0x7f03025a
org.levimc.launcher:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700ca
org.levimc.launcher:id/submenuarrow = 0x7f0901da
org.levimc.launcher:attr/shapeAppearance = 0x7f0303bd
org.levimc.launcher:dimen/mtrl_slider_tick_min_spacing = 0x7f0602eb
org.levimc.launcher:color/switch_thumb_normal_material_light = 0x7f05030e
org.levimc.launcher:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06016b
org.levimc.launcher:color/m3_sys_color_light_error_container = 0x7f0501cf
org.levimc.launcher:macro/m3_comp_filled_button_label_text_type = 0x7f0d0045
org.levimc.launcher:color/m3_textfield_label_color = 0x7f050207
org.levimc.launcher:attr/setsTag = 0x7f0303bc
org.levimc.launcher:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
org.levimc.launcher:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0d00f6
org.levimc.launcher:attr/selectionRequired = 0x7f0303ba
org.levimc.launcher:attr/materialSearchViewToolbarHeight = 0x7f030305
org.levimc.launcher:dimen/mtrl_btn_letter_spacing = 0x7f060263
org.levimc.launcher:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0601ce
org.levimc.launcher:attr/searchPrefixText = 0x7f0303b5
org.levimc.launcher:dimen/design_bottom_navigation_shadow_height = 0x7f060069
org.levimc.launcher:color/material_dynamic_neutral_variant95 = 0x7f05023b
org.levimc.launcher:string/abc_search_hint = 0x7f110012
org.levimc.launcher:attr/scrimBackground = 0x7f0303b1
org.levimc.launcher:style/Theme.AppCompat.DayNight.Dialog = 0x7f12021a
org.levimc.launcher:drawable/design_fab_background = 0x7f07008e
org.levimc.launcher:color/mtrl_textinput_disabled_color = 0x7f0502eb
org.levimc.launcher:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f120213
org.levimc.launcher:attr/titleTextEllipsize = 0x7f0304a5
org.levimc.launcher:dimen/m3_comp_navigation_bar_container_height = 0x7f060139
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f120314
org.levimc.launcher:style/ThemeOverlay.AppCompat.Dark = 0x7f120284
org.levimc.launcher:attr/scrimAnimationDuration = 0x7f0303b0
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1201e8
org.levimc.launcher:string/overwrite_file_title = 0x7f1100dd
org.levimc.launcher:attr/textAppearanceLabelMedium = 0x7f03044f
org.levimc.launcher:attr/fastScrollEnabled = 0x7f0301d3
org.levimc.launcher:attr/roundPercent = 0x7f0303ad
org.levimc.launcher:attr/round = 0x7f0303ac
org.levimc.launcher:attr/rotationCenterId = 0x7f0303ab
org.levimc.launcher:attr/percentY = 0x7f030380
org.levimc.launcher:attr/overlay = 0x7f030369
org.levimc.launcher:id/select_version_button = 0x7f0901ae
org.levimc.launcher:attr/removeEmbeddedFabElevation = 0x7f0303a8
org.levimc.launcher:attr/region_widthMoreThan = 0x7f0303a7
org.levimc.launcher:drawable/abc_star_black_48dp = 0x7f070067
org.levimc.launcher:drawable/$avd_show_password__0 = 0x7f070003
org.levimc.launcher:color/design_default_color_on_background = 0x7f050041
org.levimc.launcher:style/Widget.Material3.CompoundButton.Switch = 0x7f12038c
org.levimc.launcher:dimen/m3_comp_input_chip_container_elevation = 0x7f060130
org.levimc.launcher:attr/region_heightMoreThan = 0x7f0303a5
org.levimc.launcher:attr/labelVisibilityMode = 0x7f030272
org.levimc.launcher:attr/recyclerViewStyle = 0x7f0303a3
org.levimc.launcher:color/m3_sys_color_dynamic_light_outline = 0x7f0501b0
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f120027
org.levimc.launcher:attr/reactiveGuide_applyToConstraintSet = 0x7f0303a1
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f1202f1
org.levimc.launcher:macro/m3_comp_navigation_bar_label_text_type = 0x7f0d0077
org.levimc.launcher:id/staticLayout = 0x7f0901d6
org.levimc.launcher:color/m3_sys_color_dark_on_surface = 0x7f050168
org.levimc.launcher:attr/ratingBarStyleSmall = 0x7f03039e
org.levimc.launcher:attr/searchHintIcon = 0x7f0303b3
org.levimc.launcher:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f120318
org.levimc.launcher:drawable/m3_popupmenu_background_overlay = 0x7f0700b7
org.levimc.launcher:id/open_search_view_edit_text = 0x7f09016d
org.levimc.launcher:attr/logoAdjustViewBounds = 0x7f0302d6
org.levimc.launcher:attr/startIconCheckable = 0x7f0303f0
org.levimc.launcher:attr/ratingBarStyleIndicator = 0x7f03039d
org.levimc.launcher:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f120100
org.levimc.launcher:id/invisible = 0x7f0900fc
org.levimc.launcher:anim/abc_fade_in = 0x7f010000
org.levimc.launcher:attr/chipIconSize = 0x7f0300c4
org.levimc.launcher:id/east = 0x7f0900bc
org.levimc.launcher:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f050193
org.levimc.launcher:attr/radioButtonStyle = 0x7f03039a
org.levimc.launcher:macro/m3_comp_fab_primary_container_shape = 0x7f0d0037
org.levimc.launcher:attr/queryPatterns = 0x7f030399
org.levimc.launcher:attr/backgroundInsetEnd = 0x7f03004a
org.levimc.launcher:style/Widget.MaterialComponents.MaterialDivider = 0x7f120443
org.levimc.launcher:attr/quantizeMotionInterpolator = 0x7f030394
org.levimc.launcher:attr/progressBarStyle = 0x7f030393
org.levimc.launcher:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070042
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Inverse = 0x7f120023
org.levimc.launcher:attr/boxStrokeColor = 0x7f030085
org.levimc.launcher:attr/progressBarPadding = 0x7f030392
org.levimc.launcher:string/material_motion_easing_standard = 0x7f110083
org.levimc.launcher:dimen/mtrl_navigation_rail_icon_margin = 0x7f0602cf
org.levimc.launcher:color/m3_button_background_color_selector = 0x7f050065
org.levimc.launcher:attr/pressedTranslationZ = 0x7f030391
org.levimc.launcher:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
org.levimc.launcher:color/m3_sys_color_tertiary_fixed = 0x7f0501f9
org.levimc.launcher:attr/layout_behavior = 0x7f03027c
org.levimc.launcher:attr/constraint_referenced_ids = 0x7f030135
org.levimc.launcher:attr/splashScreenIconSize = 0x7f0303e6
org.levimc.launcher:color/m3_ref_palette_neutral12 = 0x7f050106
org.levimc.launcher:attr/queryHint = 0x7f030398
org.levimc.launcher:attr/scrimVisibleHeightTrigger = 0x7f0303b2
org.levimc.launcher:attr/behavior_peekHeight = 0x7f03006f
org.levimc.launcher:attr/snackbarButtonStyle = 0x7f0303df
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f12039e
org.levimc.launcher:attr/checkedState = 0x7f0300bc
org.levimc.launcher:id/hide_ime_id = 0x7f0900e9
org.levimc.launcher:attr/searchIcon = 0x7f0303b4
org.levimc.launcher:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f0602fe
org.levimc.launcher:attr/layout_constraintTop_toBottomOf = 0x7f0302a3
org.levimc.launcher:attr/prefixTextAppearance = 0x7f03038e
org.levimc.launcher:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f12034d
org.levimc.launcher:attr/postSplashScreenTheme = 0x7f03038c
org.levimc.launcher:styleable/BottomAppBar = 0x7f130015
org.levimc.launcher:attr/motionDurationShort1 = 0x7f030333
org.levimc.launcher:color/design_icon_tint = 0x7f050054
org.levimc.launcher:attr/tabPaddingBottom = 0x7f03042a
org.levimc.launcher:id/screen = 0x7f09019d
org.levimc.launcher:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
org.levimc.launcher:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f120295
org.levimc.launcher:string/mtrl_picker_a11y_prev_month = 0x7f1100a4
org.levimc.launcher:attr/polarRelativeTo = 0x7f030387
org.levimc.launcher:attr/contrast = 0x7f030148
org.levimc.launcher:color/material_personalized_color_surface_container_high = 0x7f05029b
org.levimc.launcher:attr/chipBackgroundColor = 0x7f0300be
org.levimc.launcher:color/m3_navigation_item_background_color = 0x7f050093
org.levimc.launcher:attr/extendMotionSpec = 0x7f0301c2
org.levimc.launcher:layout/abc_alert_dialog_material = 0x7f0c0009
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060111
org.levimc.launcher:attr/placeholder_emptyVisibility = 0x7f030386
org.levimc.launcher:color/m3_sys_color_light_outline_variant = 0x7f0501df
org.levimc.launcher:attr/placeholderTextAppearance = 0x7f030384
org.levimc.launcher:id/recycler_versions = 0x7f09018f
org.levimc.launcher:attr/pivotAnchor = 0x7f030382
org.levimc.launcher:attr/hintEnabled = 0x7f030228
org.levimc.launcher:style/ThemeOverlay.Material3.Button.TextButton = 0x7f12029c
org.levimc.launcher:attr/thumbTextPadding = 0x7f030487
org.levimc.launcher:attr/suffixTextColor = 0x7f030413
org.levimc.launcher:attr/colorOnPrimarySurface = 0x7f030105
org.levimc.launcher:attr/percentWidth = 0x7f03037e
org.levimc.launcher:color/m3_ref_palette_neutral_variant60 = 0x7f050122
org.levimc.launcher:style/Widget.Material3.Chip.Filter = 0x7f120375
org.levimc.launcher:attr/nestedScrollFlags = 0x7f03035b
org.levimc.launcher:attr/autoSizeStepGranularity = 0x7f030043
org.levimc.launcher:attr/percentHeight = 0x7f03037d
org.levimc.launcher:styleable/SideSheetBehavior_Layout = 0x7f13007d
org.levimc.launcher:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f060179
org.levimc.launcher:attr/deltaPolarAngle = 0x7f030175
org.levimc.launcher:color/m3_timepicker_button_background_color = 0x7f050209
org.levimc.launcher:id/action_mode_bar = 0x7f090045
org.levimc.launcher:attr/expanded = 0x7f0301b8
org.levimc.launcher:attr/grid_horizontalGaps = 0x7f030210
org.levimc.launcher:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
org.levimc.launcher:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120292
org.levimc.launcher:string/mtrl_switch_thumb_path_pressed = 0x7f1100cc
org.levimc.launcher:id/settings_desc = 0x7f0901b2
org.levimc.launcher:attr/shortcutMatchRequired = 0x7f0303c8
org.levimc.launcher:attr/passwordToggleTintMode = 0x7f03037a
org.levimc.launcher:attr/showPaths = 0x7f0303d0
org.levimc.launcher:id/contiguous = 0x7f090090
org.levimc.launcher:dimen/mtrl_calendar_header_height = 0x7f060281
org.levimc.launcher:color/m3_sys_color_dark_surface_container_high = 0x7f050175
org.levimc.launcher:attr/contentPaddingLeft = 0x7f030143
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f120261
org.levimc.launcher:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0d010a
org.levimc.launcher:layout/ime_base_split_test_activity = 0x7f0c0035
org.levimc.launcher:id/chronometer = 0x7f090082
org.levimc.launcher:color/m3_appbar_overlay_color = 0x7f050061
org.levimc.launcher:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1200ca
org.levimc.launcher:attr/paddingStart = 0x7f03036f
org.levimc.launcher:attr/actionBarTheme = 0x7f03000b
org.levimc.launcher:dimen/mtrl_chip_text_size = 0x7f0602a2
org.levimc.launcher:attr/iconSize = 0x7f030234
org.levimc.launcher:dimen/abc_action_bar_content_inset_material = 0x7f060000
org.levimc.launcher:color/design_fab_stroke_top_outer_color = 0x7f050053
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1203b7
org.levimc.launcher:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0d0135
org.levimc.launcher:drawable/test_level_drawable = 0x7f0700fb
org.levimc.launcher:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f12033c
org.levimc.launcher:color/material_dynamic_color_dark_on_error = 0x7f05021d
org.levimc.launcher:attr/paddingRightSystemWindowInsets = 0x7f03036e
org.levimc.launcher:id/action_bar_spinner = 0x7f09003a
org.levimc.launcher:dimen/mtrl_extended_fab_start_padding = 0x7f0602b0
org.levimc.launcher:attr/textBackgroundZoom = 0x7f030464
org.levimc.launcher:attr/hideNavigationIcon = 0x7f030224
org.levimc.launcher:attr/onTouchUp = 0x7f030367
org.levimc.launcher:style/Base.Theme.Material3.Light = 0x7f120063
org.levimc.launcher:attr/onNegativeCross = 0x7f030363
org.levimc.launcher:layout/item_mod = 0x7f0c0037
org.levimc.launcher:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f060286
org.levimc.launcher:attr/fabAlignmentModeEndMargin = 0x7f0301cb
org.levimc.launcher:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060132
org.levimc.launcher:dimen/m3_searchbar_elevation = 0x7f0601da
org.levimc.launcher:attr/onCross = 0x7f030361
org.levimc.launcher:attr/offsetAlignmentMode = 0x7f030360
org.levimc.launcher:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f120274
org.levimc.launcher:attr/chipIconVisible = 0x7f0300c6
org.levimc.launcher:attr/number = 0x7f03035e
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0d00fd
org.levimc.launcher:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0501cb
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500bc
org.levimc.launcher:dimen/design_tab_text_size = 0x7f06008b
org.levimc.launcher:drawable/ic_clear_black_24 = 0x7f07009d
org.levimc.launcher:macro/m3_comp_fab_surface_container_color = 0x7f0d003d
org.levimc.launcher:color/m3_sys_color_primary_fixed = 0x7f0501f5
org.levimc.launcher:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
org.levimc.launcher:color/m3_navigation_item_text_color = 0x7f050096
org.levimc.launcher:drawable/abc_text_select_handle_middle_mtrl = 0x7f07006f
org.levimc.launcher:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0d009e
org.levimc.launcher:attr/textEndPadding = 0x7f030467
org.levimc.launcher:attr/navigationRailStyle = 0x7f030359
org.levimc.launcher:attr/badgeShapeAppearance = 0x7f030055
org.levimc.launcher:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f06019f
org.levimc.launcher:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0501b1
org.levimc.launcher:attr/behavior_expandedOffset = 0x7f03006a
org.levimc.launcher:styleable/MenuView = 0x7f130061
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0d0164
org.levimc.launcher:id/center_horizontal = 0x7f09007b
org.levimc.launcher:attr/hintTextColor = 0x7f03022a
org.levimc.launcher:color/material_personalized_color_primary = 0x7f05028f
org.levimc.launcher:attr/autoCompleteTextViewStyle = 0x7f03003e
org.levimc.launcher:attr/colorSwitchThumbNormal = 0x7f03012a
org.levimc.launcher:color/material_dynamic_secondary20 = 0x7f05024d
org.levimc.launcher:style/TextAppearance.MaterialComponents.Headline1 = 0x7f120208
org.levimc.launcher:attr/flow_verticalGap = 0x7f0301f8
org.levimc.launcher:attr/listPreferredItemHeightLarge = 0x7f0302cf
org.levimc.launcher:attr/windowFixedHeightMajor = 0x7f0304e4
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Primary = 0x7f12039a
org.levimc.launcher:attr/chipEndPadding = 0x7f0300c0
org.levimc.launcher:attr/navigationIcon = 0x7f030356
org.levimc.launcher:style/TextAppearance.MaterialComponents.Chip = 0x7f120207
org.levimc.launcher:integer/m3_sys_motion_duration_short3 = 0x7f0a001e
org.levimc.launcher:attr/itemRippleColor = 0x7f03025b
org.levimc.launcher:attr/navigationContentDescription = 0x7f030355
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500a6
org.levimc.launcher:attr/shapeAppearanceCornerMedium = 0x7f0303c1
org.levimc.launcher:color/m3_sys_color_dynamic_light_error = 0x7f0501a0
org.levimc.launcher:attr/multiChoiceItemLayout = 0x7f030354
org.levimc.launcher:string/mtrl_timepicker_cancel = 0x7f1100d0
org.levimc.launcher:attr/textAppearanceBody2 = 0x7f03043c
org.levimc.launcher:attr/thumbColor = 0x7f03047d
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0d0072
org.levimc.launcher:attr/motion_postLayoutCollision = 0x7f030351
org.levimc.launcher:dimen/design_bottom_navigation_active_item_min_width = 0x7f060060
org.levimc.launcher:animator/design_appbar_state_list_animator = 0x7f020000
org.levimc.launcher:styleable/ColorStateListItem = 0x7f130024
org.levimc.launcher:attr/animationMode = 0x7f030035
org.levimc.launcher:dimen/mtrl_slider_thumb_elevation = 0x7f0602e9
org.levimc.launcher:attr/motionStagger = 0x7f03034f
org.levimc.launcher:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f120178
org.levimc.launcher:dimen/mtrl_switch_thumb_icon_size = 0x7f0602f8
org.levimc.launcher:attr/badgeWidePadding = 0x7f03005c
org.levimc.launcher:attr/colorOutline = 0x7f030111
org.levimc.launcher:animator/fragment_fade_exit = 0x7f020006
org.levimc.launcher:style/Base.Widget.AppCompat.PopupWindow = 0x7f1200f3
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary80 = 0x7f0500e5
org.levimc.launcher:layout/mtrl_calendar_day = 0x7f0c0059
org.levimc.launcher:integer/mtrl_calendar_selection_text_lines = 0x7f0a0032
org.levimc.launcher:color/abc_primary_text_material_dark = 0x7f05000b
org.levimc.launcher:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1202c0
org.levimc.launcher:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0d00f7
org.levimc.launcher:attr/layout_constraintEnd_toStartOf = 0x7f03028d
org.levimc.launcher:attr/motionPath = 0x7f03034c
org.levimc.launcher:dimen/highlight_alpha_material_light = 0x7f060095
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0500f3
org.levimc.launcher:attr/motionEffect_translationY = 0x7f030349
org.levimc.launcher:dimen/design_bottom_navigation_margin = 0x7f060068
org.levimc.launcher:attr/floatingActionButtonSurfaceStyle = 0x7f0301e6
org.levimc.launcher:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0b0008
org.levimc.launcher:attr/helperText = 0x7f03021e
org.levimc.launcher:attr/closeIconSize = 0x7f0300e2
org.levimc.launcher:id/BOTTOM_START = 0x7f090002
org.levimc.launcher:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
org.levimc.launcher:attr/itemIconSize = 0x7f030254
org.levimc.launcher:attr/motionEffect_start = 0x7f030346
org.levimc.launcher:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1203b0
org.levimc.launcher:dimen/m3_btn_padding_top = 0x7f0600dc
org.levimc.launcher:color/material_dynamic_neutral30 = 0x7f050227
org.levimc.launcher:attr/motionEffect_alpha = 0x7f030343
org.levimc.launcher:attr/textBackgroundRotate = 0x7f030463
org.levimc.launcher:attr/motionEasingStandardDecelerateInterpolator = 0x7f030341
org.levimc.launcher:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f12026b
org.levimc.launcher:attr/motionEasingLinearInterpolator = 0x7f03033e
org.levimc.launcher:attr/showMarker = 0x7f0303ce
org.levimc.launcher:attr/motionEasingLinear = 0x7f03033d
org.levimc.launcher:color/material_harmonized_color_error = 0x7f05026b
org.levimc.launcher:attr/motionEasingEmphasizedInterpolator = 0x7f03033c
org.levimc.launcher:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f060200
org.levimc.launcher:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f03033a
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0d015f
org.levimc.launcher:attr/itemShapeInsetBottom = 0x7f03025f
org.levimc.launcher:attr/maxImageSize = 0x7f030311
org.levimc.launcher:attr/motionEasingDecelerated = 0x7f030338
org.levimc.launcher:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070058
org.levimc.launcher:attr/checkedButton = 0x7f0300b3
org.levimc.launcher:attr/motionEasingAccelerated = 0x7f030337
org.levimc.launcher:attr/motionDebug = 0x7f030326
org.levimc.launcher:attr/iconifiedByDefault = 0x7f030238
org.levimc.launcher:id/accessibility_custom_action_16 = 0x7f09001b
org.levimc.launcher:attr/textOutlineColor = 0x7f030472
org.levimc.launcher:attr/carousel_touchUpMode = 0x7f0300aa
org.levimc.launcher:color/material_dynamic_neutral60 = 0x7f05022a
org.levimc.launcher:attr/verticalOffset = 0x7f0304d1
org.levimc.launcher:attr/motionDurationMedium3 = 0x7f030331
org.levimc.launcher:attr/expandedTitleTextAppearance = 0x7f0301c0
org.levimc.launcher:color/m3_ref_palette_neutral_variant99 = 0x7f050127
org.levimc.launcher:attr/chipSpacingHorizontal = 0x7f0300ca
org.levimc.launcher:attr/itemTextAppearanceActive = 0x7f030267
org.levimc.launcher:integer/design_snackbar_text_max_lines = 0x7f0a0007
org.levimc.launcher:attr/motionDurationLong2 = 0x7f03032c
org.levimc.launcher:attr/layout_goneMarginBaseline = 0x7f0302b0
org.levimc.launcher:attr/motionDurationLong1 = 0x7f03032b
org.levimc.launcher:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1202c8
org.levimc.launcher:id/text2 = 0x7f0901ed
org.levimc.launcher:attr/trackDecoration = 0x7f0304b9
org.levimc.launcher:attr/motionDurationExtraLong4 = 0x7f03032a
org.levimc.launcher:style/Base.V28.Theme.AppCompat = 0x7f1200bf
org.levimc.launcher:attr/mock_showLabel = 0x7f030325
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1201cc
org.levimc.launcher:animator/mtrl_card_state_list_anim = 0x7f020017
org.levimc.launcher:attr/layout_constraintGuide_percent = 0x7f030290
org.levimc.launcher:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
org.levimc.launcher:attr/titleMargins = 0x7f0304a1
org.levimc.launcher:layout/item_settings_edittext = 0x7f0c0039
org.levimc.launcher:attr/mock_labelColor = 0x7f030323
org.levimc.launcher:attr/mock_labelBackgroundColor = 0x7f030322
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1201be
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500b7
org.levimc.launcher:attr/minWidth = 0x7f03031f
org.levimc.launcher:attr/itemShapeInsetStart = 0x7f030261
org.levimc.launcher:color/bright_foreground_inverse_material_dark = 0x7f050024
org.levimc.launcher:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0d00d4
org.levimc.launcher:dimen/mtrl_shape_corner_size_large_component = 0x7f0602e2
org.levimc.launcher:attr/shapeCornerFamily = 0x7f0303c7
org.levimc.launcher:dimen/design_bottom_navigation_item_min_width = 0x7f060066
org.levimc.launcher:color/tooltip_background_light = 0x7f050311
org.levimc.launcher:attr/dayTodayStyle = 0x7f03016f
org.levimc.launcher:attr/minTouchTargetSize = 0x7f03031e
org.levimc.launcher:attr/materialIconButtonStyle = 0x7f030301
org.levimc.launcher:attr/minHideDelay = 0x7f03031c
org.levimc.launcher:attr/headerLayout = 0x7f03021c
org.levimc.launcher:attr/methodName = 0x7f03031a
org.levimc.launcher:attr/cornerSizeTopRight = 0x7f030156
org.levimc.launcher:drawable/material_ic_edit_black_24dp = 0x7f0700c1
org.levimc.launcher:attr/menuAlignmentMode = 0x7f030318
org.levimc.launcher:attr/menu = 0x7f030317
org.levimc.launcher:attr/counterMaxLength = 0x7f030158
org.levimc.launcher:dimen/material_clock_display_padding = 0x7f060223
org.levimc.launcher:string/cancel = 0x7f11002e
org.levimc.launcher:layout/mtrl_alert_dialog_actions = 0x7f0c0053
org.levimc.launcher:attr/measureWithLargestChild = 0x7f030316
org.levimc.launcher:dimen/abc_search_view_preferred_height = 0x7f060036
org.levimc.launcher:layout/mtrl_layout_snackbar = 0x7f0c0063
org.levimc.launcher:attr/tabIconTint = 0x7f03041c
org.levimc.launcher:color/on_tertiary = 0x7f0502f6
org.levimc.launcher:attr/maxNumber = 0x7f030313
org.levimc.launcher:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070024
org.levimc.launcher:styleable/AppBarLayout_Layout = 0x7f13000c
org.levimc.launcher:attr/maxHeight = 0x7f030310
org.levimc.launcher:attr/maxButtonHeight = 0x7f03030e
org.levimc.launcher:attr/expandedTitleMargin = 0x7f0301bb
org.levimc.launcher:drawable/abc_btn_colored_material = 0x7f07002f
org.levimc.launcher:attr/queryBackground = 0x7f030397
org.levimc.launcher:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f12041d
org.levimc.launcher:attr/materialCalendarMonthNavigationButton = 0x7f0302f1
org.levimc.launcher:dimen/m3_side_sheet_margin_detached = 0x7f0601e5
org.levimc.launcher:attr/badgeWidth = 0x7f03005d
org.levimc.launcher:attr/layout_constraintRight_toLeftOf = 0x7f03029d
org.levimc.launcher:color/m3_button_outline_color_selector = 0x7f050067
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents = 0x7f120137
org.levimc.launcher:color/design_fab_stroke_end_inner_color = 0x7f050050
org.levimc.launcher:styleable/Insets = 0x7f13003f
org.levimc.launcher:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0d012c
org.levimc.launcher:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000e
org.levimc.launcher:integer/bottom_sheet_slide_duration = 0x7f0a0003
org.levimc.launcher:color/m3_card_ripple_color = 0x7f05006d
org.levimc.launcher:dimen/m3_comp_input_chip_container_height = 0x7f060131
org.levimc.launcher:attr/materialThemeOverlay = 0x7f030308
org.levimc.launcher:attr/materialSwitchStyle = 0x7f030307
org.levimc.launcher:attr/materialSearchViewStyle = 0x7f030304
org.levimc.launcher:attr/ensureMinTouchTargetSize = 0x7f0301ac
org.levimc.launcher:attr/animateCircleAngleTo = 0x7f030031
org.levimc.launcher:attr/materialSearchViewPrefixStyle = 0x7f030303
org.levimc.launcher:attr/thumbIconSize = 0x7f030481
org.levimc.launcher:color/abc_search_url_text_selected = 0x7f050010
org.levimc.launcher:attr/color = 0x7f0300f2
org.levimc.launcher:attr/textureEffect = 0x7f030478
org.levimc.launcher:attr/materialIconButtonOutlinedStyle = 0x7f030300
org.levimc.launcher:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070084
org.levimc.launcher:macro/m3_comp_navigation_bar_container_color = 0x7f0d006b
org.levimc.launcher:attr/pathMotionArc = 0x7f03037b
org.levimc.launcher:attr/materialDividerHeavyStyle = 0x7f0302fc
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1201e6
org.levimc.launcher:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f06019e
org.levimc.launcher:attr/textAppearanceBodyLarge = 0x7f03043d
org.levimc.launcher:attr/contentScrim = 0x7f030147
org.levimc.launcher:attr/materialCircleRadius = 0x7f0302f9
org.levimc.launcher:attr/strokeColor = 0x7f030404
org.levimc.launcher:dimen/m3_carousel_small_item_default_corner_size = 0x7f0600f0
org.levimc.launcher:attr/layout_constraintCircleAngle = 0x7f030289
org.levimc.launcher:attr/materialCardViewOutlinedStyle = 0x7f0302f7
org.levimc.launcher:dimen/abc_text_size_button_material = 0x7f060041
org.levimc.launcher:attr/region_heightLessThan = 0x7f0303a4
org.levimc.launcher:attr/materialCardViewFilledStyle = 0x7f0302f6
org.levimc.launcher:id/match_parent = 0x7f090114
org.levimc.launcher:attr/materialCardViewElevatedStyle = 0x7f0302f5
org.levimc.launcher:attr/textBackground = 0x7f030460
org.levimc.launcher:macro/m3_comp_badge_color = 0x7f0d0002
org.levimc.launcher:attr/motionDurationExtraLong2 = 0x7f030328
org.levimc.launcher:attr/materialCalendarMonth = 0x7f0302f0
org.levimc.launcher:layout/mtrl_calendar_month_labeled = 0x7f0c005e
org.levimc.launcher:attr/materialCalendarHeaderTitle = 0x7f0302ee
org.levimc.launcher:attr/materialCalendarHeaderSelection = 0x7f0302ed
org.levimc.launcher:attr/materialCalendarFullscreenTheme = 0x7f0302e8
org.levimc.launcher:color/dim_foreground_disabled_material_light = 0x7f050057
org.levimc.launcher:style/Theme.AppCompat.Light.Dialog = 0x7f120226
org.levimc.launcher:attr/subheaderInsetEnd = 0x7f030408
org.levimc.launcher:attr/materialButtonToggleGroupStyle = 0x7f0302e5
org.levimc.launcher:attr/materialButtonStyle = 0x7f0302e4
org.levimc.launcher:id/bounce = 0x7f090067
org.levimc.launcher:dimen/abc_dialog_fixed_height_major = 0x7f06001c
org.levimc.launcher:drawable/abc_tab_indicator_material = 0x7f07006b
org.levimc.launcher:drawable/abc_cab_background_top_material = 0x7f070038
org.levimc.launcher:attr/collapsingToolbarLayoutStyle = 0x7f0300f1
org.levimc.launcher:attr/viewTransitionOnCross = 0x7f0304d5
org.levimc.launcher:attr/saturation = 0x7f0303ae
org.levimc.launcher:attr/dividerInsetStart = 0x7f030180
org.levimc.launcher:dimen/abc_alert_dialog_button_dimen = 0x7f060011
org.levimc.launcher:string/english = 0x7f110041
org.levimc.launcher:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f050091
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500b9
org.levimc.launcher:drawable/bg_third_level_item = 0x7f070082
org.levimc.launcher:dimen/mtrl_btn_max_width = 0x7f060264
org.levimc.launcher:attr/materialAlertDialogBodyTextStyle = 0x7f0302dd
org.levimc.launcher:color/on_error = 0x7f0502f2
org.levimc.launcher:id/mtrl_picker_header = 0x7f090148
org.levimc.launcher:attr/marginTopSystemWindowInsets = 0x7f0302dc
org.levimc.launcher:styleable/Layout = 0x7f130048
org.levimc.launcher:attr/defaultDuration = 0x7f030170
org.levimc.launcher:attr/contentPaddingRight = 0x7f030144
org.levimc.launcher:attr/logoDescription = 0x7f0302d7
org.levimc.launcher:attr/state_liftable = 0x7f0303fe
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0d00c9
org.levimc.launcher:attr/wavePeriod = 0x7f0304dd
org.levimc.launcher:attr/materialDisplayDividerStyle = 0x7f0302fb
org.levimc.launcher:attr/listPreferredItemPaddingStart = 0x7f0302d4
org.levimc.launcher:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0d0172
org.levimc.launcher:id/dropdown_menu = 0x7f0900b8
org.levimc.launcher:id/design_menu_item_text = 0x7f0900a5
org.levimc.launcher:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600a7
org.levimc.launcher:drawable/mtrl_ic_check_mark = 0x7f0700d8
org.levimc.launcher:attr/switchStyle = 0x7f030417
org.levimc.launcher:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
org.levimc.launcher:color/design_default_color_on_secondary = 0x7f050044
org.levimc.launcher:attr/indeterminateAnimationType = 0x7f030240
org.levimc.launcher:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1201e4
org.levimc.launcher:attr/listPreferredItemPaddingLeft = 0x7f0302d2
org.levimc.launcher:attr/materialCalendarHeaderCancelButton = 0x7f0302e9
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0500c3
org.levimc.launcher:attr/splitTrack = 0x7f0303e7
org.levimc.launcher:styleable/MenuItem = 0x7f130060
org.levimc.launcher:id/about_card = 0x7f09000f
org.levimc.launcher:attr/badgeText = 0x7f030058
org.levimc.launcher:attr/layout_constraintLeft_toLeftOf = 0x7f03029a
org.levimc.launcher:dimen/m3_alert_dialog_corner_size = 0x7f06009f
org.levimc.launcher:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1203c9
org.levimc.launcher:id/accessibility_custom_action_14 = 0x7f090019
org.levimc.launcher:attr/sideSheetDialogTheme = 0x7f0303d4
org.levimc.launcher:attr/layout_constraintTop_creator = 0x7f0302a2
org.levimc.launcher:color/m3_efab_ripple_color_selector = 0x7f050087
org.levimc.launcher:attr/liftOnScrollColor = 0x7f0302c0
org.levimc.launcher:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f12032d
org.levimc.launcher:attr/seekBarStyle = 0x7f0303b7
org.levimc.launcher:style/Widget.Material3.CheckedTextView = 0x7f120372
org.levimc.launcher:attr/forceDefaultNavigationOnClickListener = 0x7f030208
org.levimc.launcher:id/edit_query = 0x7f0900be
org.levimc.launcher:attr/layout_goneMarginTop = 0x7f0302b6
org.levimc.launcher:layout/notification_template_part_chronometer = 0x7f0c0076
org.levimc.launcher:id/rounded = 0x7f090197
org.levimc.launcher:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
org.levimc.launcher:animator/fragment_fade_enter = 0x7f020005
org.levimc.launcher:attr/textAppearanceSmallPopupMenu = 0x7f03045a
org.levimc.launcher:attr/layout_goneMarginEnd = 0x7f0302b2
org.levimc.launcher:attr/constraint_referenced_tags = 0x7f030136
org.levimc.launcher:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601ac
org.levimc.launcher:attr/layout_goneMarginStart = 0x7f0302b5
org.levimc.launcher:dimen/mtrl_btn_disabled_elevation = 0x7f06025b
org.levimc.launcher:color/mtrl_chip_text_color = 0x7f0502c7
org.levimc.launcher:styleable/TabLayout = 0x7f130089
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f120316
org.levimc.launcher:attr/grid_validateInputs = 0x7f030217
org.levimc.launcher:color/background_material_light = 0x7f050021
org.levimc.launcher:dimen/mtrl_calendar_landscape_header_width = 0x7f060287
org.levimc.launcher:attr/expandedTitleMarginEnd = 0x7f0301bd
org.levimc.launcher:attr/textAppearanceOverline = 0x7f030456
org.levimc.launcher:color/mtrl_textinput_focused_box_stroke_color = 0x7f0502ed
org.levimc.launcher:attr/layout_goneMarginLeft = 0x7f0302b3
org.levimc.launcher:id/open_search_view_status_bar_spacer = 0x7f090172
org.levimc.launcher:attr/motion_triggerOnCollision = 0x7f030352
org.levimc.launcher:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f06019d
org.levimc.launcher:attr/cornerFamilyBottomLeft = 0x7f03014d
org.levimc.launcher:attr/animateMenuItems = 0x7f030032
org.levimc.launcher:attr/materialIconButtonFilledStyle = 0x7f0302fe
org.levimc.launcher:attr/cardElevation = 0x7f03009c
org.levimc.launcher:attr/linearProgressIndicatorStyle = 0x7f0302c5
org.levimc.launcher:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f12031d
org.levimc.launcher:style/Base.V22.Theme.AppCompat.Light = 0x7f1200b5
org.levimc.launcher:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0d000c
org.levimc.launcher:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
org.levimc.launcher:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0502bb
org.levimc.launcher:attr/motionDurationMedium1 = 0x7f03032f
org.levimc.launcher:color/design_default_color_primary = 0x7f050046
org.levimc.launcher:attr/thumbStrokeColor = 0x7f030485
org.levimc.launcher:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0c0057
org.levimc.launcher:id/textinput_helper_text = 0x7f0901fa
org.levimc.launcher:attr/rippleColor = 0x7f0303aa
org.levimc.launcher:attr/layout_constraintWidth_max = 0x7f0302aa
org.levimc.launcher:style/Base.V28.Theme.AppCompat.Light = 0x7f1200c0
org.levimc.launcher:string/abc_action_mode_done = 0x7f110003
org.levimc.launcher:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0602ca
org.levimc.launcher:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f06018d
org.levimc.launcher:attr/defaultScrollFlagsEnabled = 0x7f030173
org.levimc.launcher:attr/errorTextColor = 0x7f0301b6
org.levimc.launcher:dimen/material_emphasis_high_type = 0x7f060235
org.levimc.launcher:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0d0095
org.levimc.launcher:dimen/m3_comp_progress_indicator_track_thickness = 0x7f060164
org.levimc.launcher:attr/badgeHeight = 0x7f030053
org.levimc.launcher:attr/layout_constraintVertical_chainStyle = 0x7f0302a6
org.levimc.launcher:styleable/NavigationBarActiveIndicator = 0x7f13006a
org.levimc.launcher:attr/layout_constrainedHeight = 0x7f03027f
org.levimc.launcher:attr/materialCalendarTheme = 0x7f0302f3
org.levimc.launcher:color/m3_switch_track_tint = 0x7f05015a
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1201bf
org.levimc.launcher:attr/mock_label = 0x7f030321
org.levimc.launcher:attr/layout_constraintRight_toRightOf = 0x7f03029e
org.levimc.launcher:attr/layout_insetEdge = 0x7f0302b7
org.levimc.launcher:attr/cardForegroundColor = 0x7f03009d
org.levimc.launcher:color/secondary_text_disabled_material_dark = 0x7f050306
org.levimc.launcher:attr/liftOnScrollTargetViewId = 0x7f0302c1
org.levimc.launcher:attr/layout_constraintHorizontal_bias = 0x7f030296
org.levimc.launcher:attr/layout_constraintLeft_creator = 0x7f030299
org.levimc.launcher:color/mtrl_tabs_colored_ripple_color = 0x7f0502e4
org.levimc.launcher:attr/yearSelectedStyle = 0x7f0304ef
org.levimc.launcher:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
org.levimc.launcher:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f060118
org.levimc.launcher:attr/layout_constraintHeight_min = 0x7f030294
org.levimc.launcher:attr/layout_constraintEnd_toEndOf = 0x7f03028c
org.levimc.launcher:id/submit_area = 0x7f0901db
org.levimc.launcher:color/m3_navigation_bar_ripple_color_selector = 0x7f050092
org.levimc.launcher:attr/constraintSetEnd = 0x7f030133
org.levimc.launcher:attr/fontProviderFetchTimeout = 0x7f030200
org.levimc.launcher:attr/layout_constraintGuide_begin = 0x7f03028e
org.levimc.launcher:color/m3_ref_palette_secondary80 = 0x7f05013e
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500c8
org.levimc.launcher:style/Base.Widget.AppCompat.ActionMode = 0x7f1200d1
org.levimc.launcher:attr/motionTarget = 0x7f030350
org.levimc.launcher:attr/layout_constraintDimensionRatio = 0x7f03028b
org.levimc.launcher:anim/design_bottom_sheet_slide_in = 0x7f010018
org.levimc.launcher:attr/layout_constraintStart_toStartOf = 0x7f0302a0
org.levimc.launcher:animator/m3_card_state_list_anim = 0x7f02000d
org.levimc.launcher:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f120382
org.levimc.launcher:attr/layout_constraintCircleRadius = 0x7f03028a
org.levimc.launcher:dimen/m3_comp_slider_inactive_track_height = 0x7f060187
org.levimc.launcher:attr/motionDurationExtraLong3 = 0x7f030329
org.levimc.launcher:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600bd
org.levimc.launcher:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0d0141
org.levimc.launcher:attr/trackDecorationTintMode = 0x7f0304bb
org.levimc.launcher:attr/panelMenuListWidth = 0x7f030375
org.levimc.launcher:attr/layout_constraintBaseline_creator = 0x7f030281
org.levimc.launcher:attr/clockFaceBackgroundColor = 0x7f0300db
org.levimc.launcher:attr/layout_constrainedWidth = 0x7f030280
org.levimc.launcher:attr/colorOnSecondaryContainer = 0x7f030107
org.levimc.launcher:attr/layout_constraintTag = 0x7f0302a1
org.levimc.launcher:attr/listPreferredItemPaddingEnd = 0x7f0302d1
org.levimc.launcher:drawable/bg_abi_armeabi_v7a = 0x7f07007b
org.levimc.launcher:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070047
org.levimc.launcher:attr/layout = 0x7f030276
org.levimc.launcher:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f120394
org.levimc.launcher:attr/layoutManager = 0x7f030279
org.levimc.launcher:attr/labelBehavior = 0x7f030270
org.levimc.launcher:attr/layout_dodgeInsetEdges = 0x7f0302ad
org.levimc.launcher:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f120397
org.levimc.launcher:attr/actionModeFindDrawable = 0x7f030018
org.levimc.launcher:attr/customPixelDimension = 0x7f030169
org.levimc.launcher:color/m3_filled_icon_button_container_color_selector = 0x7f05008c
org.levimc.launcher:id/tag_on_apply_window_listener = 0x7f0901e3
org.levimc.launcher:attr/titleTextAppearance = 0x7f0304a3
org.levimc.launcher:attr/actionModeShareDrawable = 0x7f03001c
org.levimc.launcher:attr/fontProviderCerts = 0x7f0301fe
org.levimc.launcher:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0502a6
org.levimc.launcher:color/m3_ref_palette_neutral4 = 0x7f05010c
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0d008d
org.levimc.launcher:attr/itemTextColor = 0x7f03026a
org.levimc.launcher:attr/lastBaselineToBottomHeight = 0x7f030274
org.levimc.launcher:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f120081
org.levimc.launcher:attr/barrierMargin = 0x7f030066
org.levimc.launcher:attr/circularflow_angles = 0x7f0300d4
org.levimc.launcher:attr/layout_constraintWidth_percent = 0x7f0302ac
org.levimc.launcher:dimen/m3_navigation_rail_item_min_height = 0x7f0601cf
org.levimc.launcher:attr/dragScale = 0x7f030185
org.levimc.launcher:attr/waveDecay = 0x7f0304db
org.levimc.launcher:attr/behavior_hideable = 0x7f03006d
org.levimc.launcher:attr/dialogCornerRadius = 0x7f030178
org.levimc.launcher:id/special_effects_controller_view_tag = 0x7f0901c5
org.levimc.launcher:attr/buttonBarButtonStyle = 0x7f03008a
org.levimc.launcher:attr/borderRoundPercent = 0x7f030075
org.levimc.launcher:dimen/mtrl_progress_circular_size = 0x7f0602d9
org.levimc.launcher:attr/itemStrokeWidth = 0x7f030265
org.levimc.launcher:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060161
org.levimc.launcher:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1200dc
org.levimc.launcher:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060143
org.levimc.launcher:attr/passwordToggleEnabled = 0x7f030378
org.levimc.launcher:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0602ad
org.levimc.launcher:attr/itemShapeInsetEnd = 0x7f030260
org.levimc.launcher:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f120118
org.levimc.launcher:attr/materialCalendarHeaderConfirmButton = 0x7f0302ea
org.levimc.launcher:style/Base.TextAppearance.AppCompat = 0x7f120019
org.levimc.launcher:attr/itemShapeFillColor = 0x7f03025e
org.levimc.launcher:integer/mtrl_badge_max_character_count = 0x7f0a002e
org.levimc.launcher:id/all = 0x7f090050
org.levimc.launcher:color/m3_ref_palette_secondary90 = 0x7f05013f
org.levimc.launcher:string/overwrite_file_message = 0x7f1100dc
org.levimc.launcher:animator/m3_card_elevated_state_list_anim = 0x7f02000c
org.levimc.launcher:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0d00e1
org.levimc.launcher:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700cc
org.levimc.launcher:layout/abc_activity_chooser_view_list_item = 0x7f0c0007
org.levimc.launcher:animator/m3_extended_fab_state_list_animator = 0x7f020014
org.levimc.launcher:animator/m3_chip_state_list_anim = 0x7f02000e
org.levimc.launcher:attr/popupTheme = 0x7f03038a
org.levimc.launcher:attr/layout_constraintCircle = 0x7f030288
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0d0089
org.levimc.launcher:attr/actionModePopupWindowStyle = 0x7f03001a
org.levimc.launcher:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f120270
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f120099
org.levimc.launcher:attr/itemShapeAppearanceOverlay = 0x7f03025d
org.levimc.launcher:attr/tabIndicatorGravity = 0x7f030423
org.levimc.launcher:id/mtrl_view_tag_bottom_padding = 0x7f090150
org.levimc.launcher:dimen/design_bottom_sheet_modal_elevation = 0x7f06006c
org.levimc.launcher:color/m3_ref_palette_error10 = 0x7f0500f7
org.levimc.launcher:id/navigation_bar_item_active_indicator_view = 0x7f090152
org.levimc.launcher:color/m3_ref_palette_neutral40 = 0x7f05010d
org.levimc.launcher:color/material_personalized_color_on_tertiary = 0x7f05028b
org.levimc.launcher:attr/itemPaddingBottom = 0x7f030259
org.levimc.launcher:style/Animation.AppCompat.DropDownUp = 0x7f120003
org.levimc.launcher:color/design_default_color_surface = 0x7f05004b
org.levimc.launcher:attr/itemMinHeight = 0x7f030257
org.levimc.launcher:id/sin = 0x7f0901bb
org.levimc.launcher:dimen/material_clock_hand_stroke_width = 0x7f060229
org.levimc.launcher:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f12011e
org.levimc.launcher:attr/layoutDescription = 0x7f030277
org.levimc.launcher:color/m3_slider_active_track_color_legacy = 0x7f050153
org.levimc.launcher:attr/itemMaxLines = 0x7f030256
org.levimc.launcher:id/postLayout = 0x7f090185
org.levimc.launcher:attr/itemHorizontalPadding = 0x7f030251
org.levimc.launcher:style/TextAppearance.Material3.ActionBar.Title = 0x7f1201ee
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0d0153
org.levimc.launcher:id/main_card = 0x7f090110
org.levimc.launcher:attr/itemActiveIndicatorStyle = 0x7f03024e
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1201c8
org.levimc.launcher:id/carryVelocity = 0x7f090077
org.levimc.launcher:color/m3_sys_color_dark_on_secondary_container = 0x7f050167
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1202eb
org.levimc.launcher:attr/liftOnScroll = 0x7f0302bf
org.levimc.launcher:attr/titleEnabled = 0x7f03049b
org.levimc.launcher:string/not_found_version = 0x7f1100d7
org.levimc.launcher:attr/closeIconVisible = 0x7f0300e5
org.levimc.launcher:attr/isMaterial3Theme = 0x7f03024c
org.levimc.launcher:attr/isLightTheme = 0x7f03024a
org.levimc.launcher:attr/tabMaxWidth = 0x7f030426
org.levimc.launcher:attr/indicatorTrackGapSize = 0x7f030247
org.levimc.launcher:attr/indicatorDirectionLinear = 0x7f030244
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f1202f2
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f12009d
org.levimc.launcher:color/design_dark_default_color_secondary = 0x7f05003c
org.levimc.launcher:attr/materialCalendarHeaderLayout = 0x7f0302ec
org.levimc.launcher:attr/listDividerAlertDialog = 0x7f0302c9
org.levimc.launcher:attr/indicatorColor = 0x7f030242
org.levimc.launcher:style/Widget.Material3.CompoundButton.RadioButton = 0x7f12038b
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1202d2
org.levimc.launcher:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f06017f
org.levimc.launcher:attr/indeterminateProgressStyle = 0x7f030241
org.levimc.launcher:attr/collapsedSize = 0x7f0300e9
org.levimc.launcher:attr/imageZoom = 0x7f03023f
org.levimc.launcher:drawable/notification_template_icon_low_bg = 0x7f0700f8
org.levimc.launcher:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f060271
org.levimc.launcher:color/abc_search_url_text = 0x7f05000d
org.levimc.launcher:attr/dayInvalidStyle = 0x7f03016c
org.levimc.launcher:attr/imageRotate = 0x7f03023e
org.levimc.launcher:style/TextAppearance.Design.Tab = 0x7f1201dd
org.levimc.launcher:attr/startIconTintMode = 0x7f0303f6
org.levimc.launcher:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301c8
org.levimc.launcher:attr/titleTextColor = 0x7f0304a4
org.levimc.launcher:macro/m3_comp_search_bar_container_color = 0x7f0d00e6
org.levimc.launcher:attr/drawableRightCompat = 0x7f03018b
org.levimc.launcher:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f12027e
org.levimc.launcher:attr/trackStopIndicatorSize = 0x7f0304be
org.levimc.launcher:attr/dragThreshold = 0x7f030186
org.levimc.launcher:id/mtrl_calendar_frame = 0x7f09013d
org.levimc.launcher:attr/layout_goneMarginRight = 0x7f0302b4
org.levimc.launcher:attr/ifTagSet = 0x7f03023a
org.levimc.launcher:string/m3_ref_typeface_plain_regular = 0x7f11006c
org.levimc.launcher:attr/imagePanX = 0x7f03023c
org.levimc.launcher:color/m3_fab_efab_background_color_selector = 0x7f050089
org.levimc.launcher:attr/iconTint = 0x7f030236
org.levimc.launcher:color/material_personalized_color_on_error_container = 0x7f050283
org.levimc.launcher:style/Platform.V21.AppCompat.Light = 0x7f12014b
org.levimc.launcher:attr/extendStrategy = 0x7f0301c3
org.levimc.launcher:attr/iconStartPadding = 0x7f030235
org.levimc.launcher:dimen/abc_action_bar_stacked_max_height = 0x7f060009
org.levimc.launcher:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0d014d
org.levimc.launcher:attr/colorOnTertiary = 0x7f03010d
org.levimc.launcher:attr/layout_constraintBottom_toTopOf = 0x7f030287
org.levimc.launcher:attr/fastScrollVerticalTrackDrawable = 0x7f0301d7
org.levimc.launcher:attr/icon = 0x7f030230
org.levimc.launcher:string/bottomsheet_action_expand = 0x7f110023
org.levimc.launcher:attr/counterOverflowTextAppearance = 0x7f030159
org.levimc.launcher:attr/drawableStartCompat = 0x7f03018d
org.levimc.launcher:color/design_fab_shadow_end_color = 0x7f05004d
org.levimc.launcher:style/Widget.Material3.Button.IconButton.Filled = 0x7f120361
org.levimc.launcher:attr/motionEffect_viewTransition = 0x7f03034a
org.levimc.launcher:macro/m3_comp_extended_fab_surface_container_color = 0x7f0d0032
org.levimc.launcher:id/up = 0x7f09021a
org.levimc.launcher:attr/hoveredFocusedTranslationZ = 0x7f03022f
org.levimc.launcher:styleable/AppCompatSeekBar = 0x7f13000f
org.levimc.launcher:style/Widget.Material3.BottomNavigation.Badge = 0x7f120356
org.levimc.launcher:attr/hideOnScroll = 0x7f030226
org.levimc.launcher:color/material_dynamic_secondary99 = 0x7f050256
org.levimc.launcher:style/Widget.Material3.Chip.Input = 0x7f120377
org.levimc.launcher:attr/hideMotionSpec = 0x7f030223
org.levimc.launcher:drawable/ic_keyboard_black_24dp = 0x7f0700a4
org.levimc.launcher:string/side_sheet_accessibility_pane_title = 0x7f1100f6
org.levimc.launcher:attr/hideAnimationBehavior = 0x7f030222
org.levimc.launcher:attr/height = 0x7f03021d
org.levimc.launcher:attr/windowSplashScreenBackground = 0x7f0304ed
org.levimc.launcher:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600c4
org.levimc.launcher:attr/maxVelocity = 0x7f030314
org.levimc.launcher:attr/expandedTitleTextColor = 0x7f0301c1
org.levimc.launcher:color/abc_search_url_text_normal = 0x7f05000e
org.levimc.launcher:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0d0100
org.levimc.launcher:attr/subheaderTextAppearance = 0x7f03040a
org.levimc.launcher:attr/grid_rows = 0x7f030213
org.levimc.launcher:macro/m3_comp_fab_primary_large_container_shape = 0x7f0d0039
org.levimc.launcher:attr/colorSurfaceVariant = 0x7f030129
org.levimc.launcher:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070039
org.levimc.launcher:attr/itemStrokeColor = 0x7f030264
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f06010c
org.levimc.launcher:attr/grid_rowWeights = 0x7f030212
org.levimc.launcher:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
org.levimc.launcher:attr/toggleCheckedStateOnClick = 0x7f0304a7
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f0601fe
org.levimc.launcher:dimen/abc_list_item_height_small_material = 0x7f060032
org.levimc.launcher:attr/grid_columns = 0x7f03020f
org.levimc.launcher:string/grant_permission = 0x7f110053
org.levimc.launcher:color/m3_text_button_background_color_selector = 0x7f050201
org.levimc.launcher:color/button_material_dark = 0x7f050028
org.levimc.launcher:attr/layout_constraintBaseline_toBaselineOf = 0x7f030282
org.levimc.launcher:attr/cardMaxElevation = 0x7f03009e
org.levimc.launcher:attr/materialCalendarYearNavigationButton = 0x7f0302f4
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f120469
org.levimc.launcher:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0d0031
org.levimc.launcher:attr/colorPrimarySurface = 0x7f030119
org.levimc.launcher:style/Widget.Material3.BottomAppBar.Legacy = 0x7f120355
org.levimc.launcher:attr/gestureInsetBottomIgnored = 0x7f03020c
org.levimc.launcher:style/Base.Theme.Material3.Dark = 0x7f12005d
org.levimc.launcher:id/showCustom = 0x7f0901b8
org.levimc.launcher:drawable/notification_bg_normal_pressed = 0x7f0700f4
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f120193
org.levimc.launcher:attr/boxCornerRadiusTopEnd = 0x7f030083
org.levimc.launcher:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1201b0
org.levimc.launcher:attr/fontWeight = 0x7f030206
org.levimc.launcher:id/mtrl_calendar_year_selector_frame = 0x7f090142
org.levimc.launcher:dimen/m3_btn_icon_only_default_size = 0x7f0600d4
org.levimc.launcher:attr/endIconDrawable = 0x7f0301a4
org.levimc.launcher:style/Theme.Design.BottomSheetDialog = 0x7f12022d
org.levimc.launcher:attr/startIconDrawable = 0x7f0303f2
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0500f4
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f12025e
org.levimc.launcher:color/m3_sys_color_light_primary = 0x7f0501e0
org.levimc.launcher:dimen/m3_btn_icon_only_default_padding = 0x7f0600d3
org.levimc.launcher:color/material_dynamic_tertiary30 = 0x7f05025b
org.levimc.launcher:color/m3_dark_highlighted_text = 0x7f050078
org.levimc.launcher:attr/fontProviderFetchStrategy = 0x7f0301ff
org.levimc.launcher:color/material_dynamic_neutral_variant70 = 0x7f050238
org.levimc.launcher:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0d0121
org.levimc.launcher:attr/motionEasingEmphasized = 0x7f030339
org.levimc.launcher:color/material_personalized_color_error_container = 0x7f050280
org.levimc.launcher:attr/layout_constraintHorizontal_chainStyle = 0x7f030297
org.levimc.launcher:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0d009b
org.levimc.launcher:attr/flow_wrapMode = 0x7f0301fa
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500b5
org.levimc.launcher:styleable/MotionLayout = 0x7f130067
org.levimc.launcher:id/icon_group = 0x7f0900f1
org.levimc.launcher:dimen/notification_small_icon_background_padding = 0x7f060316
org.levimc.launcher:attr/lastItemDecorated = 0x7f030275
org.levimc.launcher:dimen/mtrl_extended_fab_top_padding = 0x7f0602b2
org.levimc.launcher:attr/counterTextColor = 0x7f03015c
org.levimc.launcher:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f12016f
org.levimc.launcher:color/abc_decor_view_status_guard_light = 0x7f050006
org.levimc.launcher:id/accessibility_custom_action_18 = 0x7f09001d
org.levimc.launcher:attr/buttonTint = 0x7f030098
org.levimc.launcher:attr/checkMarkCompat = 0x7f0300af
org.levimc.launcher:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0d0136
org.levimc.launcher:attr/clickAction = 0x7f0300da
org.levimc.launcher:string/version_ignored = 0x7f11010a
org.levimc.launcher:attr/boxStrokeWidthFocused = 0x7f030088
org.levimc.launcher:attr/layout_constraintGuide_end = 0x7f03028f
org.levimc.launcher:dimen/mtrl_extended_fab_translation_z_base = 0x7f0602b3
org.levimc.launcher:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b1
org.levimc.launcher:animator/fragment_open_exit = 0x7f020008
org.levimc.launcher:attr/layout_constraintVertical_bias = 0x7f0302a5
org.levimc.launcher:attr/fontFamily = 0x7f0301fc
org.levimc.launcher:attr/font = 0x7f0301fb
org.levimc.launcher:dimen/abc_control_padding_material = 0x7f06001a
org.levimc.launcher:attr/flow_verticalBias = 0x7f0301f7
org.levimc.launcher:attr/materialCalendarDayOfWeekLabel = 0x7f0302e7
org.levimc.launcher:attr/flow_verticalAlign = 0x7f0301f6
org.levimc.launcher:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f120285
org.levimc.launcher:id/month_title = 0x7f090138
org.levimc.launcher:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060125
org.levimc.launcher:attr/keylines = 0x7f03026e
org.levimc.launcher:attr/layout_scrollEffect = 0x7f0302bb
org.levimc.launcher:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f120456
org.levimc.launcher:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003e
org.levimc.launcher:attr/flow_horizontalGap = 0x7f0301ee
org.levimc.launcher:attr/expandedTitleMarginStart = 0x7f0301be
org.levimc.launcher:attr/collapsedTitleGravity = 0x7f0300ea
org.levimc.launcher:color/material_harmonized_color_error_container = 0x7f05026c
org.levimc.launcher:attr/thumbStrokeWidth = 0x7f030486
org.levimc.launcher:attr/enforceMaterialTheme = 0x7f0301aa
org.levimc.launcher:attr/paddingEnd = 0x7f03036c
org.levimc.launcher:attr/flow_firstVerticalStyle = 0x7f0301eb
org.levimc.launcher:attr/transitionShapeAppearance = 0x7f0304c7
org.levimc.launcher:dimen/material_cursor_inset = 0x7f060230
org.levimc.launcher:attr/checkedIconSize = 0x7f0300b9
org.levimc.launcher:style/TextAppearance.Material3.HeadlineSmall = 0x7f1201f7
org.levimc.launcher:color/foreground_material_light = 0x7f05005e
org.levimc.launcher:attr/flow_firstHorizontalBias = 0x7f0301e8
org.levimc.launcher:id/transition_scene_layoutid_cache = 0x7f09020e
org.levimc.launcher:attr/activityChooserViewStyle = 0x7f030027
org.levimc.launcher:attr/limitBoundsTo = 0x7f0302c2
org.levimc.launcher:attr/tabUnboundedRipple = 0x7f030435
org.levimc.launcher:attr/onShow = 0x7f030365
org.levimc.launcher:dimen/m3_comp_time_picker_container_elevation = 0x7f0601a0
org.levimc.launcher:attr/layout_constraintTop_toTopOf = 0x7f0302a4
org.levimc.launcher:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f12008e
org.levimc.launcher:dimen/abc_search_view_preferred_width = 0x7f060037
org.levimc.launcher:attr/indicatorSize = 0x7f030246
org.levimc.launcher:attr/haloColor = 0x7f03021a
org.levimc.launcher:integer/material_motion_duration_medium_2 = 0x7f0a002a
org.levimc.launcher:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a4
org.levimc.launcher:string/mtrl_picker_invalid_format = 0x7f1100af
org.levimc.launcher:attr/floatingActionButtonTertiaryStyle = 0x7f0301e7
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f120039
org.levimc.launcher:dimen/mtrl_calendar_text_input_padding_top = 0x7f060293
org.levimc.launcher:color/m3_checkbox_button_icon_tint = 0x7f05006f
org.levimc.launcher:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f06013f
org.levimc.launcher:drawable/abc_btn_radio_material_anim = 0x7f070032
org.levimc.launcher:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
org.levimc.launcher:animator/design_fab_hide_motion_spec = 0x7f020001
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1201c5
org.levimc.launcher:string/call_notification_answer_video_action = 0x7f110028
org.levimc.launcher:attr/toolbarSurfaceStyle = 0x7f0304ab
org.levimc.launcher:attr/tickRadiusActive = 0x7f030492
org.levimc.launcher:dimen/mtrl_slider_widget_height = 0x7f0602ef
org.levimc.launcher:attr/statusBarScrim = 0x7f030403
org.levimc.launcher:attr/textAppearanceHeadlineMedium = 0x7f03044c
org.levimc.launcher:integer/config_tooltipAnimTime = 0x7f0a0005
org.levimc.launcher:attr/actionBarSplitStyle = 0x7f030006
org.levimc.launcher:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070052
org.levimc.launcher:style/CardView.Dark = 0x7f12012b
org.levimc.launcher:attr/floatingActionButtonSmallStyle = 0x7f0301e2
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1201cd
org.levimc.launcher:color/m3_ref_palette_neutral100 = 0x7f050105
org.levimc.launcher:attr/passwordToggleTint = 0x7f030379
org.levimc.launcher:layout/abc_expanded_menu_layout = 0x7f0c000d
org.levimc.launcher:attr/layout_marginBaseline = 0x7f0302b9
org.levimc.launcher:attr/layout_constraintHeight_max = 0x7f030293
org.levimc.launcher:attr/colorOnBackground = 0x7f0300fc
org.levimc.launcher:layout/mtrl_alert_dialog_title = 0x7f0c0054
org.levimc.launcher:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0301e0
org.levimc.launcher:style/TextAppearance.MaterialComponents.Button = 0x7f120205
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f060216
org.levimc.launcher:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060105
org.levimc.launcher:color/m3_sys_color_primary_fixed_dim = 0x7f0501f6
org.levimc.launcher:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0d013e
org.levimc.launcher:id/split_action_bar = 0x7f0901c9
org.levimc.launcher:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f06011d
org.levimc.launcher:attr/constraints = 0x7f030137
org.levimc.launcher:attr/thumbTrackGapSize = 0x7f03048a
org.levimc.launcher:drawable/compat_splash_screen = 0x7f07008c
org.levimc.launcher:style/Widget.AppCompat.ActionMode = 0x7f1202fc
org.levimc.launcher:animator/m3_btn_state_list_anim = 0x7f02000b
org.levimc.launcher:attr/fabCustomSize = 0x7f0301d1
org.levimc.launcher:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301dc
org.levimc.launcher:attr/bottomNavigationStyle = 0x7f03007a
org.levimc.launcher:styleable/AnimatedStateListDrawableCompat = 0x7f130007
org.levimc.launcher:attr/listChoiceBackgroundIndicator = 0x7f0302c6
org.levimc.launcher:style/Theme.MaterialComponents.CompactMenu = 0x7f120253
org.levimc.launcher:dimen/mtrl_card_elevation = 0x7f06029f
org.levimc.launcher:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301d9
org.levimc.launcher:id/arc = 0x7f090057
org.levimc.launcher:attr/menuGravity = 0x7f030319
org.levimc.launcher:attr/chipStartPadding = 0x7f0300cd
org.levimc.launcher:attr/badgeShapeAppearanceOverlay = 0x7f030056
org.levimc.launcher:string/hide_bottom_view_on_scroll_behavior = 0x7f110054
org.levimc.launcher:layout/m3_alert_dialog_actions = 0x7f0c0040
org.levimc.launcher:attr/fastScrollHorizontalThumbDrawable = 0x7f0301d4
org.levimc.launcher:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f070019
org.levimc.launcher:string/mtrl_picker_today_description = 0x7f1100c3
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0500ec
org.levimc.launcher:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f120171
org.levimc.launcher:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f060300
org.levimc.launcher:attr/motionDurationLong3 = 0x7f03032d
org.levimc.launcher:color/m3_ref_palette_primary20 = 0x7f05012b
org.levimc.launcher:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1202ac
org.levimc.launcher:attr/blendSrc = 0x7f030073
org.levimc.launcher:string/resourcepack_detected_message = 0x7f1100ed
org.levimc.launcher:dimen/mtrl_calendar_navigation_height = 0x7f06028c
org.levimc.launcher:attr/initialActivityCount = 0x7f030248
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0500ee
org.levimc.launcher:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0d0087
org.levimc.launcher:color/cardview_light_background = 0x7f05002d
org.levimc.launcher:color/m3_sys_color_light_on_secondary = 0x7f0501d8
org.levimc.launcher:id/coordinator = 0x7f090092
org.levimc.launcher:attr/values = 0x7f0304d0
org.levimc.launcher:attr/shapeAppearanceCornerExtraSmall = 0x7f0303bf
org.levimc.launcher:attr/titlePositionInterpolator = 0x7f0304a2
org.levimc.launcher:styleable/ConstraintLayout_ReactiveGuide = 0x7f130028
org.levimc.launcher:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f12023e
org.levimc.launcher:style/ShapeAppearance.Material3.Corner.None = 0x7f12017e
org.levimc.launcher:string/item_view_role_description = 0x7f110062
org.levimc.launcher:color/mtrl_calendar_item_stroke_color = 0x7f0502c0
org.levimc.launcher:id/fitToContents = 0x7f0900d4
org.levimc.launcher:attr/fabCradleVerticalOffset = 0x7f0301d0
org.levimc.launcher:attr/errorAccessibilityLabel = 0x7f0301ad
org.levimc.launcher:attr/listChoiceIndicatorMultipleAnimated = 0x7f0302c7
org.levimc.launcher:drawable/bg_abi_default = 0x7f07007c
org.levimc.launcher:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0d00c0
org.levimc.launcher:attr/grid_skips = 0x7f030214
org.levimc.launcher:attr/fabCradleMargin = 0x7f0301ce
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0d0154
org.levimc.launcher:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f06030b
org.levimc.launcher:attr/barrierDirection = 0x7f030065
org.levimc.launcher:color/m3_ref_palette_neutral_variant80 = 0x7f050124
org.levimc.launcher:attr/fabAlignmentMode = 0x7f0301ca
org.levimc.launcher:attr/badgeVerticalPadding = 0x7f03005b
org.levimc.launcher:color/material_blue_grey_800 = 0x7f050214
org.levimc.launcher:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501c6
org.levimc.launcher:color/m3_ref_palette_dynamic_primary80 = 0x7f0500d8
org.levimc.launcher:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301c7
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f050199
org.levimc.launcher:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301c5
org.levimc.launcher:anim/abc_slide_in_top = 0x7f010007
org.levimc.launcher:color/mtrl_filled_icon_tint = 0x7f0502d0
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0d0161
org.levimc.launcher:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501ba
org.levimc.launcher:attr/floatingActionButtonSecondaryStyle = 0x7f0301df
org.levimc.launcher:dimen/m3_alert_dialog_action_bottom_padding = 0x7f06009d
org.levimc.launcher:color/m3_ref_palette_dynamic_primary95 = 0x7f0500da
org.levimc.launcher:attr/quantizeMotionPhase = 0x7f030395
org.levimc.launcher:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0d0060
org.levimc.launcher:dimen/m3_comp_outlined_card_icon_size = 0x7f060152
org.levimc.launcher:color/cardview_shadow_start_color = 0x7f05002f
org.levimc.launcher:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301c4
org.levimc.launcher:attr/carousel_forwardTransition = 0x7f0300a6
org.levimc.launcher:attr/layout_constraintHorizontal_weight = 0x7f030298
org.levimc.launcher:color/material_dynamic_neutral100 = 0x7f050225
org.levimc.launcher:color/m3_tabs_ripple_color_secondary = 0x7f0501fe
org.levimc.launcher:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f120076
org.levimc.launcher:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
org.levimc.launcher:macro/m3_comp_search_view_container_color = 0x7f0d00f1
org.levimc.launcher:attr/boxBackgroundColor = 0x7f03007e
org.levimc.launcher:attr/expandedTitleMarginBottom = 0x7f0301bc
org.levimc.launcher:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
org.levimc.launcher:attr/chipStandaloneStyle = 0x7f0300cc
org.levimc.launcher:attr/circleRadius = 0x7f0300d2
org.levimc.launcher:color/m3_dynamic_highlighted_text = 0x7f050084
org.levimc.launcher:attr/expandedTitleGravity = 0x7f0301ba
org.levimc.launcher:attr/expandedHintEnabled = 0x7f0301b9
org.levimc.launcher:attr/expandActivityOverflowButtonDrawable = 0x7f0301b7
org.levimc.launcher:drawable/tooltip_frame_light = 0x7f0700fd
org.levimc.launcher:dimen/mtrl_progress_circular_size_small = 0x7f0602dc
org.levimc.launcher:styleable/Snackbar = 0x7f13007f
org.levimc.launcher:attr/errorTextAppearance = 0x7f0301b5
org.levimc.launcher:style/Theme.Material3.Dark = 0x7f120232
org.levimc.launcher:attr/errorShown = 0x7f0301b4
org.levimc.launcher:dimen/m3_comp_bottom_app_bar_container_height = 0x7f060102
org.levimc.launcher:attr/drawPath = 0x7f030187
org.levimc.launcher:attr/dividerPadding = 0x7f030181
org.levimc.launcher:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060185
org.levimc.launcher:drawable/abc_text_cursor_material = 0x7f07006d
org.levimc.launcher:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070040
org.levimc.launcher:attr/colorControlNormal = 0x7f0300f9
org.levimc.launcher:dimen/material_divider_thickness = 0x7f060232
org.levimc.launcher:attr/counterTextAppearance = 0x7f03015b
org.levimc.launcher:attr/reactiveGuide_animateChange = 0x7f03039f
org.levimc.launcher:attr/contentPadding = 0x7f030140
org.levimc.launcher:attr/paddingStartSystemWindowInsets = 0x7f030370
org.levimc.launcher:attr/enforceTextAppearance = 0x7f0301ab
org.levimc.launcher:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f06019a
org.levimc.launcher:attr/endIconScaleType = 0x7f0301a7
org.levimc.launcher:attr/cursorErrorColor = 0x7f030160
org.levimc.launcher:id/beginning = 0x7f090062
org.levimc.launcher:attr/colorOnPrimaryFixed = 0x7f030103
org.levimc.launcher:attr/enableEdgeToEdge = 0x7f0301a1
org.levimc.launcher:attr/thumbIcon = 0x7f030480
org.levimc.launcher:attr/iconEndPadding = 0x7f030231
org.levimc.launcher:attr/emojiCompatEnabled = 0x7f0301a0
org.levimc.launcher:string/abc_prepend_shortcut_label = 0x7f110011
org.levimc.launcher:attr/clockNumberTextColor = 0x7f0300de
org.levimc.launcher:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602e0
org.levimc.launcher:attr/elevationOverlayColor = 0x7f03019e
org.levimc.launcher:styleable/AppCompatTextHelper = 0x7f130010
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0500cc
org.levimc.launcher:integer/show_password_duration = 0x7f0a0043
org.levimc.launcher:attr/grid_orientation = 0x7f030211
org.levimc.launcher:attr/colorOnSecondaryFixed = 0x7f030108
org.levimc.launcher:attr/colorOnSurfaceVariant = 0x7f03010c
org.levimc.launcher:color/m3_chip_text_color = 0x7f050075
org.levimc.launcher:id/SHOW_ALL = 0x7f090008
org.levimc.launcher:attr/ttcIndex = 0x7f0304cb
org.levimc.launcher:style/Base.V14.Theme.MaterialComponents = 0x7f120098
org.levimc.launcher:id/save_non_transition_alpha = 0x7f090199
org.levimc.launcher:attr/dynamicColorThemeOverlay = 0x7f030198
org.levimc.launcher:color/m3_ref_palette_neutral20 = 0x7f050108
org.levimc.launcher:attr/dropdownListPreferredItemHeight = 0x7f030196
org.levimc.launcher:color/primary_text_disabled_material_dark = 0x7f0502ff
org.levimc.launcher:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f060112
org.levimc.launcher:attr/checkMarkTintMode = 0x7f0300b1
org.levimc.launcher:color/material_dynamic_neutral_variant50 = 0x7f050236
org.levimc.launcher:attr/itemIconTint = 0x7f030255
org.levimc.launcher:attr/dropDownListViewStyle = 0x7f030195
org.levimc.launcher:attr/textFillColor = 0x7f030468
org.levimc.launcher:attr/windowFixedWidthMinor = 0x7f0304e7
org.levimc.launcher:string/toast_delete_failed = 0x7f110101
org.levimc.launcher:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0d0120
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0500f1
org.levimc.launcher:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f060254
org.levimc.launcher:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
org.levimc.launcher:integer/default_icon_animation_duration = 0x7f0a0006
org.levimc.launcher:id/fade = 0x7f0900cc
org.levimc.launcher:color/m3_chip_background_color = 0x7f050072
org.levimc.launcher:id/title_template = 0x7f090201
org.levimc.launcher:attr/forceApplySystemWindowInsetTop = 0x7f030207
org.levimc.launcher:style/Base.Theme.Material3.Dark.Dialog = 0x7f12005f
org.levimc.launcher:attr/targetId = 0x7f030436
org.levimc.launcher:dimen/design_navigation_item_icon_padding = 0x7f060079
org.levimc.launcher:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
org.levimc.launcher:attr/drawerLayoutStyle = 0x7f030193
org.levimc.launcher:attr/drawableTopCompat = 0x7f030190
org.levimc.launcher:id/header_title = 0x7f0900e8
org.levimc.launcher:attr/drawableTintMode = 0x7f03018f
org.levimc.launcher:id/fitXY = 0x7f0900d5
org.levimc.launcher:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f06017b
org.levimc.launcher:drawable/abc_ratingbar_indicator_material = 0x7f07005a
org.levimc.launcher:color/m3_chip_stroke_color = 0x7f050074
org.levimc.launcher:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700d0
org.levimc.launcher:attr/numericModifiers = 0x7f03035f
org.levimc.launcher:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
org.levimc.launcher:color/mtrl_chip_surface_color = 0x7f0502c6
org.levimc.launcher:color/material_personalized_color_surface_variant = 0x7f0502a1
org.levimc.launcher:color/mtrl_choice_chip_background_color = 0x7f0502c8
org.levimc.launcher:id/parallax = 0x7f090179
org.levimc.launcher:attr/layout_constraintVertical_weight = 0x7f0302a7
org.levimc.launcher:attr/layout_constraintWidth = 0x7f0302a8
org.levimc.launcher:attr/boxStrokeErrorColor = 0x7f030086
org.levimc.launcher:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600bf
org.levimc.launcher:color/design_dark_default_color_on_background = 0x7f050034
org.levimc.launcher:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0d0079
org.levimc.launcher:attr/ifTagNotSet = 0x7f030239
org.levimc.launcher:macro/m3_comp_time_picker_headline_type = 0x7f0d0151
org.levimc.launcher:color/m3_ref_palette_error60 = 0x7f0500fd
org.levimc.launcher:attr/colorPrimaryContainer = 0x7f030114
org.levimc.launcher:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f120458
org.levimc.launcher:attr/dividerVertical = 0x7f030183
org.levimc.launcher:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1201ad
org.levimc.launcher:dimen/abc_action_button_min_height_material = 0x7f06000d
org.levimc.launcher:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f120227
org.levimc.launcher:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f050090
org.levimc.launcher:macro/m3_comp_snackbar_supporting_text_color = 0x7f0d0115
org.levimc.launcher:attr/dividerThickness = 0x7f030182
org.levimc.launcher:string/resourcepack_detected_title = 0x7f1100ee
org.levimc.launcher:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0d0063
org.levimc.launcher:attr/dialogPreferredPadding = 0x7f030179
org.levimc.launcher:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f120447
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500b3
org.levimc.launcher:layout/item_version_group_title = 0x7f0c003e
org.levimc.launcher:id/sawtooth = 0x7f09019b
org.levimc.launcher:color/material_personalized_color_primary_text = 0x7f050292
org.levimc.launcher:attr/region_widthLessThan = 0x7f0303a6
org.levimc.launcher:attr/layout_keyline = 0x7f0302b8
org.levimc.launcher:color/bright_foreground_material_dark = 0x7f050026
org.levimc.launcher:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1202ca
org.levimc.launcher:id/showTitle = 0x7f0901ba
org.levimc.launcher:color/design_default_color_error = 0x7f050040
org.levimc.launcher:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0302de
org.levimc.launcher:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06014a
org.levimc.launcher:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0d011f
org.levimc.launcher:attr/defaultQueryHint = 0x7f030172
org.levimc.launcher:attr/layout_optimizationLevel = 0x7f0302ba
org.levimc.launcher:attr/alpha = 0x7f03002e
org.levimc.launcher:attr/layout_constraintBottom_creator = 0x7f030285
org.levimc.launcher:dimen/m3_navigation_rail_default_width = 0x7f0601c9
org.levimc.launcher:color/material_slider_inactive_tick_marks_color = 0x7f0502b0
org.levimc.launcher:attr/marginRightSystemWindowInsets = 0x7f0302db
org.levimc.launcher:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f120262
org.levimc.launcher:style/Base.Widget.AppCompat.Button.Small = 0x7f1200d9
org.levimc.launcher:attr/foregroundInsidePadding = 0x7f030209
org.levimc.launcher:attr/dayStyle = 0x7f03016e
org.levimc.launcher:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601cd
org.levimc.launcher:string/update_progress = 0x7f110106
org.levimc.launcher:string/unknown_sources_permission_message = 0x7f110103
org.levimc.launcher:color/m3_textfield_input_text_color = 0x7f050206
org.levimc.launcher:string/repair_libs_success_message = 0x7f1100eb
org.levimc.launcher:anim/abc_slide_out_bottom = 0x7f010008
org.levimc.launcher:attr/subheaderInsetStart = 0x7f030409
org.levimc.launcher:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
org.levimc.launcher:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1203c3
org.levimc.launcher:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f06016a
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1202cf
org.levimc.launcher:id/cos = 0x7f090093
org.levimc.launcher:attr/customStringValue = 0x7f03016b
org.levimc.launcher:dimen/mtrl_slider_label_padding = 0x7f0602e6
org.levimc.launcher:dimen/compat_notification_large_icon_max_height = 0x7f06005b
org.levimc.launcher:attr/customColorValue = 0x7f030164
org.levimc.launcher:attr/cornerRadius = 0x7f030151
org.levimc.launcher:attr/customIntegerValue = 0x7f030167
org.levimc.launcher:id/navigation_bar_item_icon_container = 0x7f090153
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0501aa
org.levimc.launcher:dimen/mtrl_snackbar_padding_horizontal = 0x7f0602f5
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f12015d
org.levimc.launcher:attr/actionModePasteDrawable = 0x7f030019
org.levimc.launcher:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0602c6
org.levimc.launcher:dimen/mtrl_btn_stroke_size = 0x7f06026b
org.levimc.launcher:attr/cardViewStyle = 0x7f0300a1
org.levimc.launcher:style/ThemeOverlay.Material3.Chip = 0x7f12029f
org.levimc.launcher:attr/customFloatValue = 0x7f030166
org.levimc.launcher:color/design_default_color_on_error = 0x7f050042
org.levimc.launcher:string/path_password_eye = 0x7f1100df
org.levimc.launcher:drawable/btn_radio_on_mtrl = 0x7f070089
org.levimc.launcher:color/m3_ref_palette_primary0 = 0x7f050128
org.levimc.launcher:attr/flow_lastVerticalBias = 0x7f0301f2
org.levimc.launcher:attr/ratingBarStyle = 0x7f03039c
org.levimc.launcher:color/m3_sys_color_dark_surface_container_lowest = 0x7f050178
org.levimc.launcher:id/match_constraint = 0x7f090113
org.levimc.launcher:attr/customColorDrawableValue = 0x7f030163
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f050197
org.levimc.launcher:string/mtrl_picker_announce_current_range_selection = 0x7f1100a5
org.levimc.launcher:drawable/notification_oversize_large_icon_bg = 0x7f0700f6
org.levimc.launcher:style/Theme.AppCompat.DialogWhenLarge = 0x7f120222
org.levimc.launcher:dimen/m3_comp_slider_active_handle_width = 0x7f060183
org.levimc.launcher:color/m3_ref_palette_dynamic_primary20 = 0x7f0500d2
org.levimc.launcher:style/CardView.Light = 0x7f12012c
org.levimc.launcher:attr/titleTextStyle = 0x7f0304a6
org.levimc.launcher:attr/yearStyle = 0x7f0304f0
org.levimc.launcher:color/abc_primary_text_disable_only_material_light = 0x7f05000a
org.levimc.launcher:string/repair = 0x7f1100e3
org.levimc.launcher:color/material_slider_active_track_color = 0x7f0502ae
org.levimc.launcher:attr/activeIndicatorLabelPadding = 0x7f030026
org.levimc.launcher:attr/drawableBottomCompat = 0x7f030188
org.levimc.launcher:dimen/mtrl_badge_horizontal_edge_offset = 0x7f06024b
org.levimc.launcher:attr/checkboxStyle = 0x7f0300b2
org.levimc.launcher:attr/cornerFamilyTopLeft = 0x7f03014f
org.levimc.launcher:attr/layout_constraintHeight_percent = 0x7f030295
org.levimc.launcher:style/Widget.Material3.Button.TextButton.Icon = 0x7f12036a
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f120311
org.levimc.launcher:style/TextAppearance.Material3.LabelSmall = 0x7f1201fa
org.levimc.launcher:attr/cornerFamilyBottomRight = 0x7f03014e
org.levimc.launcher:styleable/RecycleListView = 0x7f130075
org.levimc.launcher:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f120440
org.levimc.launcher:color/mtrl_switch_thumb_icon_tint = 0x7f0502e0
org.levimc.launcher:attr/endIconMinSize = 0x7f0301a5
org.levimc.launcher:attr/haloRadius = 0x7f03021b
org.levimc.launcher:styleable/DrawerArrowToggle = 0x7f13002f
org.levimc.launcher:style/Widget.MaterialComponents.FloatingActionButton = 0x7f120427
org.levimc.launcher:id/mtrl_picker_text_input_date = 0x7f09014c
org.levimc.launcher:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f03033b
org.levimc.launcher:drawable/design_snackbar_background = 0x7f070092
org.levimc.launcher:attr/colorPrimaryFixed = 0x7f030116
org.levimc.launcher:attr/coplanarSiblingViewId = 0x7f03014b
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f1202ee
org.levimc.launcher:attr/actionTextColorAlpha = 0x7f030024
org.levimc.launcher:attr/closeIconStartPadding = 0x7f0300e3
org.levimc.launcher:attr/deriveConstraintsFrom = 0x7f030177
org.levimc.launcher:attr/brightness = 0x7f030089
org.levimc.launcher:id/top = 0x7f090203
org.levimc.launcher:attr/itemShapeInsetTop = 0x7f030262
org.levimc.launcher:attr/clockHandColor = 0x7f0300dc
org.levimc.launcher:color/m3_hint_foreground = 0x7f05008e
org.levimc.launcher:style/Base.DialogWindowTitle.AppCompat = 0x7f120014
org.levimc.launcher:layout/abc_cascading_menu_item_layout = 0x7f0c000b
org.levimc.launcher:dimen/mtrl_calendar_header_content_padding = 0x7f06027e
org.levimc.launcher:color/design_dark_default_color_primary_dark = 0x7f05003a
org.levimc.launcher:string/repair_libs_error_message = 0x7f1100e8
org.levimc.launcher:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f060175
org.levimc.launcher:attr/actionBarItemBackground = 0x7f030003
org.levimc.launcher:drawable/ic_internet = 0x7f0700a3
org.levimc.launcher:string/installed_packages = 0x7f11005e
org.levimc.launcher:attr/contentPaddingEnd = 0x7f030142
org.levimc.launcher:attr/contentInsetStart = 0x7f03013e
org.levimc.launcher:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1202a3
org.levimc.launcher:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f120133
org.levimc.launcher:macro/m3_comp_bottom_app_bar_container_color = 0x7f0d0005
org.levimc.launcher:dimen/m3_searchbar_text_size = 0x7f0601e1
org.levimc.launcher:attr/contentInsetEndWithActions = 0x7f03013b
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f12046a
org.levimc.launcher:id/transitionToEnd = 0x7f090206
org.levimc.launcher:attr/fontStyle = 0x7f030204
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500b8
org.levimc.launcher:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0d002f
org.levimc.launcher:attr/colorTertiaryFixed = 0x7f03012d
org.levimc.launcher:dimen/design_fab_translation_z_hovered_focused = 0x7f060073
org.levimc.launcher:id/dragLeft = 0x7f0900b3
org.levimc.launcher:attr/minHeight = 0x7f03031b
org.levimc.launcher:attr/contentDescription = 0x7f030139
org.levimc.launcher:style/Platform.AppCompat.Light = 0x7f120142
org.levimc.launcher:attr/maxCharacterCount = 0x7f03030f
org.levimc.launcher:attr/commitIcon = 0x7f03012f
org.levimc.launcher:style/Base.Theme.SplashScreen.DayNight = 0x7f12007d
org.levimc.launcher:attr/applyMotionScene = 0x7f030037
org.levimc.launcher:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
org.levimc.launcher:style/Widget.AppCompat.Spinner.Underlined = 0x7f120338
org.levimc.launcher:color/material_dynamic_tertiary10 = 0x7f050258
org.levimc.launcher:color/m3_sys_color_dynamic_dark_primary_container = 0x7f050191
org.levimc.launcher:attr/hintAnimationEnabled = 0x7f030227
org.levimc.launcher:string/launch_later = 0x7f110065
org.levimc.launcher:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
org.levimc.launcher:attr/colorSurfaceContainer = 0x7f030122
org.levimc.launcher:attr/colorSurfaceBright = 0x7f030121
org.levimc.launcher:attr/backgroundTintMode = 0x7f030051
org.levimc.launcher:id/chain = 0x7f09007d
org.levimc.launcher:attr/buttonBarNegativeButtonStyle = 0x7f03008b
org.levimc.launcher:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0601cc
org.levimc.launcher:anim/abc_popup_exit = 0x7f010004
org.levimc.launcher:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f120395
org.levimc.launcher:attr/barLength = 0x7f030063
org.levimc.launcher:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f120015
org.levimc.launcher:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0d0096
org.levimc.launcher:attr/backgroundColor = 0x7f030048
org.levimc.launcher:attr/colorSecondaryFixed = 0x7f03011d
org.levimc.launcher:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301da
org.levimc.launcher:attr/colorSecondaryContainer = 0x7f03011c
org.levimc.launcher:attr/colorPrimaryInverse = 0x7f030118
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1201c1
org.levimc.launcher:dimen/mtrl_textinput_box_stroke_width_default = 0x7f0602ff
org.levimc.launcher:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1200e0
org.levimc.launcher:attr/backgroundOverlayColorAlpha = 0x7f03004d
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f12013b
org.levimc.launcher:dimen/m3_searchbar_margin_vertical = 0x7f0601dd
org.levimc.launcher:attr/colorSurfaceInverse = 0x7f030128
org.levimc.launcher:dimen/m3_btn_inset = 0x7f0600d7
org.levimc.launcher:attr/layout_editor_absoluteX = 0x7f0302ae
org.levimc.launcher:color/design_dark_default_color_on_secondary = 0x7f050037
org.levimc.launcher:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060154
org.levimc.launcher:attr/telltales_tailScale = 0x7f030438
org.levimc.launcher:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f07001f
org.levimc.launcher:styleable/SwitchMaterial = 0x7f130087
org.levimc.launcher:dimen/m3_badge_horizontal_offset = 0x7f0600b3
org.levimc.launcher:id/item_touch_helper_previous_elevation = 0x7f0900ff
org.levimc.launcher:attr/framePosition = 0x7f03020a
org.levimc.launcher:attr/autoTransition = 0x7f030045
org.levimc.launcher:dimen/m3_ripple_pressed_alpha = 0x7f0601d8
org.levimc.launcher:id/toggle = 0x7f090202
org.levimc.launcher:color/abc_secondary_text_material_dark = 0x7f050011
org.levimc.launcher:styleable/ClockFaceView = 0x7f130020
org.levimc.launcher:color/material_dynamic_neutral_variant100 = 0x7f050232
org.levimc.launcher:attr/tickColor = 0x7f03048c
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1202d7
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0d00d2
org.levimc.launcher:attr/marginHorizontal = 0x7f0302d9
org.levimc.launcher:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f120289
org.levimc.launcher:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f12008d
org.levimc.launcher:attr/errorIconTint = 0x7f0301b2
org.levimc.launcher:dimen/abc_list_item_height_material = 0x7f060031
org.levimc.launcher:color/m3_ref_palette_neutral22 = 0x7f050109
org.levimc.launcher:style/Theme.Material3.Light.Dialog.Alert = 0x7f12024b
org.levimc.launcher:attr/backgroundStacked = 0x7f03004f
org.levimc.launcher:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700e9
org.levimc.launcher:style/Widget.Material3.Slider.Legacy.Label = 0x7f1203de
org.levimc.launcher:style/Widget.Material3.CollapsingToolbar = 0x7f120386
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f120150
org.levimc.launcher:attr/colorOnSecondary = 0x7f030106
org.levimc.launcher:color/m3_sys_color_light_error = 0x7f0501ce
org.levimc.launcher:styleable/NavigationBarView = 0x7f13006b
org.levimc.launcher:attr/contentInsetRight = 0x7f03013d
org.levimc.launcher:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f120301
org.levimc.launcher:attr/colorSecondaryVariant = 0x7f03011f
org.levimc.launcher:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0d00e8
org.levimc.launcher:attr/helperTextTextAppearance = 0x7f030220
org.levimc.launcher:color/m3_sys_color_dark_inverse_surface = 0x7f050160
org.levimc.launcher:style/Base.ThemeOverlay.AppCompat.Light = 0x7f120085
org.levimc.launcher:string/material_clock_display_divider = 0x7f110078
org.levimc.launcher:attr/carousel_firstView = 0x7f0300a5
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0d0073
org.levimc.launcher:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0500f5
org.levimc.launcher:dimen/appcompat_dialog_background_inset = 0x7f060051
org.levimc.launcher:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f120153
org.levimc.launcher:color/m3_dynamic_default_color_secondary_text = 0x7f050083
org.levimc.launcher:attr/colorControlActivated = 0x7f0300f7
org.levimc.launcher:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f1203f7
org.levimc.launcher:attr/actionMenuTextColor = 0x7f030011
org.levimc.launcher:color/mtrl_navigation_item_text_color = 0x7f0502d9
org.levimc.launcher:drawable/material_ic_calendar_black_24dp = 0x7f0700bf
org.levimc.launcher:id/normal = 0x7f09015f
org.levimc.launcher:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070074
org.levimc.launcher:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0601c7
org.levimc.launcher:style/Widget.Material3.Chip.Input.Elevated = 0x7f120378
org.levimc.launcher:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1200cd
org.levimc.launcher:attr/thumbHeight = 0x7f03047f
org.levimc.launcher:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0d0107
org.levimc.launcher:attr/motionDurationExtraLong1 = 0x7f030327
org.levimc.launcher:color/m3_ref_palette_error95 = 0x7f050101
org.levimc.launcher:dimen/design_navigation_elevation = 0x7f060075
org.levimc.launcher:dimen/m3_btn_disabled_translation_z = 0x7f0600ce
org.levimc.launcher:attr/chipSurfaceColor = 0x7f0300d1
org.levimc.launcher:dimen/mtrl_switch_text_padding = 0x7f0602f6
org.levimc.launcher:attr/borderRound = 0x7f030074
org.levimc.launcher:layout/design_layout_tab_icon = 0x7f0c0024
org.levimc.launcher:id/bounceBoth = 0x7f090068
org.levimc.launcher:attr/colorError = 0x7f0300fa
org.levimc.launcher:attr/colorSurfaceDim = 0x7f030127
org.levimc.launcher:attr/behavior_fitToContents = 0x7f03006b
org.levimc.launcher:attr/colorAccent = 0x7f0300f3
org.levimc.launcher:color/m3_elevated_chip_background_color = 0x7f050088
org.levimc.launcher:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0d0139
org.levimc.launcher:attr/drawerArrowStyle = 0x7f030191
org.levimc.launcher:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0d00b8
org.levimc.launcher:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060166
org.levimc.launcher:attr/alertDialogCenterButtons = 0x7f03002a
org.levimc.launcher:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070086
org.levimc.launcher:attr/coordinatorLayoutStyle = 0x7f03014a
org.levimc.launcher:attr/buttonIconTint = 0x7f030093
org.levimc.launcher:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f1203fb
org.levimc.launcher:attr/overlapAnchor = 0x7f030368
org.levimc.launcher:attr/buttonGravity = 0x7f030090
org.levimc.launcher:attr/buttonIconTintMode = 0x7f030094
org.levimc.launcher:style/Theme.Material3.Light.SideSheetDialog = 0x7f12024f
org.levimc.launcher:attr/thumbRadius = 0x7f030484
org.levimc.launcher:dimen/m3_large_fab_size = 0x7f0601ba
org.levimc.launcher:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300f0
org.levimc.launcher:dimen/m3_sys_elevation_level5 = 0x7f0601f5
org.levimc.launcher:attr/singleChoiceItemLayout = 0x7f0303da
org.levimc.launcher:attr/collapsingToolbarLayoutLargeSize = 0x7f0300ed
org.levimc.launcher:attr/motionDurationLong4 = 0x7f03032e
org.levimc.launcher:styleable/FragmentContainerView = 0x7f13003a
org.levimc.launcher:attr/displayOptions = 0x7f03017b
org.levimc.launcher:style/Widget.Material3.BottomSheet.DragHandle = 0x7f12035a
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f060208
org.levimc.launcher:attr/chipIconEnabled = 0x7f0300c3
org.levimc.launcher:attr/collapseIcon = 0x7f0300e8
org.levimc.launcher:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f12028f
org.levimc.launcher:style/TextAppearance.AppCompat.Button = 0x7f1201a1
org.levimc.launcher:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f120169
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f05018b
org.levimc.launcher:attr/collapseContentDescription = 0x7f0300e7
org.levimc.launcher:drawable/abc_ic_search_api_material = 0x7f070048
org.levimc.launcher:attr/chipIcon = 0x7f0300c2
org.levimc.launcher:string/exit = 0x7f11004c
org.levimc.launcher:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0d00e0
org.levimc.launcher:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f06017d
org.levimc.launcher:id/x_right = 0x7f09022e
org.levimc.launcher:attr/closeItemLayout = 0x7f0300e6
org.levimc.launcher:attr/closeIconTint = 0x7f0300e4
org.levimc.launcher:color/m3_sys_color_dark_outline_variant = 0x7f05016d
org.levimc.launcher:attr/bottomSheetDialogTheme = 0x7f03007b
org.levimc.launcher:style/Widget.Material3.Snackbar = 0x7f1203df
org.levimc.launcher:attr/textAppearanceLargePopupMenu = 0x7f030451
org.levimc.launcher:attr/materialAlertDialogTheme = 0x7f0302df
org.levimc.launcher:string/storage_permission_not_granted = 0x7f1100fb
org.levimc.launcher:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
org.levimc.launcher:integer/m3_sys_motion_duration_medium4 = 0x7f0a001b
org.levimc.launcher:attr/borderWidth = 0x7f030076
org.levimc.launcher:color/m3_button_ripple_color = 0x7f050068
org.levimc.launcher:attr/iconTintMode = 0x7f030237
org.levimc.launcher:style/Base.Theme.AppCompat.CompactMenu = 0x7f12004f
org.levimc.launcher:attr/closeIconEndPadding = 0x7f0300e1
org.levimc.launcher:attr/appBarLayoutStyle = 0x7f030036
org.levimc.launcher:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
org.levimc.launcher:attr/closeIcon = 0x7f0300df
org.levimc.launcher:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0d0009
org.levimc.launcher:attr/carousel_infinite = 0x7f0300a7
org.levimc.launcher:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f12031f
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f05018d
org.levimc.launcher:id/src_atop = 0x7f0901ce
org.levimc.launcher:attr/clockIcon = 0x7f0300dd
org.levimc.launcher:style/Theme.Material3.Dark.Dialog = 0x7f120234
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f120044
org.levimc.launcher:attr/circularflow_defaultRadius = 0x7f0300d6
org.levimc.launcher:attr/floatingActionButtonStyle = 0x7f0301e5
org.levimc.launcher:attr/circularProgressIndicatorStyle = 0x7f0300d3
org.levimc.launcher:attr/alphabeticModifiers = 0x7f03002f
org.levimc.launcher:style/Platform.MaterialComponents = 0x7f120143
org.levimc.launcher:anim/m3_motion_fade_exit = 0x7f010024
org.levimc.launcher:id/save_overlay_view = 0x7f09019a
org.levimc.launcher:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002e
org.levimc.launcher:color/m3_ref_palette_dynamic_primary10 = 0x7f0500d0
org.levimc.launcher:color/material_personalized_color_primary_container = 0x7f050290
org.levimc.launcher:attr/chipStyle = 0x7f0300d0
org.levimc.launcher:attr/windowMinWidthMinor = 0x7f0304e9
org.levimc.launcher:dimen/m3_chip_icon_size = 0x7f0600f9
org.levimc.launcher:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0502d4
org.levimc.launcher:attr/actionBarTabStyle = 0x7f030009
org.levimc.launcher:attr/chipStrokeWidth = 0x7f0300cf
org.levimc.launcher:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0601be
org.levimc.launcher:style/Widget.Material3.Chip.Assist = 0x7f120373
org.levimc.launcher:animator/design_fab_show_motion_spec = 0x7f020002
org.levimc.launcher:attr/colorPrimaryDark = 0x7f030115
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0d0075
org.levimc.launcher:dimen/mtrl_alert_dialog_background_inset_start = 0x7f060248
org.levimc.launcher:string/repair_libs_in_progress = 0x7f1100ea
org.levimc.launcher:attr/materialIconButtonFilledTonalStyle = 0x7f0302ff
org.levimc.launcher:attr/chipStrokeColor = 0x7f0300ce
org.levimc.launcher:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0d00b2
org.levimc.launcher:attr/colorSecondary = 0x7f03011b
org.levimc.launcher:id/tv_title = 0x7f090214
org.levimc.launcher:attr/endIconCheckable = 0x7f0301a2
org.levimc.launcher:attr/textAppearanceButton = 0x7f030440
org.levimc.launcher:style/Widget.Design.TabLayout = 0x7f120347
org.levimc.launcher:attr/carousel_alignment = 0x7f0300a2
org.levimc.launcher:attr/firstBaselineToTopHeight = 0x7f0301d8
org.levimc.launcher:attr/colorTertiary = 0x7f03012b
org.levimc.launcher:dimen/design_fab_translation_z_pressed = 0x7f060074
org.levimc.launcher:attr/chipSpacingVertical = 0x7f0300cb
org.levimc.launcher:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1202b6
org.levimc.launcher:color/m3_sys_color_dynamic_light_on_error = 0x7f0501a6
org.levimc.launcher:attr/shapeAppearanceOverlay = 0x7f0303c5
org.levimc.launcher:attr/horizontalOffsetWithText = 0x7f03022e
org.levimc.launcher:string/dialog_message_delete_version = 0x7f110038
org.levimc.launcher:attr/iconGravity = 0x7f030232
org.levimc.launcher:style/TextAppearance.MaterialComponents.Headline4 = 0x7f12020b
org.levimc.launcher:attr/chipGroupStyle = 0x7f0300c1
org.levimc.launcher:attr/behavior_saveFlags = 0x7f030070
org.levimc.launcher:style/Widget.Material3.Slider.Legacy = 0x7f1203dd
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Display2 = 0x7f12001f
org.levimc.launcher:attr/checkedIconVisible = 0x7f0300bb
org.levimc.launcher:style/Widget.MaterialComponents.Slider = 0x7f120450
org.levimc.launcher:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0d015e
org.levimc.launcher:attr/contentPaddingTop = 0x7f030146
org.levimc.launcher:attr/checkedIconTint = 0x7f0300ba
org.levimc.launcher:color/m3_sys_color_light_surface_variant = 0x7f0501ec
org.levimc.launcher:styleable/AppBarLayout = 0x7f13000a
org.levimc.launcher:attr/waveOffset = 0x7f0304dc
org.levimc.launcher:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
org.levimc.launcher:attr/materialCalendarHeaderToggleButton = 0x7f0302ef
org.levimc.launcher:drawable/btn_checkbox_unchecked_mtrl = 0x7f070085
org.levimc.launcher:attr/behavior_significantVelocityThreshold = 0x7f030071
org.levimc.launcher:attr/onStateTransition = 0x7f030366
org.levimc.launcher:id/navigation_bar_item_large_label_view = 0x7f090156
org.levimc.launcher:attr/materialCalendarHeaderDivider = 0x7f0302eb
org.levimc.launcher:attr/checkedIcon = 0x7f0300b5
org.levimc.launcher:dimen/material_clock_face_margin_top = 0x7f060226
org.levimc.launcher:xml/network_security_config = 0x7f140001
org.levimc.launcher:attr/checkMarkTint = 0x7f0300b0
org.levimc.launcher:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f120229
org.levimc.launcher:interpolator/fast_out_slow_in = 0x7f0b0006
org.levimc.launcher:attr/actionModeCopyDrawable = 0x7f030016
org.levimc.launcher:attr/trackDecorationTint = 0x7f0304ba
org.levimc.launcher:attr/textAppearanceCaption = 0x7f030441
org.levimc.launcher:attr/endIconTint = 0x7f0301a8
org.levimc.launcher:layout/material_chip_input_combo = 0x7f0c0044
org.levimc.launcher:attr/endIconMode = 0x7f0301a6
org.levimc.launcher:macro/m3_comp_filled_tonal_button_container_color = 0x7f0d0052
org.levimc.launcher:attr/buttonStyle = 0x7f030096
org.levimc.launcher:id/contentPanel = 0x7f09008f
org.levimc.launcher:attr/layout_anchorGravity = 0x7f03027b
org.levimc.launcher:attr/constraintRotate = 0x7f030131
org.levimc.launcher:attr/autoShowKeyboard = 0x7f03003f
org.levimc.launcher:attr/closeIconEnabled = 0x7f0300e0
org.levimc.launcher:id/tabMode = 0x7f0901de
org.levimc.launcher:color/material_personalized_color_secondary_text = 0x7f050296
org.levimc.launcher:style/Base.Animation.AppCompat.DropDownUp = 0x7f120011
org.levimc.launcher:color/m3_ref_palette_neutral10 = 0x7f050104
org.levimc.launcher:attr/cardUseCompatPadding = 0x7f0300a0
org.levimc.launcher:attr/arcMode = 0x7f030038
org.levimc.launcher:color/m3_sys_color_on_tertiary_fixed = 0x7f0501f3
org.levimc.launcher:attr/colorSurface = 0x7f030120
org.levimc.launcher:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f12027b
org.levimc.launcher:color/cardview_dark_background = 0x7f05002c
org.levimc.launcher:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0d00cd
org.levimc.launcher:attr/subtitleTextColor = 0x7f03040f
org.levimc.launcher:attr/contentInsetLeft = 0x7f03013c
org.levimc.launcher:drawable/notification_bg_low = 0x7f0700f0
org.levimc.launcher:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1200f2
org.levimc.launcher:attr/carousel_backwardTransition = 0x7f0300a3
org.levimc.launcher:attr/buttonIcon = 0x7f030091
org.levimc.launcher:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f06023a
org.levimc.launcher:attr/collapsedTitleTextAppearance = 0x7f0300eb
org.levimc.launcher:attr/selectableItemBackgroundBorderless = 0x7f0303b9
org.levimc.launcher:attr/buttonBarPositiveButtonStyle = 0x7f03008d
org.levimc.launcher:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0d00bd
org.levimc.launcher:attr/backgroundTint = 0x7f030050
org.levimc.launcher:id/constraint = 0x7f09008c
org.levimc.launcher:attr/colorSecondaryFixedDim = 0x7f03011e
org.levimc.launcher:color/material_dynamic_secondary95 = 0x7f050255
org.levimc.launcher:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0d0033
org.levimc.launcher:id/expand_activities_button = 0x7f0900ca
org.levimc.launcher:anim/m3_side_sheet_enter_from_left = 0x7f010025
org.levimc.launcher:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f120233
org.levimc.launcher:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
org.levimc.launcher:attr/colorOnSurface = 0x7f03010a
org.levimc.launcher:attr/labelStyle = 0x7f030271
org.levimc.launcher:id/edge = 0x7f0900bd
org.levimc.launcher:dimen/mtrl_switch_thumb_size = 0x7f0602f9
org.levimc.launcher:color/material_dynamic_tertiary70 = 0x7f05025f
org.levimc.launcher:attr/colorOnPrimaryFixedVariant = 0x7f030104
org.levimc.launcher:color/material_dynamic_secondary0 = 0x7f05024a
org.levimc.launcher:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f120083
org.levimc.launcher:attr/materialCalendarDay = 0x7f0302e6
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500b4
org.levimc.launcher:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1100bf
org.levimc.launcher:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
org.levimc.launcher:id/easeIn = 0x7f0900b9
org.levimc.launcher:attr/checkedChip = 0x7f0300b4
org.levimc.launcher:attr/actionModeBackground = 0x7f030012
org.levimc.launcher:style/Widget.AppCompat.Toolbar = 0x7f12033b
org.levimc.launcher:attr/dialogTheme = 0x7f03017a
org.levimc.launcher:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f12021b
org.levimc.launcher:attr/textOutlineThickness = 0x7f030473
org.levimc.launcher:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060121
org.levimc.launcher:attr/colorErrorContainer = 0x7f0300fb
org.levimc.launcher:color/m3_ref_palette_dynamic_primary60 = 0x7f0500d6
org.levimc.launcher:id/month_grid = 0x7f090133
org.levimc.launcher:attr/materialSearchViewToolbarStyle = 0x7f030306
org.levimc.launcher:attr/cardBackgroundColor = 0x7f03009a
org.levimc.launcher:attr/motionDurationMedium4 = 0x7f030332
org.levimc.launcher:attr/colorPrimaryFixedDim = 0x7f030117
org.levimc.launcher:dimen/mtrl_slider_label_radius = 0x7f0602e7
org.levimc.launcher:style/Theme.MaterialComponents.NoActionBar = 0x7f12027d
org.levimc.launcher:attr/itemFillColor = 0x7f030250
org.levimc.launcher:attr/currentState = 0x7f03015e
org.levimc.launcher:drawable/m3_radiobutton_ripple = 0x7f0700b8
org.levimc.launcher:attr/placeholderText = 0x7f030383
org.levimc.launcher:attr/contentInsetStartWithNavigation = 0x7f03013f
org.levimc.launcher:attr/chipSpacing = 0x7f0300c9
org.levimc.launcher:attr/backgroundSplit = 0x7f03004e
org.levimc.launcher:string/mtrl_picker_toggle_to_year_selection = 0x7f1100c7
org.levimc.launcher:id/design_menu_item_action_area = 0x7f0900a3
org.levimc.launcher:attr/errorEnabled = 0x7f0301b0
org.levimc.launcher:attr/collapsingToolbarLayoutMediumSize = 0x7f0300ef
org.levimc.launcher:anim/abc_fade_out = 0x7f010001
org.levimc.launcher:styleable/ViewBackgroundHelper = 0x7f130095
org.levimc.launcher:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b0
org.levimc.launcher:id/motion_base = 0x7f090139
org.levimc.launcher:attr/boxCornerRadiusTopStart = 0x7f030084
org.levimc.launcher:anim/m3_side_sheet_enter_from_right = 0x7f010026
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500ad
org.levimc.launcher:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
org.levimc.launcher:attr/behavior_draggable = 0x7f030069
org.levimc.launcher:color/dim_foreground_material_dark = 0x7f050058
org.levimc.launcher:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f120428
org.levimc.launcher:color/design_dark_default_color_primary_variant = 0x7f05003b
org.levimc.launcher:attr/indicatorDirectionCircular = 0x7f030243
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1202d1
org.levimc.launcher:id/tv_version_code_group = 0x7f090215
org.levimc.launcher:attr/errorIconTintMode = 0x7f0301b3
org.levimc.launcher:drawable/abc_item_background_holo_light = 0x7f07004b
org.levimc.launcher:dimen/fastscroll_default_thickness = 0x7f060090
org.levimc.launcher:attr/barrierAllowsGoneWidgets = 0x7f030064
org.levimc.launcher:attr/motionEasingStandard = 0x7f03033f
org.levimc.launcher:style/Theme.MaterialComponents.DayNight = 0x7f120254
org.levimc.launcher:attr/backgroundInsetTop = 0x7f03004c
org.levimc.launcher:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f12018e
org.levimc.launcher:dimen/mtrl_card_dragged_z = 0x7f06029e
org.levimc.launcher:attr/actionProviderClass = 0x7f030023
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1201bc
org.levimc.launcher:string/import_confirmation_message = 0x7f110059
org.levimc.launcher:attr/actionMenuTextAppearance = 0x7f030010
org.levimc.launcher:id/outward = 0x7f090176
org.levimc.launcher:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f06027f
org.levimc.launcher:color/error = 0x7f05005a
org.levimc.launcher:dimen/m3_comp_navigation_rail_icon_size = 0x7f06014b
org.levimc.launcher:attr/constraintSet = 0x7f030132
org.levimc.launcher:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f120131
org.levimc.launcher:attr/cardCornerRadius = 0x7f03009b
org.levimc.launcher:attr/behavior_halfExpandedRatio = 0x7f03006c
org.levimc.launcher:drawable/indeterminate_static = 0x7f0700b2
org.levimc.launcher:styleable/AlertDialog = 0x7f130006
org.levimc.launcher:style/Widget.MaterialComponents.Tooltip = 0x7f120472
org.levimc.launcher:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1200c2
org.levimc.launcher:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
org.levimc.launcher:attr/paddingLeftSystemWindowInsets = 0x7f03036d
org.levimc.launcher:dimen/m3_fab_corner_size = 0x7f0601b6
org.levimc.launcher:attr/behavior_autoShrink = 0x7f030068
org.levimc.launcher:style/Widget.Support.CoordinatorLayout = 0x7f120473
org.levimc.launcher:style/Theme.Material3.Light.DialogWhenLarge = 0x7f12024d
org.levimc.launcher:attr/centerIfNoTextEnabled = 0x7f0300ad
org.levimc.launcher:id/checkbox = 0x7f090080
org.levimc.launcher:attr/textureWidth = 0x7f03047a
org.levimc.launcher:dimen/design_bottom_navigation_label_padding = 0x7f060067
org.levimc.launcher:attr/circularflow_defaultAngle = 0x7f0300d5
org.levimc.launcher:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f050210
org.levimc.launcher:attr/materialTimePickerTheme = 0x7f03030a
org.levimc.launcher:integer/m3_sys_motion_duration_long2 = 0x7f0a0015
org.levimc.launcher:attr/actionBarPopupTheme = 0x7f030004
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500b2
org.levimc.launcher:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f050189
org.levimc.launcher:attr/verticalOffsetWithText = 0x7f0304d2
org.levimc.launcher:style/Platform.MaterialComponents.Light = 0x7f120145
org.levimc.launcher:attr/textInputStyle = 0x7f030470
org.levimc.launcher:dimen/mtrl_calendar_navigation_top_padding = 0x7f06028d
org.levimc.launcher:color/material_personalized_color_secondary = 0x7f050294
org.levimc.launcher:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f12029a
org.levimc.launcher:attr/theme = 0x7f03047b
org.levimc.launcher:id/selected = 0x7f0901af
org.levimc.launcher:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f060288
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f05019a
org.levimc.launcher:macro/m3_sys_color_light_surface_tint = 0x7f0d0176
org.levimc.launcher:attr/errorContentDescription = 0x7f0301af
org.levimc.launcher:style/Widget.Material3.Toolbar.OnSurface = 0x7f1203f2
org.levimc.launcher:macro/m3_comp_search_bar_supporting_text_color = 0x7f0d00ee
org.levimc.launcher:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
org.levimc.launcher:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f120313
org.levimc.launcher:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f110097
org.levimc.launcher:attr/tintMode = 0x7f030496
org.levimc.launcher:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f05019b
org.levimc.launcher:attr/flow_lastVerticalStyle = 0x7f0301f3
org.levimc.launcher:attr/badgeWithTextShapeAppearanceOverlay = 0x7f030061
org.levimc.launcher:dimen/cardview_default_elevation = 0x7f060053
org.levimc.launcher:attr/reactiveGuide_applyToAllConstraintSets = 0x7f0303a0
org.levimc.launcher:attr/subMenuArrow = 0x7f030406
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f12013d
org.levimc.launcher:attr/badgeWithTextRadius = 0x7f03005f
org.levimc.launcher:id/animateToStart = 0x7f090054
org.levimc.launcher:id/accessibility_custom_action_26 = 0x7f090026
org.levimc.launcher:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070061
org.levimc.launcher:attr/badgeTextAppearance = 0x7f030059
org.levimc.launcher:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600c7
org.levimc.launcher:attr/tabStyle = 0x7f030432
org.levimc.launcher:attr/sideSheetModalStyle = 0x7f0303d5
org.levimc.launcher:attr/fabSize = 0x7f0301d2
org.levimc.launcher:drawable/avd_show_password = 0x7f070078
org.levimc.launcher:style/Widget.Material3.SearchView = 0x7f1203d4
org.levimc.launcher:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
org.levimc.launcher:dimen/mtrl_alert_dialog_background_inset_top = 0x7f060249
org.levimc.launcher:attr/textAppearanceTitleSmall = 0x7f03045f
org.levimc.launcher:style/Widget.MaterialComponents.Button = 0x7f12040a
org.levimc.launcher:attr/badgeTextColor = 0x7f03005a
org.levimc.launcher:attr/colorTertiaryContainer = 0x7f03012c
org.levimc.launcher:drawable/ic_arrow_back_black_24 = 0x7f070094
org.levimc.launcher:color/design_dark_default_color_on_primary = 0x7f050036
org.levimc.launcher:attr/arrowHeadLength = 0x7f030039
org.levimc.launcher:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f1202e6
org.levimc.launcher:string/already_latest_version = 0x7f11001d
org.levimc.launcher:attr/paddingBottomSystemWindowInsets = 0x7f03036b
org.levimc.launcher:attr/trackHeight = 0x7f0304bc
org.levimc.launcher:string/overlay_permission_not_granted = 0x7f1100da
org.levimc.launcher:color/m3_dynamic_hint_foreground = 0x7f050085
org.levimc.launcher:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070036
org.levimc.launcher:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f060204
org.levimc.launcher:dimen/m3_badge_with_text_offset = 0x7f0600b8
org.levimc.launcher:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1200e4
org.levimc.launcher:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0d0157
org.levimc.launcher:attr/behavior_overlapTop = 0x7f03006e
org.levimc.launcher:attr/badgeWithTextShapeAppearance = 0x7f030060
org.levimc.launcher:attr/colorPrimary = 0x7f030113
org.levimc.launcher:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0601bd
org.levimc.launcher:attr/actionModeSplitBackground = 0x7f03001d
org.levimc.launcher:attr/bottomSheetDragHandleStyle = 0x7f03007c
org.levimc.launcher:attr/logoScaleType = 0x7f0302d8
org.levimc.launcher:dimen/abc_text_size_medium_material = 0x7f060049
org.levimc.launcher:attr/autoSizeMaxTextSize = 0x7f030040
org.levimc.launcher:attr/showMotionSpec = 0x7f0303cf
org.levimc.launcher:dimen/splashscreen_icon_size_no_background = 0x7f060320
org.levimc.launcher:attr/autoCompleteMode = 0x7f03003d
org.levimc.launcher:attr/placeholderTextColor = 0x7f030385
org.levimc.launcher:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0b000d
org.levimc.launcher:attr/state_lifted = 0x7f0303ff
org.levimc.launcher:layout/dialog_settings = 0x7f0c0034
org.levimc.launcher:attr/textAppearanceDisplayMedium = 0x7f030443
org.levimc.launcher:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0d00e5
org.levimc.launcher:color/material_dynamic_tertiary40 = 0x7f05025c
org.levimc.launcher:dimen/mtrl_navigation_rail_text_size = 0x7f0602d3
org.levimc.launcher:attr/state_dragged = 0x7f0303fb
org.levimc.launcher:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06017c
org.levimc.launcher:id/always = 0x7f090052
org.levimc.launcher:attr/hideOnContentScroll = 0x7f030225
org.levimc.launcher:drawable/mtrl_tabs_default_indicator = 0x7f0700ec
org.levimc.launcher:color/m3_timepicker_display_text_color = 0x7f05020f
org.levimc.launcher:attr/colorOnTertiaryFixed = 0x7f03010f
org.levimc.launcher:attr/actionModeSelectAllDrawable = 0x7f03001b
org.levimc.launcher:drawable/mtrl_dropdown_arrow = 0x7f0700d4
org.levimc.launcher:style/Theme.AppCompat.NoActionBar = 0x7f12022b
org.levimc.launcher:attr/mock_diagonalsColor = 0x7f030320
org.levimc.launcher:attr/insetForeground = 0x7f030249
org.levimc.launcher:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f12019a
org.levimc.launcher:attr/reactiveGuide_valueId = 0x7f0303a2
org.levimc.launcher:drawable/design_ic_visibility = 0x7f07008f
org.levimc.launcher:string/allow_unknown_sources = 0x7f11001c
org.levimc.launcher:color/m3_sys_color_dark_on_error_container = 0x7f050163
org.levimc.launcher:color/call_notification_decline_color = 0x7f05002b
org.levimc.launcher:attr/windowFixedWidthMajor = 0x7f0304e6
org.levimc.launcher:attr/colorOnSurfaceInverse = 0x7f03010b
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f120040
org.levimc.launcher:dimen/m3_comp_search_bar_avatar_size = 0x7f06016e
org.levimc.launcher:attr/altSrc = 0x7f030030
org.levimc.launcher:dimen/mtrl_extended_fab_bottom_padding = 0x7f0602a6
org.levimc.launcher:color/m3_sys_color_on_primary_fixed_variant = 0x7f0501f0
org.levimc.launcher:attr/colorButtonNormal = 0x7f0300f5
org.levimc.launcher:attr/textInputFilledExposedDropdownMenuStyle = 0x7f03046a
org.levimc.launcher:dimen/m3_comp_search_bar_container_height = 0x7f060170
org.levimc.launcher:anim/abc_slide_out_top = 0x7f010009
org.levimc.launcher:id/mtrl_picker_text_input_range_start = 0x7f09014e
org.levimc.launcher:attr/addElevationShadow = 0x7f030028
org.levimc.launcher:macro/m3_comp_slider_disabled_handle_color = 0x7f0d010d
org.levimc.launcher:id/tag_unhandled_key_listeners = 0x7f0901ea
org.levimc.launcher:color/on_secondary = 0x7f0502f4
org.levimc.launcher:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1201ae
org.levimc.launcher:color/abc_tint_btn_checkable = 0x7f050013
org.levimc.launcher:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070071
org.levimc.launcher:attr/itemIconPadding = 0x7f030253
org.levimc.launcher:attr/isMaterialTheme = 0x7f03024d
org.levimc.launcher:attr/expandedTitleMarginTop = 0x7f0301bf
org.levimc.launcher:attr/flow_firstVerticalBias = 0x7f0301ea
org.levimc.launcher:attr/badgeWithTextWidth = 0x7f030062
org.levimc.launcher:color/primary_material_dark = 0x7f0502fb
org.levimc.launcher:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f12046c
org.levimc.launcher:attr/motionEasingStandardAccelerateInterpolator = 0x7f030340
org.levimc.launcher:dimen/m3_badge_offset = 0x7f0600b4
org.levimc.launcher:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1203e8
org.levimc.launcher:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
org.levimc.launcher:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1201b9
org.levimc.launcher:attr/snackbarStyle = 0x7f0303e0
org.levimc.launcher:attr/layout_collapseParallaxMultiplier = 0x7f03027e
org.levimc.launcher:attr/badgeGravity = 0x7f030052
org.levimc.launcher:attr/bottomInsetScrimEnabled = 0x7f030079
org.levimc.launcher:color/material_dynamic_primary95 = 0x7f050248
org.levimc.launcher:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1200a4
org.levimc.launcher:attr/marginLeftSystemWindowInsets = 0x7f0302da
org.levimc.launcher:style/Base.Widget.Material3.ActionMode = 0x7f120105
org.levimc.launcher:drawable/ic_call_answer_video_low = 0x7f070099
org.levimc.launcher:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f120116
org.levimc.launcher:attr/customNavigationLayout = 0x7f030168
org.levimc.launcher:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301dd
org.levimc.launcher:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f060127
org.levimc.launcher:attr/actionModeCloseContentDescription = 0x7f030014
org.levimc.launcher:dimen/design_bottom_navigation_text_size = 0x7f06006a
org.levimc.launcher:attr/floatingActionButtonLargeStyle = 0x7f0301db
org.levimc.launcher:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1202cb
org.levimc.launcher:attr/colorSurfaceContainerHighest = 0x7f030124
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f120034
org.levimc.launcher:animator/m3_extended_fab_show_motion_spec = 0x7f020013
org.levimc.launcher:color/m3_ref_palette_dynamic_primary99 = 0x7f0500db
org.levimc.launcher:dimen/mtrl_navigation_rail_compact_width = 0x7f0602cc
org.levimc.launcher:string/character_counter_pattern = 0x7f110031
org.levimc.launcher:animator/mtrl_fab_show_motion_spec = 0x7f02001f
org.levimc.launcher:style/Base.Widget.Material3.CollapsingToolbar = 0x7f120109
org.levimc.launcher:attr/cornerSizeBottomLeft = 0x7f030153
org.levimc.launcher:style/Base.Widget.AppCompat.RatingBar = 0x7f1200f6
org.levimc.launcher:anim/abc_tooltip_enter = 0x7f01000a
org.levimc.launcher:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f060178
org.levimc.launcher:id/accessibility_custom_action_28 = 0x7f090028
org.levimc.launcher:attr/dragDirection = 0x7f030184
org.levimc.launcher:style/Widget.MaterialComponents.NavigationView = 0x7f120449
org.levimc.launcher:id/fitEnd = 0x7f0900d2
org.levimc.launcher:attr/content = 0x7f030138
org.levimc.launcher:integer/m3_sys_motion_duration_long4 = 0x7f0a0017
org.levimc.launcher:drawable/ic_minecraft_cube = 0x7f0700aa
org.levimc.launcher:attr/borderlessButtonStyle = 0x7f030077
org.levimc.launcher:attr/colorPrimaryVariant = 0x7f03011a
org.levimc.launcher:dimen/abc_text_size_title_material = 0x7f06004f
org.levimc.launcher:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0502a7
org.levimc.launcher:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0501b5
org.levimc.launcher:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f12036b
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f12013c
org.levimc.launcher:color/m3_ref_palette_primary70 = 0x7f050130
org.levimc.launcher:attr/flow_padding = 0x7f0301f5
org.levimc.launcher:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
org.levimc.launcher:anim/abc_grow_fade_in_from_bottom = 0x7f010002
org.levimc.launcher:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070060
org.levimc.launcher:color/m3_default_color_primary_text = 0x7f05007b
org.levimc.launcher:attr/SharedValue = 0x7f030000
org.levimc.launcher:string/fab_transformation_sheet_behavior = 0x7f11004f
org.levimc.launcher:attr/actionModeWebSearchDrawable = 0x7f030020
org.levimc.launcher:attr/actionDropDownStyle = 0x7f03000e
org.levimc.launcher:attr/tabContentStart = 0x7f03041a
org.levimc.launcher:attr/carousel_emptyViewsBehavior = 0x7f0300a4
org.levimc.launcher:color/material_dynamic_neutral_variant60 = 0x7f050237
org.levimc.launcher:id/checked = 0x7f090081
org.levimc.launcher:color/bright_foreground_inverse_material_light = 0x7f050025
org.levimc.launcher:color/m3_sys_color_dark_on_surface_variant = 0x7f050169
org.levimc.launcher:attr/cornerSizeBottomRight = 0x7f030154
org.levimc.launcher:dimen/m3_card_hovered_z = 0x7f0600eb
org.levimc.launcher:attr/motionEffect_move = 0x7f030345
org.levimc.launcher:attr/actionBarSize = 0x7f030005
org.levimc.launcher:dimen/cardview_compat_inset_shadow = 0x7f060052
org.levimc.launcher:attr/actionOverflowMenuStyle = 0x7f030022
org.levimc.launcher:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060160
org.levimc.launcher:attr/buttonCompat = 0x7f03008f
org.levimc.launcher:id/scrollView = 0x7f0901a1
org.levimc.launcher:attr/hintTextAppearance = 0x7f030229
org.levimc.launcher:attr/textAppearanceHeadline6 = 0x7f03044a
org.levimc.launcher:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500b0
org.levimc.launcher:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f060256
org.levimc.launcher:id/accessibility_custom_action_29 = 0x7f090029
org.levimc.launcher:attr/extendedFloatingActionButtonStyle = 0x7f0301c6
org.levimc.launcher:attr/actionBarStyle = 0x7f030007
org.levimc.launcher:drawable/$avd_hide_password__2 = 0x7f070002
org.levimc.launcher:dimen/mtrl_chip_pressed_translation_z = 0x7f0602a1
org.levimc.launcher:attr/actionModeCloseDrawable = 0x7f030015
org.levimc.launcher:style/Base.Widget.AppCompat.EditText = 0x7f1200e2
org.levimc.launcher:dimen/material_clock_display_height = 0x7f060222
org.levimc.launcher:styleable/KeyAttribute = 0x7f130040
org.levimc.launcher:color/m3_ref_palette_secondary70 = 0x7f05013d
org.levimc.launcher:attr/textInputOutlinedDenseStyle = 0x7f03046d
org.levimc.launcher:attr/fontProviderSystemFontFamily = 0x7f030203
org.levimc.launcher:macro/m3_comp_checkbox_selected_container_color = 0x7f0d0006
org.levimc.launcher:attr/editTextColor = 0x7f03019a
org.levimc.launcher:attr/badgeStyle = 0x7f030057
org.levimc.launcher:anim/abc_popup_enter = 0x7f010003
org.levimc.launcher:attr/waveShape = 0x7f0304df
org.levimc.launcher:layout/item_settings_switch = 0x7f0c003b
org.levimc.launcher:anim/m3_side_sheet_exit_to_left = 0x7f010027
org.levimc.launcher:attr/itemPadding = 0x7f030258
org.levimc.launcher:attr/popupMenuStyle = 0x7f030389
org.levimc.launcher:style/Base.Theme.AppCompat.Light.Dialog = 0x7f120057
org.levimc.launcher:style/Base.AlertDialog.AppCompat = 0x7f12000e
org.levimc.launcher:color/m3_sys_color_light_outline = 0x7f0501de
org.levimc.launcher:string/abc_searchview_description_submit = 0x7f110016
org.levimc.launcher:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
org.levimc.launcher:id/message = 0x7f09012a
org.levimc.launcher:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
org.levimc.launcher:attr/counterOverflowTextColor = 0x7f03015a
org.levimc.launcher:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f12035e
org.levimc.launcher:attr/actionBarDivider = 0x7f030002
org.levimc.launcher:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f120237
org.levimc.launcher:style/TextAppearance.MaterialComponents.Headline2 = 0x7f120209
org.levimc.launcher:anim/design_bottom_sheet_slide_out = 0x7f010019
org.levimc.launcher:anim/m3_motion_fade_enter = 0x7f010023
org.levimc.launcher:color/material_dynamic_neutral20 = 0x7f050226
org.levimc.launcher:attr/counterEnabled = 0x7f030157
org.levimc.launcher:styleable/MaterialAutoCompleteTextView = 0x7f13004f
org.levimc.launcher:attr/textAppearanceBodyMedium = 0x7f03043e
org.levimc.launcher:animator/fragment_close_enter = 0x7f020003
org.levimc.launcher:string/material_timepicker_select_time = 0x7f11008c
org.levimc.launcher:dimen/m3_comp_assist_chip_container_height = 0x7f0600fa
org.levimc.launcher:style/Widget.Compat.NotificationActionText = 0x7f12033e
org.levimc.launcher:attr/itemTextAppearance = 0x7f030266
org.levimc.launcher:style/Base.Widget.Material3.ActionBar.Solid = 0x7f120104
org.levimc.launcher:string/theme_title = 0x7f110100
org.levimc.launcher:color/m3_ref_palette_white = 0x7f05014f
org.levimc.launcher:string/character_counter_overflowed_content_description = 0x7f110030
org.levimc.launcher:attr/flow_verticalStyle = 0x7f0301f9
org.levimc.launcher:attr/colorOnContainerUnchecked = 0x7f0300fe
org.levimc.launcher:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1201c0
org.levimc.launcher:attr/paddingTopNoTitle = 0x7f030371
org.levimc.launcher:style/Widget.AppCompat.EditText = 0x7f12030c
org.levimc.launcher:animator/mtrl_chip_state_list_anim = 0x7f020018
org.levimc.launcher:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f12013e
org.levimc.launcher:string/m3_sys_motion_easing_linear = 0x7f110074
org.levimc.launcher:anim/abc_tooltip_exit = 0x7f01000b
org.levimc.launcher:color/material_dynamic_neutral99 = 0x7f05022f
org.levimc.launcher:style/Widget.Design.BottomNavigationView = 0x7f120340
org.levimc.launcher:color/m3_navigation_item_icon_tint = 0x7f050094
org.levimc.launcher:color/material_dynamic_color_dark_error = 0x7f05021b
org.levimc.launcher:attr/background = 0x7f030047
org.levimc.launcher:style/Widget.AppCompat.SearchView = 0x7f120331
org.levimc.launcher:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f06014e
org.levimc.launcher:styleable/Motion = 0x7f130063
org.levimc.launcher:attr/actionModeCloseButtonStyle = 0x7f030013
org.levimc.launcher:color/mtrl_switch_track_tint = 0x7f0502e3
org.levimc.launcher:attr/largeFontVerticalOffsetAdjustment = 0x7f030273
org.levimc.launcher:color/m3_sys_color_light_surface_container_lowest = 0x7f0501ea
org.levimc.launcher:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f060213
org.levimc.launcher:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f0600fc
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary60 = 0x7f0500e3
org.levimc.launcher:id/circle_center = 0x7f090083
org.levimc.launcher:color/m3_ref_palette_dynamic_secondary99 = 0x7f0500e8
org.levimc.launcher:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600ca
org.levimc.launcher:id/search_button = 0x7f0901a5
org.levimc.launcher:attr/tabIndicatorHeight = 0x7f030424
org.levimc.launcher:attr/actionModeStyle = 0x7f03001e
org.levimc.launcher:style/Widget.Material3.Badge.AdjustToBounds = 0x7f120352
org.levimc.launcher:style/Platform.MaterialComponents.Light.Dialog = 0x7f120146
org.levimc.launcher:id/groups = 0x7f0900e5
org.levimc.launcher:attr/maxWidth = 0x7f030315
org.levimc.launcher:id/tag_unhandled_key_event_manager = 0x7f0901e9
org.levimc.launcher:drawable/bg_rounded_card = 0x7f070081
org.levimc.launcher:color/m3_ref_palette_secondary40 = 0x7f05013a
org.levimc.launcher:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0d0074
org.levimc.launcher:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
org.levimc.launcher:dimen/m3_fab_border_width = 0x7f0601b5
org.levimc.launcher:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f12003e
org.levimc.launcher:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600de
org.levimc.launcher:color/material_deep_teal_500 = 0x7f050219

<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="org.levimc.launcher" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/activity_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="300" endOffset="14"/></Target><Target id="@+id/header" view="LinearLayout"><Expressions/><location startLine="13" startOffset="4" endLine="68" endOffset="18"/></Target><Target id="@+id/settings_button" view="ImageButton"><Expressions/><location startLine="42" startOffset="8" endLine="50" endOffset="42"/></Target><Target id="@+id/language_button" view="ImageButton"><Expressions/><location startLine="52" startOffset="8" endLine="61" endOffset="42"/></Target><Target id="@+id/main_card" view="LinearLayout"><Expressions/><location startLine="78" startOffset="8" endLine="202" endOffset="22"/></Target><Target id="@+id/abi_label" view="TextView"><Expressions/><location startLine="103" startOffset="16" endLine="116" endOffset="66"/></Target><Target id="@+id/import_apk_button" view="Button"><Expressions/><location startLine="118" startOffset="16" endLine="130" endOffset="62"/></Target><Target id="@+id/delete_version_button" view="ImageButton"><Expressions/><location startLine="137" startOffset="16" endLine="146" endOffset="50"/></Target><Target id="@+id/text_minecraft_version" view="TextView"><Expressions/><location startLine="157" startOffset="16" endLine="169" endOffset="55"/></Target><Target id="@+id/select_version_button" view="ImageButton"><Expressions/><location startLine="171" startOffset="16" endLine="178" endOffset="54"/></Target><Target id="@+id/progress_loader" view="ProgressBar"><Expressions/><location startLine="180" startOffset="16" endLine="186" endOffset="47"/></Target><Target id="@+id/launch_button" view="Button"><Expressions/><location startLine="188" startOffset="16" endLine="200" endOffset="59"/></Target><Target id="@+id/mod_card" view="LinearLayout"><Expressions/><location startLine="213" startOffset="12" endLine="261" endOffset="26"/></Target><Target id="@+id/mods_title_text" view="TextView"><Expressions/><location startLine="229" startOffset="20" endLine="237" endOffset="47"/></Target><Target id="@+id/add_mod_button" view="ImageButton"><Expressions/><location startLine="244" startOffset="20" endLine="250" endOffset="56"/></Target><Target id="@+id/mods_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="253" startOffset="17" endLine="260" endOffset="49"/></Target><Target id="@+id/about_card" view="LinearLayout"><Expressions/><location startLine="263" startOffset="12" endLine="297" endOffset="26"/></Target><Target id="@+id/github_icon" view="ImageView"><Expressions/><location startLine="290" startOffset="20" endLine="295" endOffset="54"/></Target></Targets></Layout>
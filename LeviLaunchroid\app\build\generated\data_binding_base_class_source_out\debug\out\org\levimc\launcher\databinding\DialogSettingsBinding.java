// Generated by view binder compiler. Do not edit!
package org.levimc.launcher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.levimc.launcher.R;

public final class DialogSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final LinearLayout rootLayout;

  @NonNull
  public final TextView settingsDesc;

  @NonNull
  public final LinearLayout settingsItems;

  @NonNull
  public final TextView settingsTitle;

  private DialogSettingsBinding(@NonNull ScrollView rootView, @NonNull LinearLayout rootLayout,
      @NonNull TextView settingsDesc, @NonNull LinearLayout settingsItems,
      @NonNull TextView settingsTitle) {
    this.rootView = rootView;
    this.rootLayout = rootLayout;
    this.settingsDesc = settingsDesc;
    this.settingsItems = settingsItems;
    this.settingsTitle = settingsTitle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.root_layout;
      LinearLayout rootLayout = ViewBindings.findChildViewById(rootView, id);
      if (rootLayout == null) {
        break missingId;
      }

      id = R.id.settings_desc;
      TextView settingsDesc = ViewBindings.findChildViewById(rootView, id);
      if (settingsDesc == null) {
        break missingId;
      }

      id = R.id.settings_items;
      LinearLayout settingsItems = ViewBindings.findChildViewById(rootView, id);
      if (settingsItems == null) {
        break missingId;
      }

      id = R.id.settings_title;
      TextView settingsTitle = ViewBindings.findChildViewById(rootView, id);
      if (settingsTitle == null) {
        break missingId;
      }

      return new DialogSettingsBinding((ScrollView) rootView, rootLayout, settingsDesc,
          settingsItems, settingsTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

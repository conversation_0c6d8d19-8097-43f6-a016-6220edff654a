{
    files = {
        [[src\main.cpp]]
    },
    depfiles = "build\\.objs\\leviutils\\android\\arm64-v8a\\release\\src\\__cpp_main.cpp.cpp:   src/main.cpp src/gui/gui.h src/imgui/imgui.h src/imgui/imconfig.h   src/imgui/imgui_impl_android.h src/imgui/imgui_impl_opengl3.h   src/imgui/imgui_internal.h src/imgui/imstb_textedit.h   src/gui/renderer.h\
",
    depfiles_format = "gcc",
    values = {
        [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        {
            [[--sysroot=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android]],
            "-nostdinc++",
            "-Qunused-arguments",
            "--target=aarch64-none-linux-android21",
            "-fPIC",
            "-Oz",
            "-std=c++20",
            "-Isrc",
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++\include]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++\libs\arm64-v8a\include]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++abi\include]],
            "-O3",
            "-DNDEBUG"
        }
    }
}
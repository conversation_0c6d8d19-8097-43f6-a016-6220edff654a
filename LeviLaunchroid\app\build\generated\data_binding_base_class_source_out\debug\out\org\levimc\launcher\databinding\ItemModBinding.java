// Generated by view binder compiler. Do not edit!
package org.levimc.launcher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.levimc.launcher.R;

public final class ItemModBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView dragHandle;

  @NonNull
  public final TextView modName;

  @NonNull
  public final TextView modOrder;

  @NonNull
  public final Switch modSwitch;

  private ItemModBinding(@NonNull LinearLayout rootView, @NonNull ImageView dragHandle,
      @NonNull TextView modName, @NonNull TextView modOrder, @NonNull Switch modSwitch) {
    this.rootView = rootView;
    this.dragHandle = dragHandle;
    this.modName = modName;
    this.modOrder = modOrder;
    this.modSwitch = modSwitch;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemModBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemModBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_mod, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemModBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drag_handle;
      ImageView dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.mod_name;
      TextView modName = ViewBindings.findChildViewById(rootView, id);
      if (modName == null) {
        break missingId;
      }

      id = R.id.mod_order;
      TextView modOrder = ViewBindings.findChildViewById(rootView, id);
      if (modOrder == null) {
        break missingId;
      }

      id = R.id.mod_switch;
      Switch modSwitch = ViewBindings.findChildViewById(rootView, id);
      if (modSwitch == null) {
        break missingId;
      }

      return new ItemModBinding((LinearLayout) rootView, dragHandle, modName, modOrder, modSwitch);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

// Generated by view binder compiler. Do not edit!
package org.levimc.launcher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.levimc.launcher.R;

public final class AlertDialogCustomBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnNegative;

  @NonNull
  public final Button btnNeutral;

  @NonNull
  public final Button btnPositive;

  @NonNull
  public final View btnSpacingNegNeu;

  @NonNull
  public final View btnSpacingNeuPos;

  @NonNull
  public final TextView tvMessage;

  @NonNull
  public final TextView tvTitle;

  private AlertDialogCustomBinding(@NonNull LinearLayout rootView, @NonNull Button btnNegative,
      @NonNull Button btnNeutral, @NonNull Button btnPositive, @NonNull View btnSpacingNegNeu,
      @NonNull View btnSpacingNeuPos, @NonNull TextView tvMessage, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnNegative = btnNegative;
    this.btnNeutral = btnNeutral;
    this.btnPositive = btnPositive;
    this.btnSpacingNegNeu = btnSpacingNegNeu;
    this.btnSpacingNeuPos = btnSpacingNeuPos;
    this.tvMessage = tvMessage;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static AlertDialogCustomBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static AlertDialogCustomBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.alert_dialog_custom, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static AlertDialogCustomBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_negative;
      Button btnNegative = ViewBindings.findChildViewById(rootView, id);
      if (btnNegative == null) {
        break missingId;
      }

      id = R.id.btn_neutral;
      Button btnNeutral = ViewBindings.findChildViewById(rootView, id);
      if (btnNeutral == null) {
        break missingId;
      }

      id = R.id.btn_positive;
      Button btnPositive = ViewBindings.findChildViewById(rootView, id);
      if (btnPositive == null) {
        break missingId;
      }

      id = R.id.btn_spacing_neg_neu;
      View btnSpacingNegNeu = ViewBindings.findChildViewById(rootView, id);
      if (btnSpacingNegNeu == null) {
        break missingId;
      }

      id = R.id.btn_spacing_neu_pos;
      View btnSpacingNeuPos = ViewBindings.findChildViewById(rootView, id);
      if (btnSpacingNeuPos == null) {
        break missingId;
      }

      id = R.id.tv_message;
      TextView tvMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvMessage == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new AlertDialogCustomBinding((LinearLayout) rootView, btnNegative, btnNeutral,
          btnPositive, btnSpacingNegNeu, btnSpacingNeuPos, tvMessage, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

// Generated by view binder compiler. Do not edit!
package org.levimc.launcher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.levimc.launcher.R;

public final class ItemVersionGroupTitleBinding implements ViewBinding {
  @NonNull
  private final TextView rootView;

  @NonNull
  public final TextView tvVersionCodeGroup;

  private ItemVersionGroupTitleBinding(@NonNull TextView rootView,
      @NonNull TextView tvVersionCodeGroup) {
    this.rootView = rootView;
    this.tvVersionCodeGroup = tvVersionCodeGroup;
  }

  @Override
  @NonNull
  public TextView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemVersionGroupTitleBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemVersionGroupTitleBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_version_group_title, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemVersionGroupTitleBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    TextView tvVersionCodeGroup = (TextView) rootView;

    return new ItemVersionGroupTitleBinding((TextView) rootView, tvVersionCodeGroup);
  }
}

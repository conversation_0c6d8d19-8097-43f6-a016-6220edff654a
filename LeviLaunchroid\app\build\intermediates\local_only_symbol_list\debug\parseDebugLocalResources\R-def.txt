R_DEF: Internal format may change without notice
local
anim slide_in_top
color background
color error
color on_background
color on_error
color on_primary
color on_secondary
color on_surface
color on_tertiary
color outline
color primary
color secondary
color surface
color tertiary
drawable background_gradient
drawable bg_abi_arm64_v8a
drawable bg_abi_armeabi_v7a
drawable bg_abi_default
drawable bg_abi_x86
drawable bg_abi_x86_64
drawable bg_item_rounded
drawable bg_round_gradient
drawable bg_rounded_card
drawable bg_third_level_item
drawable card_background
drawable ic_add
drawable ic_arrow_down
drawable ic_check
drawable ic_delete
drawable ic_drag_handle
drawable ic_game_icon
drawable ic_github
drawable ic_internet
drawable ic_launch
drawable ic_leaf_logo
drawable ic_minecraft_cube
drawable ic_settings
font misans
id abi_label
id about_card
id action_chinese
id action_english
id action_russian
id add_mod_button
id btn_action
id btn_cancel
id btn_install
id btn_negative
id btn_neutral
id btn_positive
id btn_spacing_neg_neu
id btn_spacing_neu_pos
id delete_version_button
id drag_handle
id edit_value
id edit_version_name
id github_icon
id guideline_vertical
id header
id imgLeaf
id import_apk_button
id language_button
id launch_button
id linear_parent
id main_card
id mod_card
id mod_name
id mod_order
id mod_switch
id mods_recycler
id mods_title_text
id progress_bar
id progress_loader
id progress_text
id recycler_versions
id root_layout
id select_version_button
id settings_button
id settings_desc
id settings_items
id settings_title
id spinner_value
id switch_value
id text_minecraft_version
id text_version_error
id title
id tvAppName
id tv_big_group_title
id tv_message
id tv_title
id tv_version_code_group
id tv_version_name_item
layout activity_main
layout activity_splash
layout alert_dialog_custom
layout dialog_apk_version_confirm
layout dialog_game_version_select
layout dialog_install_progress
layout dialog_libs_repair
layout dialog_loading
layout dialog_settings
layout item_mod
layout item_settings_button
layout item_settings_edittext
layout item_settings_spinner
layout item_settings_switch
layout item_version
layout item_version_big_group
layout item_version_group_title
menu language_menu
mipmap ic_launcher
mipmap ic_launcher_adaptive_back
mipmap ic_launcher_adaptive_fore
string about_title
string allow_unknown_sources
string already_latest_version
string app_name
string cancel
string check_update
string chinese
string confirm
string copyright
string dialog_message_delete_mod
string dialog_message_delete_version
string dialog_negative_cancel
string dialog_positive_delete
string dialog_title_delete_mod
string dialog_title_delete_version
string download_update
string downloading_update
string drag_to_reorder
string enable_debug_log
string english
string error
string error_delete_builtin_version
string error_no_browser
string error_versions
string eula_agree
string eula_exit
string eula_message
string eula_title
string exit
string files_processed
string files_processing_error
string font_license
string grant_permission
string ignore_this_version
string illegal_apk_title
string import_apk
string import_confirmation_message
string import_confirmation_title
string install
string install_done
string install_failed
string installed_packages
string installing_message
string installing_title
string invalid_mod_file
string language
string launch
string launch_later
string launch_now
string local_custom
string minecraft
string missing_libs_message
string missing_libs_title
string mod_load_order
string mod_reordered
string mods_title
string name_invalid
string new_version_found
string no_install_minecraft
string no_minecraft
string no_mods_found
string not_found_version
string not_mc_apk
string overlay_permission_message
string overlay_permission_not_granted
string overwrite
string overwrite_file_message
string overwrite_file_title
string repair
string repair_completed
string repair_error
string repair_failed
string repair_libs_dialog_title
string repair_libs_error_message
string repair_libs_failed_message
string repair_libs_in_progress
string repair_libs_success_message
string requires_repair
string resourcepack_detected_message
string resourcepack_detected_title
string russian
string settings_desc
string settings_title
string skip
string storage_permission_message
string storage_permission_not_granted
string storage_permission_title
string theme_dark
string theme_follow_system
string theme_light
string theme_title
string toast_delete_failed
string toast_delete_success
string unknown_sources_permission_message
string unknown_sources_permission_title
string update_failed
string update_progress
string update_question
string user_cancelled
string version
string version_ignored
string version_isolation
string version_name
string version_prefix
style AppFullScreenTheme
style AppTheme.Dark
style AppTheme.Light
style Base.Theme.FullScreen
xml file_provider_paths
xml network_security_config

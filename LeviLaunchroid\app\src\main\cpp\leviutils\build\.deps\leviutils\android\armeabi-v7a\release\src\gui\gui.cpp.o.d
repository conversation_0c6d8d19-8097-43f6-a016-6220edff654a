{
    values = {
        [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        {
            [[--sysroot=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\arm-linux-androideabi]],
            "-nostdinc++",
            "-Qunused-arguments",
            "--target=armv7-none-linux-androideabi21",
            "-mthumb",
            "-fPIC",
            "-Oz",
            "-std=c++20",
            "-Isrc",
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++\include]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++\libs\armeabi-v7a\include]],
            "-isystem",
            [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\sources\cxx-stl\llvm-libc++abi\include]],
            "-O3",
            "-DNDEBUG"
        }
    },
    depfiles_format = "gcc",
    files = {
        [[src\gui\gui.cpp]]
    },
    depfiles = "build\\.objs\\leviutils\\android\\armeabi-v7a\\release\\src\\gui\\__cpp_gui.cpp.cpp:   src/gui/gui.cpp src/gui/gui.h src/imgui/imgui.h src/imgui/imconfig.h   src/imgui/imgui_impl_android.h src/imgui/imgui_impl_opengl3.h   src/imgui/imgui_internal.h src/imgui/imstb_textedit.h   src/gui/renderer.h\
"
}
#Wed Jul 30 04:54:07 BDT 2025
org.levimc.launcher.app-main-34\:/anim/slide_in_top.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_top.xml.flat
org.levimc.launcher.app-main-34\:/drawable/background_gradient.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_background_gradient.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_abi_arm64_v8a.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_abi_arm64_v8a.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_abi_armeabi_v7a.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_abi_armeabi_v7a.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_abi_default.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_abi_default.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_abi_x86.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_abi_x86.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_abi_x86_64.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_abi_x86_64.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_item_rounded.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_item_rounded.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_round_gradient.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_round_gradient.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_rounded_card.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_rounded_card.xml.flat
org.levimc.launcher.app-main-34\:/drawable/bg_third_level_item.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_third_level_item.xml.flat
org.levimc.launcher.app-main-34\:/drawable/card_background.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_card_background.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_add.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_arrow_down.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_down.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_check.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_delete.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_drag_handle.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_drag_handle.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_game_icon.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_game_icon.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_github.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_github.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_internet.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_internet.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_launch.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launch.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_leaf_logo.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_leaf_logo.png.flat
org.levimc.launcher.app-main-34\:/drawable/ic_minecraft_cube.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_minecraft_cube.xml.flat
org.levimc.launcher.app-main-34\:/drawable/ic_settings.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
org.levimc.launcher.app-main-34\:/font/misans.ttf=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_misans.ttf.flat
org.levimc.launcher.app-main-34\:/menu/language_menu.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_language_menu.xml.flat
org.levimc.launcher.app-main-34\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
org.levimc.launcher.app-main-34\:/mipmap-hdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
org.levimc.launcher.app-main-34\:/mipmap-hdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_adaptive_back.png.flat
org.levimc.launcher.app-main-34\:/mipmap-hdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_adaptive_fore.png.flat
org.levimc.launcher.app-main-34\:/mipmap-mdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
org.levimc.launcher.app-main-34\:/mipmap-mdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_adaptive_back.png.flat
org.levimc.launcher.app-main-34\:/mipmap-mdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_adaptive_fore.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xhdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xhdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_adaptive_back.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xhdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_adaptive_fore.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xxhdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xxhdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_adaptive_back.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xxhdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_adaptive_fore.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xxxhdpi/ic_launcher.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xxxhdpi/ic_launcher_adaptive_back.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_adaptive_back.png.flat
org.levimc.launcher.app-main-34\:/mipmap-xxxhdpi/ic_launcher_adaptive_fore.png=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_adaptive_fore.png.flat
org.levimc.launcher.app-main-34\:/xml/file_provider_paths.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_provider_paths.xml.flat
org.levimc.launcher.app-main-34\:/xml/network_security_config.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/activity_main.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/activity_splash.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_splash.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/alert_dialog_custom.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_alert_dialog_custom.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/dialog_apk_version_confirm.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_apk_version_confirm.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/dialog_game_version_select.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_game_version_select.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/dialog_install_progress.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_install_progress.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/dialog_libs_repair.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_libs_repair.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/dialog_loading.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_loading.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/dialog_settings.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_settings.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/item_mod.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_mod.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/item_settings_button.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_settings_button.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/item_settings_edittext.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_settings_edittext.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/item_settings_spinner.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_settings_spinner.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/item_settings_switch.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_settings_switch.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/item_version.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_version.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/item_version_big_group.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_version_big_group.xml.flat
org.levimc.launcher.app-mergeDebugResources-31\:/layout/item_version_group_title.xml=D\:\\Levi\\LeviLaunchroid\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_version_group_title.xml.flat

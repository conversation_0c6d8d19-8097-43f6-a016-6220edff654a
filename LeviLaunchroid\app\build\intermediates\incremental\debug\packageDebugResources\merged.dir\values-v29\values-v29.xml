<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppTheme.Dark" parent="Base.Theme.FullScreen">
        <item name="android:forceDarkAllowed">true</item>
    </style>
    <style name="AppTheme.Light" parent="Base.Theme.FullScreen">
        <item name="android:forceDarkAllowed">false</item>
    </style>
    <style name="Base.Theme.FullScreen" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorTertiary">@color/tertiary</item>
        <item name="colorOnTertiary">@color/on_tertiary</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOutline">@color/outline</item>

        <item name="android:windowBackground">@color/background</item>

        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>

        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>

    </style>
</resources>
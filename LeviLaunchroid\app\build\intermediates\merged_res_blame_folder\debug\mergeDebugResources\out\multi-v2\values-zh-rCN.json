{"logs": [{"outputFile": "org.levimc.launcher.app-mergeDebugResources-30:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e65fc3b7d0a9477c2db825c7f4882f80\\transformed\\core-1.13.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "42,43,44,45,46,47,48,200", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3422,3514,3615,3709,3803,3896,3990,13522", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3509,3610,3704,3798,3891,3985,4081,13618"}}, {"source": "D:\\Levi\\LeviLaunchroid\\app\\src\\main\\res\\values-zh-rCN\\strings.xml", "from": {"startLines": "15,118,89,2,43,36,137,31,16,131,121,123,122,130,120,84,91,11,74,136,50,126,111,128,115,116,114,113,54,46,47,17,42,85,66,60,26,25,61,64,88,33,72,71,127,135,5,23,22,34,3,101,100,10,12,8,63,82,52,53,9,69,67,56,57,29,28,27,102,94,98,96,103,99,97,93,95,104,21,20,138,77,76,30,41,58,40,109,107,108,106,125,124,80,79,87,86,83,48,4,90,133,62,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "520,5169,3658,38,1754,1437,6081,1292,566,5841,5299,5434,5379,5781,5237,3384,3777,390,2920,6036,1992,5604,4746,5730,5082,5125,4860,4802,2151,1818,1877,631,1703,3434,2639,2394,1012,952,2440,2582,3601,1334,2858,2805,5671,5995,178,903,858,1387,89,4326,4265,338,443,238,2527,3268,2032,2096,289,2750,2692,2190,2255,1213,1145,1089,4398,3898,4149,4026,4437,4196,4074,3835,3949,4499,779,713,6123,3027,2980,1255,1622,2324,1561,4699,4599,4653,4553,5544,5489,3147,3080,3547,3489,3326,1939,138,3724,5912,2480,1484", "endColumns": "44,64,64,49,37,45,40,38,63,67,78,53,53,58,60,48,54,51,56,43,36,65,52,47,41,40,220,56,35,57,60,56,49,53,51,44,75,58,38,53,55,51,58,51,57,39,37,45,43,46,47,70,59,50,49,49,53,56,62,53,47,51,54,63,67,40,66,54,37,49,45,46,60,67,73,61,75,50,77,64,43,49,45,35,79,66,59,43,52,44,44,58,53,117,65,52,56,56,49,38,51,50,45,47", "endOffsets": "560,5229,3718,83,1787,1478,6117,1326,625,5904,5373,5483,5428,5835,5293,3428,3827,437,2972,6075,2024,5665,4794,5773,5119,5161,5076,4854,2182,1871,1933,683,1748,3483,2686,2434,1083,1006,2474,2631,3652,1381,2912,2852,5724,6030,211,944,897,1429,132,4392,4320,384,488,283,2576,3320,2090,2145,332,2797,2742,2249,2318,1249,1207,1139,4431,3943,4190,4068,4493,4259,4143,3892,4020,4545,852,773,6162,3072,3021,1286,1697,2386,1616,4738,4647,4693,4593,5598,5538,3260,3141,3595,3541,3378,1984,172,3771,5958,2521,1527"}, "to": {"startLines": "33,34,35,36,49,52,53,55,56,57,58,59,60,61,62,63,64,65,66,67,68,70,72,73,74,75,76,77,78,80,81,82,83,85,86,87,88,89,90,91,92,93,94,95,96,98,99,100,101,102,116,117,118,119,120,121,167,168,169,170,171,172,173,174,175,176,177,178,180,181,182,183,184,185,186,187,188,189,190,191,192,196,197,199,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2899,2964,3029,4086,4321,4367,4481,4520,4584,4652,4731,4785,4839,4898,4959,5008,5063,5115,5172,5216,5304,5432,5485,5533,5575,5616,5837,5894,6007,6065,6126,6183,6292,6346,6398,6443,6519,6578,6617,6671,6727,6779,6838,6890,7007,7047,7085,7131,7175,8055,8103,8174,8234,8285,8335,11586,11640,11697,11760,11814,11862,11914,11969,12033,12101,12142,12209,12333,12371,12421,12467,12514,12575,12643,12717,12779,12855,12906,12984,13049,13320,13370,13486,13623,13703,13770,13830,13874,13927,13972,14017,14076,14130,14248,14314,14367,14424,14481,14531,14570,14622,14673,14719", "endColumns": "44,64,64,49,37,45,40,38,63,67,78,53,53,58,60,48,54,51,56,43,36,65,52,47,41,40,220,56,35,57,60,56,49,53,51,44,75,58,38,53,55,51,58,51,57,39,37,45,43,46,47,70,59,50,49,49,53,56,62,53,47,51,54,63,67,40,66,54,37,49,45,46,60,67,73,61,75,50,77,64,43,49,45,35,79,66,59,43,52,44,44,58,53,117,65,52,56,56,49,38,51,50,45,47", "endOffsets": "2894,2959,3024,3074,4119,4362,4403,4515,4579,4647,4726,4780,4834,4893,4954,5003,5058,5110,5167,5211,5248,5365,5480,5528,5570,5611,5832,5889,5925,6060,6121,6178,6228,6341,6393,6438,6514,6573,6612,6666,6722,6774,6833,6885,6943,7042,7080,7126,7170,7217,8098,8169,8229,8280,8330,8380,11635,11692,11755,11809,11857,11909,11964,12028,12096,12137,12204,12259,12366,12416,12462,12509,12570,12638,12712,12774,12850,12901,12979,13044,13088,13365,13411,13517,13698,13765,13825,13869,13922,13967,14012,14071,14125,14243,14309,14362,14419,14476,14526,14565,14617,14668,14714,14762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b923a94a0a3fff51545ad4d215c7ae9a\\transformed\\material-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,911,973,1050,1109,1168,1246,1307,1364,1420,1479,1537,1591,1676,1732,1790,1844,1909,2001,2075,2147,2226,2300,2376,2498,2560,2622,2721,2800,2874,2924,2975,3041,3105,3174,3245,3316,3377,3448,3515,3575,3661,3740,3807,3890,3975,4049,4114,4190,4238,4311,4375,4451,4529,4591,4655,4718,4783,4863,4939,5017,5093,5147,5202,5271,5346,5419", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "242,306,368,438,508,585,676,782,855,906,968,1045,1104,1163,1241,1302,1359,1415,1474,1532,1586,1671,1727,1785,1839,1904,1996,2070,2142,2221,2295,2371,2493,2555,2617,2716,2795,2869,2919,2970,3036,3100,3169,3240,3311,3372,3443,3510,3570,3656,3735,3802,3885,3970,4044,4109,4185,4233,4306,4370,4446,4524,4586,4650,4713,4778,4858,4934,5012,5088,5142,5197,5266,5341,5414,5484"}, "to": {"startLines": "2,37,38,39,40,41,50,51,54,69,71,79,84,97,103,104,105,106,107,108,109,110,111,112,113,114,115,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,179,194,195,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3079,3143,3205,3275,3345,4124,4215,4408,5253,5370,5930,6233,6948,7222,7300,7361,7418,7474,7533,7591,7645,7730,7786,7844,7898,7963,8385,8459,8531,8610,8684,8760,8882,8944,9006,9105,9184,9258,9308,9359,9425,9489,9558,9629,9700,9761,9832,9899,9959,10045,10124,10191,10274,10359,10433,10498,10574,10622,10695,10759,10835,10913,10975,11039,11102,11167,11247,11323,11401,11477,11531,12264,13172,13247,13416", "endLines": "5,37,38,39,40,41,50,51,54,69,71,79,84,97,103,104,105,106,107,108,109,110,111,112,113,114,115,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,179,194,195,198", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "292,3138,3200,3270,3340,3417,4210,4316,4476,5299,5427,6002,6287,7002,7295,7356,7413,7469,7528,7586,7640,7725,7781,7839,7893,7958,8050,8454,8526,8605,8679,8755,8877,8939,9001,9100,9179,9253,9303,9354,9420,9484,9553,9624,9695,9756,9827,9894,9954,10040,10119,10186,10269,10354,10428,10493,10569,10617,10690,10754,10830,10908,10970,11034,11097,11162,11242,11318,11396,11472,11526,11581,12328,13242,13315,13481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7f30831b919b2528567b209346c061ff\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,13093", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,13167"}}]}]}
// Generated by view binder compiler. Do not edit!
package org.levimc.launcher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.levimc.launcher.R;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView abiLabel;

  @NonNull
  public final LinearLayout aboutCard;

  @NonNull
  public final ImageButton addModButton;

  @NonNull
  public final ImageButton deleteVersionButton;

  @NonNull
  public final ImageView githubIcon;

  @NonNull
  public final LinearLayout header;

  @NonNull
  public final Button importApkButton;

  @NonNull
  public final ImageButton languageButton;

  @NonNull
  public final Button launchButton;

  @NonNull
  public final LinearLayout mainCard;

  @NonNull
  public final LinearLayout modCard;

  @NonNull
  public final RecyclerView modsRecycler;

  @NonNull
  public final TextView modsTitleText;

  @NonNull
  public final ProgressBar progressLoader;

  @NonNull
  public final LinearLayout rootLayout;

  @NonNull
  public final ImageButton selectVersionButton;

  @NonNull
  public final ImageButton settingsButton;

  @NonNull
  public final TextView textMinecraftVersion;

  private ActivityMainBinding(@NonNull LinearLayout rootView, @NonNull TextView abiLabel,
      @NonNull LinearLayout aboutCard, @NonNull ImageButton addModButton,
      @NonNull ImageButton deleteVersionButton, @NonNull ImageView githubIcon,
      @NonNull LinearLayout header, @NonNull Button importApkButton,
      @NonNull ImageButton languageButton, @NonNull Button launchButton,
      @NonNull LinearLayout mainCard, @NonNull LinearLayout modCard,
      @NonNull RecyclerView modsRecycler, @NonNull TextView modsTitleText,
      @NonNull ProgressBar progressLoader, @NonNull LinearLayout rootLayout,
      @NonNull ImageButton selectVersionButton, @NonNull ImageButton settingsButton,
      @NonNull TextView textMinecraftVersion) {
    this.rootView = rootView;
    this.abiLabel = abiLabel;
    this.aboutCard = aboutCard;
    this.addModButton = addModButton;
    this.deleteVersionButton = deleteVersionButton;
    this.githubIcon = githubIcon;
    this.header = header;
    this.importApkButton = importApkButton;
    this.languageButton = languageButton;
    this.launchButton = launchButton;
    this.mainCard = mainCard;
    this.modCard = modCard;
    this.modsRecycler = modsRecycler;
    this.modsTitleText = modsTitleText;
    this.progressLoader = progressLoader;
    this.rootLayout = rootLayout;
    this.selectVersionButton = selectVersionButton;
    this.settingsButton = settingsButton;
    this.textMinecraftVersion = textMinecraftVersion;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.abi_label;
      TextView abiLabel = ViewBindings.findChildViewById(rootView, id);
      if (abiLabel == null) {
        break missingId;
      }

      id = R.id.about_card;
      LinearLayout aboutCard = ViewBindings.findChildViewById(rootView, id);
      if (aboutCard == null) {
        break missingId;
      }

      id = R.id.add_mod_button;
      ImageButton addModButton = ViewBindings.findChildViewById(rootView, id);
      if (addModButton == null) {
        break missingId;
      }

      id = R.id.delete_version_button;
      ImageButton deleteVersionButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteVersionButton == null) {
        break missingId;
      }

      id = R.id.github_icon;
      ImageView githubIcon = ViewBindings.findChildViewById(rootView, id);
      if (githubIcon == null) {
        break missingId;
      }

      id = R.id.header;
      LinearLayout header = ViewBindings.findChildViewById(rootView, id);
      if (header == null) {
        break missingId;
      }

      id = R.id.import_apk_button;
      Button importApkButton = ViewBindings.findChildViewById(rootView, id);
      if (importApkButton == null) {
        break missingId;
      }

      id = R.id.language_button;
      ImageButton languageButton = ViewBindings.findChildViewById(rootView, id);
      if (languageButton == null) {
        break missingId;
      }

      id = R.id.launch_button;
      Button launchButton = ViewBindings.findChildViewById(rootView, id);
      if (launchButton == null) {
        break missingId;
      }

      id = R.id.main_card;
      LinearLayout mainCard = ViewBindings.findChildViewById(rootView, id);
      if (mainCard == null) {
        break missingId;
      }

      id = R.id.mod_card;
      LinearLayout modCard = ViewBindings.findChildViewById(rootView, id);
      if (modCard == null) {
        break missingId;
      }

      id = R.id.mods_recycler;
      RecyclerView modsRecycler = ViewBindings.findChildViewById(rootView, id);
      if (modsRecycler == null) {
        break missingId;
      }

      id = R.id.mods_title_text;
      TextView modsTitleText = ViewBindings.findChildViewById(rootView, id);
      if (modsTitleText == null) {
        break missingId;
      }

      id = R.id.progress_loader;
      ProgressBar progressLoader = ViewBindings.findChildViewById(rootView, id);
      if (progressLoader == null) {
        break missingId;
      }

      LinearLayout rootLayout = (LinearLayout) rootView;

      id = R.id.select_version_button;
      ImageButton selectVersionButton = ViewBindings.findChildViewById(rootView, id);
      if (selectVersionButton == null) {
        break missingId;
      }

      id = R.id.settings_button;
      ImageButton settingsButton = ViewBindings.findChildViewById(rootView, id);
      if (settingsButton == null) {
        break missingId;
      }

      id = R.id.text_minecraft_version;
      TextView textMinecraftVersion = ViewBindings.findChildViewById(rootView, id);
      if (textMinecraftVersion == null) {
        break missingId;
      }

      return new ActivityMainBinding((LinearLayout) rootView, abiLabel, aboutCard, addModButton,
          deleteVersionButton, githubIcon, header, importApkButton, languageButton, launchButton,
          mainCard, modCard, modsRecycler, modsTitleText, progressLoader, rootLayout,
          selectVersionButton, settingsButton, textMinecraftVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

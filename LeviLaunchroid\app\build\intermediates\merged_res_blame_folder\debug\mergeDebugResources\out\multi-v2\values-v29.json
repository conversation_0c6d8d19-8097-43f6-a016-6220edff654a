{"logs": [{"outputFile": "org.levimc.launcher.app-mergeDebugResources-30:/values-v29/values-v29.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5588f16723e7a1ef0290c5ed47d7e081\\transformed\\core-splashscreen-1.0.1\\res\\values-v29\\values-v29.xml", "from": {"startLines": "2,7", "startColumns": "4,4", "startOffsets": "55,374", "endLines": "6,8", "endColumns": "12,12", "endOffsets": "369,464"}, "to": {"startLines": "33,38", "startColumns": "4,4", "startOffsets": "1628,1947", "endLines": "37,39", "endColumns": "12,12", "endOffsets": "1942,2037"}}, {"source": "D:\\Levi\\LeviLaunchroid\\app\\src\\main\\res\\values-v29\\theme.xml", "from": {"startLines": "32,28,2", "startColumns": "4,4,4", "startOffsets": "1527,1385,57", "endLines": "34,30,26", "endColumns": "12,12,12", "endOffsets": "1659,1519,1377"}, "to": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,190,327", "endLines": "4,7,32", "endColumns": "12,12,12", "endOffsets": "185,322,1623"}}]}]}
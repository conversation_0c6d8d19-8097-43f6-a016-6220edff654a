# LeviLauncher

This is a launcher designed for Minecraft Bedrock Edition on Android, aimed at providing a better user experience and mod loading. The current version is in development, and we hope to implement more features in the future.

## Project Introduction

LeviLauncher is an open-source launcher that allows players to conveniently load mods and offers a rich API for enhanced functionality.

## Features

- Intuitive user interface
- Multiple game version management
- Mod support

## Development Goals

In the upcoming development, our goals include, but are not limited to:

- [ ] **Version Management**: Implement functionality for switching and managing multiple versions, allowing users to choose and update game versions freely.
- [ ] **Mod Support**: Add support for mods, enabling players to easily install and manage different mods.
- [ ] **Development Support**: Encapsulate necessary MC APIs and events for developers to use.
- [ ] **Better UI**: Beautify and improve the UI design.

## How to Contribute

We welcome anyone to participate in the development of LeviLauncher! You can contribute in the following ways:

- Submit issues and suggestions
- Submit code contributions

## Contact Us

If you have any questions or suggestions regarding the project, please feel free to contact us through the LeviMC official Discord.

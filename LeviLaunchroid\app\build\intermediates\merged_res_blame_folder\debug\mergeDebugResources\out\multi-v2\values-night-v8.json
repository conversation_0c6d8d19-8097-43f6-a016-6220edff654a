{"logs": [{"outputFile": "org.levimc.launcher.app-mergeDebugResources-30:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7f30831b919b2528567b209346c061ff\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "16,17,18,19,20,21,22,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "713,783,867,951,1047,1149,1251,4233", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "778,862,946,1042,1144,1246,1340,4317"}}, {"source": "D:\\Levi\\LeviLaunchroid\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "14,11,16,12,3,6,17,9,19,2,5,15,8", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "566,439,687,499,104,236,757,369,826,45,172,628,307", "endColumns": "44,39,47,42,44,46,44,45,41,41,43,41,42", "endOffsets": "606,474,730,537,144,278,797,410,863,82,211,665,345"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,100,140,188,231,276,323,368,414,456,498,542,584", "endColumns": "44,39,47,42,44,46,44,45,41,41,43,41,42", "endOffsets": "95,135,183,226,271,318,363,409,451,493,537,579,622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5588f16723e7a1ef0290c5ed47d7e081\\transformed\\core-splashscreen-1.0.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "85", "endOffsets": "136"}, "to": {"startLines": "15", "startColumns": "4", "startOffsets": "627", "endColumns": "85", "endOffsets": "708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b923a94a0a3fff51545ad4d215c7ae9a\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1345,1420,1531,1620,1721,1828,1935,2034,2141,2244,2371,2459,2583,2685,2787,2903,3005,3119,3247,3363,3485,3621,3741,3875,3995,4107,4322,4439,4563,4693,4815,4953,5087,5203", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1415,1526,1615,1716,1823,1930,2029,2136,2239,2366,2454,2578,2680,2782,2898,3000,3114,3242,3358,3480,3616,3736,3870,3990,4102,4228,4434,4558,4688,4810,4948,5082,5198,5318"}}]}]}
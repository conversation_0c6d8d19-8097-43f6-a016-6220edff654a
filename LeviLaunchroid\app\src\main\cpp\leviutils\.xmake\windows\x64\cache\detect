{
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Wno-gnu-line-marker -Werror"] = false,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-MMD -MF"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--Bdynamic"] = true,
            ["-l"] = true,
            ["--disable-nxcompat"] = true,
            ["--disable-tsaware"] = true,
            ["--strip-all"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--no-demangle"] = true,
            ["--export-all-symbols"] = true,
            ["--fatal-warnings"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["-v"] = true,
            ["-o"] = true,
            ["--gc-sections"] = true,
            ["--verbose"] = true,
            ["-L"] = true,
            ["-dy"] = true,
            ["--whole-archive"] = true,
            ["-S"] = true,
            ["--no-gc-sections"] = true,
            ["--disable-dynamicbase"] = true,
            ["--Bstatic"] = true,
            ["--insert-timestamp"] = true,
            ["-s"] = true,
            ["-m"] = true,
            ["--strip-debug"] = true,
            ["--no-insert-timestamp"] = true,
            ["--large-address-aware"] = true,
            ["--help"] = true,
            ["--no-dynamicbase"] = true,
            ["--tsaware"] = true,
            ["--allow-multiple-definition"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--dynamicbase"] = true,
            ["--kill-at"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--high-entropy-va"] = true,
            ["--nxcompat"] = true,
            ["--exclude-all-symbols"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--no-whole-archive"] = true,
            ["--demangle"] = true,
            ["--appcontainer"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--no-fatal-warnings"] = true,
            ["--version"] = true,
            ["--shared"] = true,
            ["--disable-auto-import"] = true,
            ["-static"] = true,
            ["--no-seh"] = true,
            ["-dn"] = true,
            ["--enable-auto-import"] = true,
            ["--disable-no-seh"] = true
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-dsym-dir"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-ftime-trace"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fno-fixed-point"] = true,
            ["-mextern-sdata"] = true,
            ["-fcxx-exceptions"] = true,
            ["-module-dependency-dir"] = true,
            ["-fseh-exceptions"] = true,
            ["-fobjc-weak"] = true,
            ["-P"] = true,
            ["-gdwarf64"] = true,
            ["-mmsa"] = true,
            ["-include-pch"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-Wdeprecated"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fzvector"] = true,
            ["-fenable-matrix"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fcommon"] = true,
            ["-extract-api"] = true,
            ["-mno-save-restore"] = true,
            ["--config"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-ffixed-x4"] = true,
            ["-rewrite-objc"] = true,
            ["-ffixed-a0"] = true,
            ["-ffixed-x5"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-mno-execute-only"] = true,
            ["-verify-pch"] = true,
            ["-iquote"] = true,
            ["-ffixed-x6"] = true,
            ["-nogpulib"] = true,
            ["-fstack-protector-all"] = true,
            ["-mno-madd4"] = true,
            ["-mpacked-stack"] = true,
            ["-fcall-saved-x15"] = true,
            ["--analyzer-output"] = true,
            ["-msoft-float"] = true,
            ["-fsanitize-stats"] = true,
            ["-mtgsplit"] = true,
            ["-moutline"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-ibuiltininc"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fdebug-macro"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-mno-msa"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fno-addrsig"] = true,
            ["-mno-crc"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-miamcu"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-L"] = true,
            ["-fno-rtti"] = true,
            ["-emit-ast"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fshort-enums"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-ffixed-x19"] = true,
            ["-cl-opt-disable"] = true,
            ["-print-target-triple"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-mfp64"] = true,
            ["-mno-abicalls"] = true,
            ["-Qn"] = true,
            ["-ffixed-d4"] = true,
            ["-Qunused-arguments"] = true,
            ["-fsanitize-trap"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-working-directory"] = true,
            ["-Xopenmp-target"] = true,
            ["-fverbose-asm"] = true,
            ["-ffixed-x30"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fdata-sections"] = true,
            ["-mlong-double-80"] = true,
            ["-msvr4-struct-return"] = true,
            ["-shared-libsan"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-o"] = true,
            ["-fno-force-enable-int128"] = true,
            ["--cuda-host-only"] = true,
            ["-print-ivar-layout"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-mlong-double-128"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fms-extensions"] = true,
            ["-mwavefrontsize64"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-M"] = true,
            ["-ffixed-x16"] = true,
            ["-dependency-file"] = true,
            ["--help-hidden"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fmodules-search-all"] = true,
            ["-fno-integrated-as"] = true,
            ["-ffinite-loops"] = true,
            ["-serialize-diagnostics"] = true,
            ["-malign-double"] = true,
            ["-fexceptions"] = true,
            ["-relocatable-pch"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-mbackchain"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-idirafter"] = true,
            ["-fintegrated-as"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fcall-saved-x10"] = true,
            ["-mno-long-calls"] = true,
            ["-ffixed-r9"] = true,
            ["-fnew-infallible"] = true,
            ["-fno-standalone-debug"] = true,
            ["-finline-hint-functions"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fmemory-profile"] = true,
            ["-femit-all-decls"] = true,
            ["-ffixed-x25"] = true,
            ["-iwithprefix"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-ffixed-x21"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fstack-protector"] = true,
            ["-finline-functions"] = true,
            ["-fgpu-sanitize"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fcall-saved-x8"] = true,
            ["-dependency-dot"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fprofile-generate"] = true,
            ["-F"] = true,
            ["-ffixed-a2"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-ffixed-d0"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fmodules-decluse"] = true,
            ["-gline-tables-only"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fdigraphs"] = true,
            ["-maix-struct-return"] = true,
            ["-nobuiltininc"] = true,
            ["-print-runtime-dir"] = true,
            ["-ffixed-d2"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-mno-nvs"] = true,
            ["-Ttext"] = true,
            ["-I"] = true,
            ["-meabi"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fno-use-init-array"] = true,
            ["-mno-global-merge"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fmath-errno"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fopenmp"] = true,
            ["-ffixed-x29"] = true,
            ["-D"] = true,
            ["-ffixed-x7"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fno-discard-value-names"] = true,
            ["-mlocal-sdata"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-print-supported-cpus"] = true,
            ["--precompile"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-nohipwrapperinc"] = true,
            ["-nostdinc"] = true,
            ["-ffixed-x3"] = true,
            ["-ffixed-d5"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-arch"] = true,
            ["-mlvi-cfi"] = true,
            ["-mlong-double-64"] = true,
            ["-print-effective-triple"] = true,
            ["-fmerge-all-constants"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-rpath"] = true,
            ["-ffixed-d6"] = true,
            ["--verify-debug-info"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-static-libsan"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-ffixed-x20"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-debug-macro"] = true,
            ["-membedded-data"] = true,
            ["-ffixed-d7"] = true,
            ["-fno-lto"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-B"] = true,
            ["-fforce-enable-int128"] = true,
            ["-g"] = true,
            ["-fms-hotpatch"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-Qy"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-mno-mt"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-MD"] = true,
            ["-MM"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-C"] = true,
            ["-MP"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-mno-restrict-it"] = true,
            ["-mlvi-hardening"] = true,
            ["-MT"] = true,
            ["-pipe"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-mmadd4"] = true,
            ["-ffunction-sections"] = true,
            ["-dI"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-Xlinker"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-ffixed-x10"] = true,
            ["-ffixed-x18"] = true,
            ["-isysroot"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fsycl"] = true,
            ["-fstandalone-debug"] = true,
            ["-fkeep-static-consts"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-elide-type"] = true,
            ["-MQ"] = true,
            ["-fcall-saved-x11"] = true,
            ["-mrelax-all"] = true,
            ["-fapprox-func"] = true,
            ["-fno-global-isel"] = true,
            ["-funroll-loops"] = true,
            ["-fsave-optimization-record"] = true,
            ["--cuda-device-only"] = true,
            ["-mnvs"] = true,
            ["-ffixed-r19"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mrestrict-it"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-gembed-source"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fslp-vectorize"] = true,
            ["-mno-outline"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fno-plt"] = true,
            ["-fsized-deallocation"] = true,
            ["-fno-strict-return"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-imacros"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-ffixed-x31"] = true,
            ["-fno-jump-tables"] = true,
            ["-mfentry"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-gdwarf-3"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-freciprocal-math"] = true,
            ["-cl-no-stdinc"] = true,
            ["-help"] = true,
            ["-gline-directives-only"] = true,
            ["-ffixed-x15"] = true,
            ["-fno-show-column"] = true,
            ["-MF"] = true,
            ["-mstackrealign"] = true,
            ["-fasync-exceptions"] = true,
            ["-MV"] = true,
            ["-fno-trigraphs"] = true,
            ["-mnocrc"] = true,
            ["-print-resource-dir"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-ftrapv"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fno-builtin"] = true,
            ["-iwithsysroot"] = true,
            ["-mpackets"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-CC"] = true,
            ["-msave-restore"] = true,
            ["-mllvm"] = true,
            ["-ffixed-x28"] = true,
            ["-fno-xray-function-index"] = true,
            ["-pthread"] = true,
            ["--analyze"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-common"] = true,
            ["--cuda-compile-host-device"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-mno-nvj"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-pedantic"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-mno-movt"] = true,
            ["-fno-short-wchar"] = true,
            ["-fno-spell-checking"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fopenmp-extensions"] = true,
            ["-mno-embedded-data"] = true,
            ["-ffixed-x26"] = true,
            ["-mno-gpopt"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fapple-kext"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-isystem-after"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fopenmp-simd"] = true,
            ["-fno-signed-char"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fembed-bitcode"] = true,
            ["-fstack-size-section"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-mno-hvx"] = true,
            ["-faligned-allocation"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-freg-struct-return"] = true,
            ["-ivfsoverlay"] = true,
            ["-fobjc-arc"] = true,
            ["-fdeclspec"] = true,
            ["-fpch-codegen"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-E"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fsigned-char"] = true,
            ["-fcxx-modules"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-U"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-ffixed-a6"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-G"] = true,
            ["-ffast-math"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-moutline-atomics"] = true,
            ["-fno-unroll-loops"] = true,
            ["-ffixed-a5"] = true,
            ["-fwasm-exceptions"] = true,
            ["-mno-memops"] = true,
            ["-z"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fstrict-enums"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-ffixed-x12"] = true,
            ["--hip-link"] = true,
            ["-H"] = true,
            ["-fgpu-rdc"] = true,
            ["-Xpreprocessor"] = true,
            ["-ffixed-d3"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-mlong-calls"] = true,
            ["-Xassembler"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fno-exceptions"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-Xanalyzer"] = true,
            ["-mrelax"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-S"] = true,
            ["-fno-split-stack"] = true,
            ["-fobjc-exceptions"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-mcrc"] = true,
            ["-fprotect-parens"] = true,
            ["-gno-embed-source"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-emit-llvm"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-frwpi"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-mno-cumode"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-gcodeview-ghash"] = true,
            ["-ffixed-x1"] = true,
            ["-fgnu89-inline"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fno-sanitize-stats"] = true,
            ["--migrate"] = true,
            ["-fglobal-isel"] = true,
            ["-fblocks"] = true,
            ["-fapplication-extension"] = true,
            ["-fgnu-keywords"] = true,
            ["-fno-offload-lto"] = true,
            ["-MJ"] = true,
            ["-MMD"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-x"] = true,
            ["-fno-access-control"] = true,
            ["-mrtd"] = true,
            ["-fmodules"] = true,
            ["-mthread-model"] = true,
            ["-fmodules-ts"] = true,
            ["-iprefix"] = true,
            ["-ffixed-x22"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fno-show-source-location"] = true,
            ["-time"] = true,
            ["-fno-sycl"] = true,
            ["-mibt-seal"] = true,
            ["-femulated-tls"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-trigraphs"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-mno-relax"] = true,
            ["-mrecord-mcount"] = true,
            ["-mgeneral-regs-only"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-ffreestanding"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-mhvx-qfloat"] = true,
            ["-foffload-lto"] = true,
            ["-fignore-exceptions"] = true,
            ["-MG"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fno-new-infallible"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fno-temp-file"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fcs-profile-generate"] = true,
            ["-ffixed-x13"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-mstack-arg-probe"] = true,
            ["-gdwarf32"] = true,
            ["-fno-declspec"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fno-signed-zeros"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-b"] = true,
            ["-fcf-protection"] = true,
            ["-mhvx"] = true,
            ["-fgnu-runtime"] = true,
            ["-gdwarf"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-dM"] = true,
            ["-ffixed-x27"] = true,
            ["-ffixed-x23"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fuse-line-directives"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-mms-bitfields"] = true,
            ["-gdwarf-4"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-mmark-bti-property"] = true,
            ["-isystem"] = true,
            ["-mnvj"] = true,
            ["-mgpopt"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fcoverage-mapping"] = true,
            ["-flto"] = true,
            ["-munaligned-access"] = true,
            ["-fno-profile-generate"] = true,
            ["-fxray-link-deps"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fdebug-types-section"] = true,
            ["-fno-elide-constructors"] = true,
            ["-print-multiarch"] = true,
            ["-fsystem-module"] = true,
            ["-ffixed-a4"] = true,
            ["-c"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-finstrument-functions"] = true,
            ["-fwritable-strings"] = true,
            ["-mno-outline-atomics"] = true,
            ["-index-header-map"] = true,
            ["-mabicalls"] = true,
            ["-fxray-instrument"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fno-autolink"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["--version"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fshort-wchar"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fjump-tables"] = true,
            ["-ffixed-x11"] = true,
            ["-Xclang"] = true,
            ["-pg"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mno-packets"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-emit-module"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-ffixed-x24"] = true,
            ["-freroll-loops"] = true,
            ["-mfp32"] = true,
            ["-fvectorize"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-undef"] = true,
            ["-fcall-saved-x9"] = true,
            ["-mno-implicit-float"] = true,
            ["-fno-stack-protector"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mseses"] = true,
            ["--emit-static-lib"] = true,
            ["-fno-finite-loops"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-save-stats"] = true,
            ["-Tbss"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-mnop-mcount"] = true,
            ["-emit-merged-ifs"] = true,
            ["-mcumode"] = true,
            ["-gdwarf-5"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-ffixed-x14"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fno-rtti-data"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-cxx-isystem"] = true,
            ["-fpascal-strings"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-mno-code-object-v3"] = true,
            ["-mno-seses"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-mcode-object-v3"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fconvergent-functions"] = true,
            ["-ffixed-x9"] = true,
            ["-fborland-extensions"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fminimize-whitespace"] = true,
            ["-include"] = true,
            ["-module-file-info"] = true,
            ["-cl-mad-enable"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-ffixed-point"] = true,
            ["-fno-digraphs"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-mcmse"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-save-temps"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fansi-escape-codes"] = true,
            ["-nogpuinc"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-mmemops"] = true,
            ["-fstack-usage"] = true,
            ["-mno-tgsplit"] = true,
            ["-I-"] = true,
            ["-ffixed-d1"] = true,
            ["-v"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fsplit-stack"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-operator-names"] = true,
            ["-ffixed-a3"] = true,
            ["-mmt"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-mexecute-only"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-ffixed-x2"] = true,
            ["-faddrsig"] = true,
            ["-mno-extern-sdata"] = true,
            ["-ffixed-a1"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-dD"] = true,
            ["-static-openmp"] = true,
            ["-traditional-cpp"] = true,
            ["-gmodules"] = true,
            ["-Tdata"] = true,
            ["-fms-compatibility"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fropi"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-print-search-dirs"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-gcodeview"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-ffixed-x8"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-w"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-mglobal-merge"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-print-targets"] = true,
            ["-T"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-ffixed-x17"] = true
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            cross = "arm-linux-androideabi-",
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkver = "21",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            ndkver = 25
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_utils.binary.deplibs"] = {
        objdump = [[C:\msys64\usr\bin\objdump.exe]],
        ["llvm-objdump"] = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}
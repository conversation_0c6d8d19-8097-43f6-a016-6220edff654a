{
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-global-isel"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fstack-protector-all"] = true,
            ["-c"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-F"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mgpopt"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-mmemops"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fno-builtin"] = true,
            ["-fzvector"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-mfp32"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-iprefix"] = true,
            ["-fpcc-struct-return"] = true,
            ["-C"] = true,
            ["-save-stats"] = true,
            ["-mrestrict-it"] = true,
            ["-fxray-instrument"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-ffixed-r19"] = true,
            ["-maix-struct-return"] = true,
            ["-undef"] = true,
            ["-Qunused-arguments"] = true,
            ["-fstack-protector"] = true,
            ["-msoft-float"] = true,
            ["-dependency-dot"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fwritable-strings"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-new-infallible"] = true,
            ["-fno-jump-tables"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-dsym-dir"] = true,
            ["-fno-show-source-location"] = true,
            ["-ffixed-x18"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-Xanalyzer"] = true,
            ["-fstrict-enums"] = true,
            ["-relocatable-pch"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-ftrigraphs"] = true,
            ["-time"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-mno-hvx"] = true,
            ["-E"] = true,
            ["-mno-madd4"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fxray-link-deps"] = true,
            ["-fembed-bitcode"] = true,
            ["-fseh-exceptions"] = true,
            ["-mrtd"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-faligned-allocation"] = true,
            ["-fwasm-exceptions"] = true,
            ["-iquote"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-o"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fdebug-macro"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["--config"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-mcmse"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-ffixed-d7"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fnew-infallible"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-print-target-triple"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-ffixed-a4"] = true,
            ["-fpascal-strings"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fstack-clash-protection"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fno-short-wchar"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mmadd4"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fno-rtti"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mno-save-restore"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-rpath"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fms-hotpatch"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fno-declspec"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-L"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-membedded-data"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fpch-codegen"] = true,
            ["-fms-extensions"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fno-fixed-point"] = true,
            ["-ffixed-x26"] = true,
            ["-fborland-extensions"] = true,
            ["-mwavefrontsize64"] = true,
            ["-cl-opt-disable"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-Tbss"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fverbose-asm"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ivfsoverlay"] = true,
            ["-mnvj"] = true,
            ["-fno-pch-codegen"] = true,
            ["--emit-static-lib"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-mlong-double-80"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-ffixed-x13"] = true,
            ["-Xclang"] = true,
            ["-ffixed-x27"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-mmark-bti-property"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fapprox-func"] = true,
            ["-I-"] = true,
            ["-print-ivar-layout"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-mno-outline"] = true,
            ["-fno-addrsig"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-mno-crc"] = true,
            ["-ffixed-a3"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-nogpuinc"] = true,
            ["-mglobal-merge"] = true,
            ["-mlong-double-64"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-H"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fsanitize-stats"] = true,
            ["-fopenmp"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fno-plt"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fapple-kext"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fstack-size-section"] = true,
            ["-flto"] = true,
            ["-fprotect-parens"] = true,
            ["-isysroot"] = true,
            ["-nobuiltininc"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fmodules-ts"] = true,
            ["-ffixed-a5"] = true,
            ["-fprofile-generate"] = true,
            ["-module-dependency-dir"] = true,
            ["-g"] = true,
            ["-fconvergent-functions"] = true,
            ["-fropi"] = true,
            ["-ffixed-x17"] = true,
            ["-fno-trigraphs"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-idirafter"] = true,
            ["-CC"] = true,
            ["-finstrument-functions"] = true,
            ["-pthread"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fsplit-stack"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-print-supported-cpus"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fslp-vectorize"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-mno-memops"] = true,
            ["-fexceptions"] = true,
            ["-ffixed-d4"] = true,
            ["-iwithprefixbefore"] = true,
            ["-mbackchain"] = true,
            ["-mms-bitfields"] = true,
            ["-fdebug-types-section"] = true,
            ["-mnop-mcount"] = true,
            ["-ffixed-x31"] = true,
            ["-fasync-exceptions"] = true,
            ["-fshort-enums"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-ffixed-x29"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fno-finite-loops"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fcommon"] = true,
            ["-ffixed-d3"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fglobal-isel"] = true,
            ["-fobjc-arc"] = true,
            ["-ffixed-x16"] = true,
            ["-malign-double"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-MP"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-dD"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-ffinite-loops"] = true,
            ["-fintegrated-cc1"] = true,
            ["-MQ"] = true,
            ["-fno-rtti-data"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-iwithprefix"] = true,
            ["-I"] = true,
            ["-fcall-saved-x13"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-print-search-dirs"] = true,
            ["-Xassembler"] = true,
            ["-ffixed-a0"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-Wdeprecated"] = true,
            ["-moutline"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-S"] = true,
            ["-fno-profile-generate"] = true,
            ["-mno-tgsplit"] = true,
            ["-faddrsig"] = true,
            ["-imacros"] = true,
            ["-save-temps"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-ffunction-sections"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fmath-errno"] = true,
            ["-mlocal-sdata"] = true,
            ["-ffixed-x23"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-emit-ast"] = true,
            ["-fno-split-stack"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-MD"] = true,
            ["-mno-nvj"] = true,
            ["-MV"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-Qn"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fcxx-exceptions"] = true,
            ["-gno-embed-source"] = true,
            ["-fno-cxx-modules"] = true,
            ["-v"] = true,
            ["-iwithsysroot"] = true,
            ["-print-targets"] = true,
            ["-fno-autolink"] = true,
            ["-U"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fgpu-rdc"] = true,
            ["-ffast-math"] = true,
            ["-MG"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fno-use-init-array"] = true,
            ["-fmodules-search-all"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-cl-finite-math-only"] = true,
            ["-ffixed-x6"] = true,
            ["-ffixed-d0"] = true,
            ["-mpackets"] = true,
            ["-fno-elide-type"] = true,
            ["-freg-struct-return"] = true,
            ["-mstack-arg-probe"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-ftime-trace"] = true,
            ["-ffixed-x30"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-mno-cumode"] = true,
            ["-mrecord-mcount"] = true,
            ["-fsized-deallocation"] = true,
            ["-ibuiltininc"] = true,
            ["-fgnu-keywords"] = true,
            ["-meabi"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-ffixed-x4"] = true,
            ["-mno-msa"] = true,
            ["-B"] = true,
            ["-ffixed-x19"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-ffixed-x14"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-ffixed-x20"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fno-signed-char"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["--cuda-device-only"] = true,
            ["-mlvi-cfi"] = true,
            ["-MT"] = true,
            ["-mno-gpopt"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mskip-rax-setup"] = true,
            ["-b"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-femit-all-decls"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-arch"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-gdwarf-5"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-pedantic"] = true,
            ["-fsystem-module"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fgnu-runtime"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-mpacked-stack"] = true,
            ["-mno-embedded-data"] = true,
            ["-fno-lto"] = true,
            ["-ffixed-d1"] = true,
            ["-fobjc-weak"] = true,
            ["-ftrapv"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-dependency-file"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fcxx-modules"] = true,
            ["-ffixed-x3"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fno-common"] = true,
            ["-mtgsplit"] = true,
            ["-MF"] = true,
            ["-print-multiarch"] = true,
            ["-fno-operator-names"] = true,
            ["-fsanitize-trap"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fcall-saved-x11"] = true,
            ["-include-pch"] = true,
            ["--hip-link"] = true,
            ["-mnvs"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-gembed-source"] = true,
            ["-fgnu89-inline"] = true,
            ["--analyzer-output"] = true,
            ["-mmsa"] = true,
            ["-fsycl"] = true,
            ["-fdiscard-value-names"] = true,
            ["-nogpulib"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-ffixed-x1"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["--help-hidden"] = true,
            ["-mcumode"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-mnocrc"] = true,
            ["-ffixed-x7"] = true,
            ["-mno-local-sdata"] = true,
            ["-mlong-calls"] = true,
            ["-Ttext"] = true,
            ["-verify-pch"] = true,
            ["-fno-offload-lto"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-ffixed-x2"] = true,
            ["-mthread-model"] = true,
            ["-emit-interface-stubs"] = true,
            ["-mlvi-hardening"] = true,
            ["-dI"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fno-stack-protector"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fno-sycl"] = true,
            ["--gpu-bundle-output"] = true,
            ["--cuda-host-only"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-mseses"] = true,
            ["-shared-libsan"] = true,
            ["-moutline-atomics"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-mcrc"] = true,
            ["--analyze"] = true,
            ["-gdwarf64"] = true,
            ["-mfp64"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-frwpi"] = true,
            ["-ffixed-x12"] = true,
            ["-gdwarf-4"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fminimize-whitespace"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-gcodeview"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-ffixed-x8"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fno-strict-return"] = true,
            ["-gdwarf-3"] = true,
            ["-ffixed-x21"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fdeclspec"] = true,
            ["-fno-exceptions"] = true,
            ["-fmodules"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-print-runtime-dir"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-cl-mad-enable"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fmemory-profile"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-emit-llvm"] = true,
            ["-fmodules-decluse"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-mrelax-all"] = true,
            ["-fblocks"] = true,
            ["-mno-seses"] = true,
            ["-gline-tables-only"] = true,
            ["-mno-global-merge"] = true,
            ["-mextern-sdata"] = true,
            ["-ffixed-x10"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-isystem-after"] = true,
            ["-gdwarf"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-nostdinc"] = true,
            ["-fopenmp-simd"] = true,
            ["-fdigraphs"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-gline-directives-only"] = true,
            ["-fshort-wchar"] = true,
            ["-mno-execute-only"] = true,
            ["-mno-extern-sdata"] = true,
            ["-Tdata"] = true,
            ["-mno-nvs"] = true,
            ["-mabicalls"] = true,
            ["-fno-spell-checking"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fcoverage-mapping"] = true,
            ["-print-effective-triple"] = true,
            ["-fstack-usage"] = true,
            ["-msvr4-struct-return"] = true,
            ["-foffload-lto"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-G"] = true,
            ["-miamcu"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-gdwarf32"] = true,
            ["-mno-restrict-it"] = true,
            ["-mrelax"] = true,
            ["-fno-debug-macro"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-ffixed-a1"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-MMD"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fdata-sections"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-finline-hint-functions"] = true,
            ["-mno-relax"] = true,
            ["-fuse-line-directives"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fstandalone-debug"] = true,
            ["-working-directory"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-trigraphs"] = true,
            ["-fignore-exceptions"] = true,
            ["-mibt-seal"] = true,
            ["-fno-digraphs"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fcall-saved-x14"] = true,
            ["-mexecute-only"] = true,
            ["-traditional-cpp"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-ffixed-point"] = true,
            ["-mhvx"] = true,
            ["-freroll-loops"] = true,
            ["-pipe"] = true,
            ["-mno-packets"] = true,
            ["-fsigned-char"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fno-discard-value-names"] = true,
            ["-M"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-funroll-loops"] = true,
            ["-module-file-info"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fno-access-control"] = true,
            ["-fintegrated-as"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-cxx-isystem"] = true,
            ["-ffixed-a6"] = true,
            ["-mstackrealign"] = true,
            ["-fno-show-column"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fno-temp-file"] = true,
            ["-ffixed-x28"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-ffixed-d6"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-mcode-object-v3"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-ffixed-r9"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-finline-functions"] = true,
            ["-mno-mt"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fno-force-enable-int128"] = true,
            ["--migrate"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fpch-debuginfo"] = true,
            ["-msave-restore"] = true,
            ["-fms-compatibility"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-dM"] = true,
            ["-ffixed-x15"] = true,
            ["--precompile"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-rewrite-objc"] = true,
            ["-fvectorize"] = true,
            ["-Xopenmp-target"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-ffixed-d2"] = true,
            ["-fcf-protection"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fenable-matrix"] = true,
            ["-mhvx-qfloat"] = true,
            ["-nohipwrapperinc"] = true,
            ["-ffixed-x25"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-gmodules"] = true,
            ["-ffixed-x22"] = true,
            ["-D"] = true,
            ["-fapplication-extension"] = true,
            ["-mlong-double-128"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-ffixed-a2"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-z"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-mno-movt"] = true,
            ["-static-openmp"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-MM"] = true,
            ["-include"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-help"] = true,
            ["-index-header-map"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-mfentry"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-ffixed-x9"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fno-integrated-as"] = true,
            ["-Xlinker"] = true,
            ["-munaligned-access"] = true,
            ["-mno-implicit-float"] = true,
            ["-fjump-tables"] = true,
            ["-mmt"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-ffixed-x24"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-w"] = true,
            ["-ffreestanding"] = true,
            ["-ffixed-x5"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-femulated-tls"] = true,
            ["-Qy"] = true,
            ["--version"] = true,
            ["-freciprocal-math"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-emit-module"] = true,
            ["-Xpreprocessor"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fno-memory-profile"] = true,
            ["-mno-long-calls"] = true,
            ["-T"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-ffixed-d5"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-isystem"] = true,
            ["-ffixed-x11"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mllvm"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-extract-api"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-print-resource-dir"] = true,
            ["--verify-debug-info"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fno-profile-instr-use"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-P"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-x"] = true,
            ["-MJ"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-pg"] = true,
            ["-mno-abicalls"] = true,
            ["-static-libsan"] = true,
            ["-flegacy-pass-manager"] = true
        }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--export-all-symbols"] = true,
            ["--exclude-all-symbols"] = true,
            ["--disable-no-seh"] = true,
            ["--verbose"] = true,
            ["-S"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--nxcompat"] = true,
            ["--demangle"] = true,
            ["--help"] = true,
            ["-dy"] = true,
            ["--enable-auto-import"] = true,
            ["-L"] = true,
            ["--fatal-warnings"] = true,
            ["--no-seh"] = true,
            ["--no-whole-archive"] = true,
            ["--Bstatic"] = true,
            ["--insert-timestamp"] = true,
            ["--no-dynamicbase"] = true,
            ["--large-address-aware"] = true,
            ["-m"] = true,
            ["-o"] = true,
            ["--high-entropy-va"] = true,
            ["--version"] = true,
            ["--no-fatal-warnings"] = true,
            ["--disable-tsaware"] = true,
            ["--tsaware"] = true,
            ["--appcontainer"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--Bdynamic"] = true,
            ["--strip-debug"] = true,
            ["--no-gc-sections"] = true,
            ["--strip-all"] = true,
            ["--disable-dynamicbase"] = true,
            ["--kill-at"] = true,
            ["--disable-high-entropy-va"] = true,
            ["-l"] = true,
            ["-s"] = true,
            ["--allow-multiple-definition"] = true,
            ["-dn"] = true,
            ["--disable-nxcompat"] = true,
            ["--dynamicbase"] = true,
            ["-static"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--no-insert-timestamp"] = true,
            ["--gc-sections"] = true,
            ["--disable-auto-import"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--shared"] = true,
            ["-v"] = true,
            ["--no-demangle"] = true,
            ["--whole-archive"] = true,
            ["--no-allow-multiple-definition"] = true
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            cross = "arm-linux-androideabi-",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sdkver = "21",
            ndkver = 25,
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    }
}
{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--enable-stdcall-fixup"] = true,
            ["--no-seh"] = true,
            ["--version"] = true,
            ["--no-whole-archive"] = true,
            ["--demangle"] = true,
            ["-dy"] = true,
            ["--insert-timestamp"] = true,
            ["--disable-auto-import"] = true,
            ["--disable-dynamicbase"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-no-seh"] = true,
            ["--fatal-warnings"] = true,
            ["--kill-at"] = true,
            ["--no-dynamicbase"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--disable-nxcompat"] = true,
            ["-static"] = true,
            ["--exclude-all-symbols"] = true,
            ["--large-address-aware"] = true,
            ["-S"] = true,
            ["--no-fatal-warnings"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--gc-sections"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--strip-debug"] = true,
            ["-dn"] = true,
            ["--dynamicbase"] = true,
            ["--strip-all"] = true,
            ["-l"] = true,
            ["--disable-tsaware"] = true,
            ["--help"] = true,
            ["--appcontainer"] = true,
            ["-o"] = true,
            ["--verbose"] = true,
            ["-m"] = true,
            ["--tsaware"] = true,
            ["--nxcompat"] = true,
            ["--no-gc-sections"] = true,
            ["--shared"] = true,
            ["--whole-archive"] = true,
            ["-v"] = true,
            ["--enable-auto-import"] = true,
            ["-L"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--Bdynamic"] = true,
            ["--Bstatic"] = true,
            ["--no-demangle"] = true,
            ["--high-entropy-va"] = true,
            ["--allow-multiple-definition"] = true,
            ["-s"] = true,
            ["--export-all-symbols"] = true
        }
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            ndkver = 25,
            cross = "arm-linux-androideabi-",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sdkver = "21",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-hip-new-launch-api"] = true,
            ["-fno-use-init-array"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fms-hotpatch"] = true,
            ["-save-stats"] = true,
            ["-ffixed-x5"] = true,
            ["-MP"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-foffload-lto"] = true,
            ["-fsigned-char"] = true,
            ["-ffixed-a3"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-mno-global-merge"] = true,
            ["-mmadd4"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-mno-restrict-it"] = true,
            ["-w"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-gembed-source"] = true,
            ["-mpacked-stack"] = true,
            ["-fprofile-generate"] = true,
            ["-fdebug-macro"] = true,
            ["-Wdeprecated"] = true,
            ["-ffixed-x2"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-cl-mad-enable"] = true,
            ["-ffixed-x6"] = true,
            ["-Tbss"] = true,
            ["-ffixed-x23"] = true,
            ["-fno-trigraphs"] = true,
            ["-module-file-info"] = true,
            ["-v"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-isysroot"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fno-stack-protector"] = true,
            ["-fsanitize-trap"] = true,
            ["-finline-functions"] = true,
            ["-include"] = true,
            ["-fintegrated-cc1"] = true,
            ["-mmemops"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-builtin"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fstrict-enums"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-ffixed-r19"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-S"] = true,
            ["-finstrument-functions"] = true,
            ["-ftime-trace"] = true,
            ["-mmark-bti-property"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fzvector"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-mrtd"] = true,
            ["-time"] = true,
            ["-fno-unique-section-names"] = true,
            ["-isystem-after"] = true,
            ["-mqdsp6-compat"] = true,
            ["-ffixed-x14"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-ffixed-x8"] = true,
            ["-mllvm"] = true,
            ["-x"] = true,
            ["-ffixed-x20"] = true,
            ["-mibt-seal"] = true,
            ["-T"] = true,
            ["-ffixed-d5"] = true,
            ["-ffixed-x29"] = true,
            ["-mno-memops"] = true,
            ["-frwpi"] = true,
            ["-fno-autolink"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-mnocrc"] = true,
            ["-fno-exceptions"] = true,
            ["-gdwarf32"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-mno-madd4"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fshort-enums"] = true,
            ["-fms-extensions"] = true,
            ["-relocatable-pch"] = true,
            ["-ffixed-x13"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-plt"] = true,
            ["-iprefix"] = true,
            ["-fembed-bitcode"] = true,
            ["-fignore-exceptions"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mno-extern-sdata"] = true,
            ["-ffixed-point"] = true,
            ["-isystem"] = true,
            ["-fcxx-modules"] = true,
            ["-mpackets"] = true,
            ["-fdebug-types-section"] = true,
            ["-fno-rtti-data"] = true,
            ["-fno-finite-loops"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fno-xray-function-index"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-nostdinc"] = true,
            ["-munaligned-access"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-mlong-double-64"] = true,
            ["-ffixed-x11"] = true,
            ["-ffixed-a4"] = true,
            ["-freroll-loops"] = true,
            ["--precompile"] = true,
            ["-fexceptions"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fropi"] = true,
            ["-fsystem-module"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-moutline"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fasync-exceptions"] = true,
            ["-fno-temp-file"] = true,
            ["-fseh-exceptions"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-mmt"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-freciprocal-math"] = true,
            ["-mno-packets"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fxray-link-deps"] = true,
            ["-mrestrict-it"] = true,
            ["-fno-split-stack"] = true,
            ["-ffixed-d6"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-ffixed-x31"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fno-declspec"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-o"] = true,
            ["-mstackrealign"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mno-cumode"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-mno-hvx"] = true,
            ["-fno-elide-type"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-ffixed-x26"] = true,
            ["-ibuiltininc"] = true,
            ["-malign-double"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fblocks"] = true,
            ["-msave-restore"] = true,
            ["-mnvj"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-mno-embedded-data"] = true,
            ["-undef"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-ffixed-x15"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-faddrsig"] = true,
            ["-fsave-optimization-record"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fobjc-arc"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-ffixed-x12"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fno-offload-lto"] = true,
            ["-mlvi-cfi"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-emit-ast"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fwritable-strings"] = true,
            ["--analyze"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fborland-extensions"] = true,
            ["-nogpulib"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fjump-tables"] = true,
            ["-mno-gpopt"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fconvergent-functions"] = true,
            ["-mexecute-only"] = true,
            ["-ffixed-d1"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fpch-codegen"] = true,
            ["-flto"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-iquote"] = true,
            ["-fobjc-weak"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-mrecord-mcount"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fvectorize"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fmath-errno"] = true,
            ["-fno-short-wchar"] = true,
            ["-mnop-mcount"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-ffast-math"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-help"] = true,
            ["-working-directory"] = true,
            ["-Xanalyzer"] = true,
            ["-P"] = true,
            ["--migrate"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-Xopenmp-target"] = true,
            ["-fno-global-isel"] = true,
            ["-static-openmp"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fno-show-column"] = true,
            ["-MV"] = true,
            ["-nobuiltininc"] = true,
            ["-freg-struct-return"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-mcmse"] = true,
            ["-mno-crc"] = true,
            ["-E"] = true,
            ["-module-dependency-dir"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-mno-movt"] = true,
            ["-gdwarf-5"] = true,
            ["-mno-nvj"] = true,
            ["-maix-struct-return"] = true,
            ["-emit-module"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-mcrc"] = true,
            ["-meabi"] = true,
            ["-fsplit-stack"] = true,
            ["-fstack-protector-all"] = true,
            ["-mmsa"] = true,
            ["-mlong-double-128"] = true,
            ["-mno-neg-immediates"] = true,
            ["-Qunused-arguments"] = true,
            ["-c"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-mrelax-all"] = true,
            ["-D"] = true,
            ["-ivfsoverlay"] = true,
            ["--hip-link"] = true,
            ["-gline-tables-only"] = true,
            ["-nogpuinc"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-funroll-loops"] = true,
            ["-fcf-protection"] = true,
            ["-ffixed-x22"] = true,
            ["-membedded-data"] = true,
            ["-C"] = true,
            ["-fmodules"] = true,
            ["-fopenmp-simd"] = true,
            ["-fno-debug-macro"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fno-new-infallible"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-gcodeview"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-print-targets"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-ffixed-x27"] = true,
            ["-gmodules"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fgnu-keywords"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-mno-outline"] = true,
            ["-mms-bitfields"] = true,
            ["-mrelax"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-I-"] = true,
            ["-I"] = true,
            ["-fstack-clash-protection"] = true,
            ["-gdwarf64"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-mno-execute-only"] = true,
            ["-include-pch"] = true,
            ["-print-ivar-layout"] = true,
            ["-ffixed-d0"] = true,
            ["-Tdata"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fstack-size-section"] = true,
            ["-ffixed-d3"] = true,
            ["-fsized-deallocation"] = true,
            ["-iwithsysroot"] = true,
            ["-CC"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fms-compatibility"] = true,
            ["-L"] = true,
            ["-fnew-infallible"] = true,
            ["-ffixed-x19"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-gcodeview-ghash"] = true,
            ["-MJ"] = true,
            ["-fno-signed-char"] = true,
            ["--gpu-bundle-output"] = true,
            ["-MT"] = true,
            ["-dD"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-Xpreprocessor"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-ffixed-x9"] = true,
            ["-H"] = true,
            ["-mlong-calls"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-save-temps"] = true,
            ["-mbackchain"] = true,
            ["-pg"] = true,
            ["-extract-api"] = true,
            ["-fdigraphs"] = true,
            ["-g"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-index-header-map"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-pipe"] = true,
            ["-ffixed-x10"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fgpu-rdc"] = true,
            ["-static-libsan"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-MQ"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fno-spell-checking"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-mno-relax"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-mno-long-calls"] = true,
            ["-fdata-sections"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-gdwarf-3"] = true,
            ["-mno-abicalls"] = true,
            ["-print-target-triple"] = true,
            ["-fuse-line-directives"] = true,
            ["-G"] = true,
            ["--help-hidden"] = true,
            ["-fno-show-source-location"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fstandalone-debug"] = true,
            ["-mcode-object-v3"] = true,
            ["-MM"] = true,
            ["-mseses"] = true,
            ["-mno-seses"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-dsym-dir"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-dM"] = true,
            ["-fno-rtti"] = true,
            ["-msoft-float"] = true,
            ["-fstack-usage"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["--emit-static-lib"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-Xlinker"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["--verify-debug-info"] = true,
            ["-fxray-instrument"] = true,
            ["--analyzer-output"] = true,
            ["-moutline-atomics"] = true,
            ["-ffixed-x4"] = true,
            ["-rpath"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-print-effective-triple"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-mhvx"] = true,
            ["-fopenmp"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fcommon"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fverbose-asm"] = true,
            ["-fno-strict-return"] = true,
            ["-fkeep-static-consts"] = true,
            ["--config"] = true,
            ["-imacros"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-verify-pch"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-trigraphs"] = true,
            ["-ffixed-a5"] = true,
            ["-ffixed-x28"] = true,
            ["-fcs-profile-generate"] = true,
            ["-mno-nvs"] = true,
            ["-gdwarf-4"] = true,
            ["-dI"] = true,
            ["-shared-libsan"] = true,
            ["-ffixed-r9"] = true,
            ["-ffixed-x17"] = true,
            ["-fno-jump-tables"] = true,
            ["-Qy"] = true,
            ["-ffixed-x3"] = true,
            ["-fno-lto"] = true,
            ["-finline-hint-functions"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fno-operator-names"] = true,
            ["-cxx-isystem"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fmodules-ts"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-mcumode"] = true,
            ["-emit-llvm"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fno-digraphs"] = true,
            ["-miamcu"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fcall-saved-x12"] = true,
            ["-femit-all-decls"] = true,
            ["-ffixed-d7"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-gdwarf-2"] = true,
            ["-cl-opt-disable"] = true,
            ["-fno-pch-codegen"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-mlvi-hardening"] = true,
            ["-gline-directives-only"] = true,
            ["-MF"] = true,
            ["-fslp-vectorize"] = true,
            ["-fenable-matrix"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fsplit-lto-unit"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-print-runtime-dir"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-Xclang"] = true,
            ["-fno-profile-generate"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-dependency-file"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fintegrated-as"] = true,
            ["-ffunction-sections"] = true,
            ["-pedantic"] = true,
            ["-mextern-sdata"] = true,
            ["-fno-access-control"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-gno-embed-source"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fsanitize-stats"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-print-multiarch"] = true,
            ["-ffixed-a1"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-mgpopt"] = true,
            ["-fcall-saved-x13"] = true,
            ["-ffixed-x7"] = true,
            ["-gdwarf"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fno-fixed-point"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fgnu-runtime"] = true,
            ["-fno-elide-constructors"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-iwithprefix"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-serialize-diagnostics"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fpascal-strings"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fno-standalone-debug"] = true,
            ["-mno-msa"] = true,
            ["-fmemory-profile"] = true,
            ["-mno-local-sdata"] = true,
            ["-mtgsplit"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-Ttext"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-ffixed-a6"] = true,
            ["-ffreestanding"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-B"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fsycl"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fcall-saved-x11"] = true,
            ["--cuda-host-only"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-sycl"] = true,
            ["-ffixed-x18"] = true,
            ["--version"] = true,
            ["-faligned-allocation"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-emit-interface-stubs"] = true,
            ["-Qn"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fdeclspec"] = true,
            ["-ffixed-x30"] = true,
            ["-fno-addrsig"] = true,
            ["-arch"] = true,
            ["-b"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-mlong-double-80"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-print-supported-cpus"] = true,
            ["--cuda-device-only"] = true,
            ["-mno-save-restore"] = true,
            ["-ffixed-x21"] = true,
            ["-mfp32"] = true,
            ["-fmodules-search-all"] = true,
            ["-fglobal-isel"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fno-common"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fprotect-parens"] = true,
            ["-MD"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-ftrapv"] = true,
            ["-traditional-cpp"] = true,
            ["-mno-tgsplit"] = true,
            ["-mlocal-sdata"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-ffinite-loops"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fno-integrated-as"] = true,
            ["-dependency-dot"] = true,
            ["-z"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fapprox-func"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fcall-saved-x18"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-MMD"] = true,
            ["-mno-implicit-float"] = true,
            ["-ffixed-a0"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-mfp64"] = true,
            ["-ffixed-d4"] = true,
            ["-mfentry"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fcall-saved-x14"] = true,
            ["-ffixed-x24"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-ffixed-x25"] = true,
            ["-nohipwrapperinc"] = true,
            ["-mthread-model"] = true,
            ["-fstack-protector"] = true,
            ["-fshort-wchar"] = true,
            ["-fmodules-decluse"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-ffixed-d2"] = true,
            ["-ffixed-a2"] = true,
            ["-print-resource-dir"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-mabicalls"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mno-unaligned-access"] = true,
            ["-mno-mt"] = true,
            ["-pthread"] = true,
            ["-fgnu89-inline"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-mglobal-merge"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fcall-saved-x8"] = true,
            ["-M"] = true,
            ["-fapple-kext"] = true,
            ["-mnvs"] = true,
            ["-F"] = true,
            ["-mhvx-qfloat"] = true,
            ["-ffixed-x1"] = true,
            ["-MG"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-iwithprefixbefore"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-idirafter"] = true,
            ["-Xassembler"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-print-search-dirs"] = true,
            ["-femulated-tls"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fapplication-extension"] = true,
            ["-U"] = true
        }
    }
}
[{"merged": "org.levimc.launcher.app-debug-32:/mipmap-xhdpi_ic_launcher_adaptive_back.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xhdpi/ic_launcher_adaptive_back.png"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_dialog_install_progress.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/dialog_install_progress.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_add.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_add.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_item_version_big_group.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/item_version_big_group.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-xxhdpi_ic_launcher_adaptive_fore.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xxhdpi/ic_launcher_adaptive_fore.png"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_drag_handle.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_drag_handle.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_activity_splash.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/activity_splash.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_third_level_item.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_third_level_item.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_alert_dialog_custom.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/alert_dialog_custom.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-mdpi_ic_launcher_adaptive_fore.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-mdpi/ic_launcher_adaptive_fore.png"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_item_version_group_title.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/item_version_group_title.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_rounded_card.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_rounded_card.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_dialog_apk_version_confirm.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/dialog_apk_version_confirm.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-xhdpi_ic_launcher_adaptive_fore.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xhdpi/ic_launcher_adaptive_fore.png"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-mdpi_ic_launcher_adaptive_back.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-mdpi/ic_launcher_adaptive_back.png"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_item_settings_button.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/item_settings_button.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-xxhdpi_ic_launcher_adaptive_back.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xxhdpi/ic_launcher_adaptive_back.png"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_github.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_github.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-mdpi_ic_launcher.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-mdpi/ic_launcher.png"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_dialog_loading.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/dialog_loading.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-xxxhdpi_ic_launcher_adaptive_fore.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xxxhdpi/ic_launcher_adaptive_fore.png"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_settings.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_settings.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_activity_main.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/activity_main.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/menu_language_menu.xml.flat", "source": "org.levimc.launcher.app-main-34:/menu/language_menu.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-hdpi_ic_launcher_adaptive_fore.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-hdpi/ic_launcher_adaptive_fore.png"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_item_settings_spinner.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/item_settings_spinner.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_abi_x86.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_abi_x86.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/font_misans.ttf.flat", "source": "org.levimc.launcher.app-main-34:/font/misans.ttf"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_leaf_logo.png.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_leaf_logo.png"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-hdpi_ic_launcher.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-hdpi/ic_launcher.png"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_item_settings_edittext.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/item_settings_edittext.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_minecraft_cube.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_minecraft_cube.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_abi_armeabi_v7a.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_abi_armeabi_v7a.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_arrow_down.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_arrow_down.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_card_background.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/card_background.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_round_gradient.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_round_gradient.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_abi_default.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_abi_default.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_dialog_libs_repair.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/dialog_libs_repair.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_abi_x86_64.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_abi_x86_64.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_dialog_settings.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/dialog_settings.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_item_mod.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/item_mod.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_dialog_game_version_select.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/dialog_game_version_select.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_check.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_check.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "org.levimc.launcher.app-debug-32:/xml_network_security_config.xml.flat", "source": "org.levimc.launcher.app-main-34:/xml/network_security_config.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-hdpi_ic_launcher_adaptive_back.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-hdpi/ic_launcher_adaptive_back.png"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-xhdpi_ic_launcher.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_background_gradient.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/background_gradient.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_launch.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_launch.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_internet.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_internet.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/mipmap-xxxhdpi_ic_launcher_adaptive_back.png.flat", "source": "org.levimc.launcher.app-main-34:/mipmap-xxxhdpi/ic_launcher_adaptive_back.png"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_delete.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_delete.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/xml_file_provider_paths.xml.flat", "source": "org.levimc.launcher.app-main-34:/xml/file_provider_paths.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/anim_slide_in_top.xml.flat", "source": "org.levimc.launcher.app-main-34:/anim/slide_in_top.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_ic_game_icon.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/ic_game_icon.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_item_version.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/item_version.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/layout_item_settings_switch.xml.flat", "source": "org.levimc.launcher.app-main-34:/layout/item_settings_switch.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_item_rounded.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_item_rounded.xml"}, {"merged": "org.levimc.launcher.app-debug-32:/drawable_bg_abi_arm64_v8a.xml.flat", "source": "org.levimc.launcher.app-main-34:/drawable/bg_abi_arm64_v8a.xml"}]
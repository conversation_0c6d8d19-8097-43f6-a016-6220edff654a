<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Levi\LeviLaunchroid\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Levi\LeviLaunchroid\app\src\main\res"><file name="slide_in_top" path="D:\Levi\LeviLaunchroid\app\src\main\res\anim\slide_in_top.xml" qualifiers="" type="anim"/><file name="background_gradient" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\background_gradient.xml" qualifiers="" type="drawable"/><file name="bg_abi_arm64_v8a" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_abi_arm64_v8a.xml" qualifiers="" type="drawable"/><file name="bg_abi_armeabi_v7a" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_abi_armeabi_v7a.xml" qualifiers="" type="drawable"/><file name="bg_abi_default" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_abi_default.xml" qualifiers="" type="drawable"/><file name="bg_abi_x86" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_abi_x86.xml" qualifiers="" type="drawable"/><file name="bg_abi_x86_64" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_abi_x86_64.xml" qualifiers="" type="drawable"/><file name="bg_item_rounded" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_item_rounded.xml" qualifiers="" type="drawable"/><file name="bg_rounded_card" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_rounded_card.xml" qualifiers="" type="drawable"/><file name="bg_round_gradient" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_round_gradient.xml" qualifiers="" type="drawable"/><file name="bg_third_level_item" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\bg_third_level_item.xml" qualifiers="" type="drawable"/><file name="card_background" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_arrow_down" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_arrow_down.xml" qualifiers="" type="drawable"/><file name="ic_check" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_drag_handle" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_drag_handle.xml" qualifiers="" type="drawable"/><file name="ic_game_icon" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_game_icon.xml" qualifiers="" type="drawable"/><file name="ic_github" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_github.xml" qualifiers="" type="drawable"/><file name="ic_internet" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_internet.xml" qualifiers="" type="drawable"/><file name="ic_launch" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_launch.xml" qualifiers="" type="drawable"/><file name="ic_leaf_logo" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_leaf_logo.png" qualifiers="" type="drawable"/><file name="ic_minecraft_cube" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_minecraft_cube.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\Levi\LeviLaunchroid\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="misans" path="D:\Levi\LeviLaunchroid\app\src\main\res\font\misans.ttf" qualifiers="" type="font"/><file name="activity_main" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_splash" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="alert_dialog_custom" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\alert_dialog_custom.xml" qualifiers="" type="layout"/><file name="dialog_apk_version_confirm" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\dialog_apk_version_confirm.xml" qualifiers="" type="layout"/><file name="dialog_game_version_select" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\dialog_game_version_select.xml" qualifiers="" type="layout"/><file name="dialog_install_progress" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\dialog_install_progress.xml" qualifiers="" type="layout"/><file name="dialog_libs_repair" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\dialog_libs_repair.xml" qualifiers="" type="layout"/><file name="dialog_loading" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\dialog_loading.xml" qualifiers="" type="layout"/><file name="dialog_settings" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\dialog_settings.xml" qualifiers="" type="layout"/><file name="item_mod" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\item_mod.xml" qualifiers="" type="layout"/><file name="item_settings_button" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\item_settings_button.xml" qualifiers="" type="layout"/><file name="item_settings_edittext" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\item_settings_edittext.xml" qualifiers="" type="layout"/><file name="item_settings_spinner" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\item_settings_spinner.xml" qualifiers="" type="layout"/><file name="item_settings_switch" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\item_settings_switch.xml" qualifiers="" type="layout"/><file name="item_version" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\item_version.xml" qualifiers="" type="layout"/><file name="item_version_big_group" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\item_version_big_group.xml" qualifiers="" type="layout"/><file name="item_version_group_title" path="D:\Levi\LeviLaunchroid\app\src\main\res\layout\item_version_group_title.xml" qualifiers="" type="layout"/><file name="language_menu" path="D:\Levi\LeviLaunchroid\app\src\main\res\menu\language_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_back" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-hdpi\ic_launcher_adaptive_back.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_fore" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-hdpi\ic_launcher_adaptive_fore.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_back" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-mdpi\ic_launcher_adaptive_back.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_fore" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-mdpi\ic_launcher_adaptive_fore.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_back" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xhdpi\ic_launcher_adaptive_back.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_fore" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xhdpi\ic_launcher_adaptive_fore.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_back" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xxhdpi\ic_launcher_adaptive_back.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_fore" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xxhdpi\ic_launcher_adaptive_fore.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_back" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xxxhdpi\ic_launcher_adaptive_back.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_adaptive_fore" path="D:\Levi\LeviLaunchroid\app\src\main\res\mipmap-xxxhdpi\ic_launcher_adaptive_fore.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary">#C8E6C9</color><color name="on_primary">#1B5E20</color><color name="secondary">#A5D6A7</color><color name="on_secondary">#1B5E20</color><color name="tertiary">#E8F5E9</color><color name="on_tertiary">#1B5E20</color><color name="error">#F44336</color><color name="on_error">#FFFFFF</color><color name="background">#F8FFF0</color><color name="surface">#F1F8E9</color><color name="on_background">#212121</color><color name="on_surface">#212121</color><color name="outline">#BDBDBD</color></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">LeviLauncher</string><string name="minecraft">Minecraft</string><string name="version">Version</string><string name="launch">Launch</string><string name="mods_title">🧩 Mods (%d)</string><string name="no_mods_found">No Mods Found</string><string name="mod_load_order">Load order: %d</string><string name="drag_to_reorder">Drag to reorder</string><string name="mod_reordered">Mod order updated</string><string name="about_title">☕ About</string><string name="copyright">©2024–2025 LeviMC. All rights reserved.</string><string name="font_license">This app uses the Misans font.</string><string name="resourcepack_detected_title">Resource Pack Detected</string><string name="resourcepack_detected_message">Do you want to launch the game now?\n\nResource Pack: %s</string><string name="launch_now">Launch Now</string><string name="launch_later">Launch Later</string><string name="import_confirmation_title">Import Mods</string><string name="import_confirmation_message">Are you sure you want to import the selected %d mod files?</string><string name="overwrite_file_title">File Already Exists</string><string name="overwrite_file_message">The file %s already exists. Do you want to overwrite it?</string><string name="overwrite">Overwrite</string><string name="skip">Skip</string><string name="confirm">Confirm</string><string name="installed_packages">Installed Packages</string><string name="local_custom">Local Custom</string><string name="storage_permission_title">Storage Permission Required</string><string name="storage_permission_message">Please grant storage permission to access mods directory and detect mods.</string><string name="grant_permission">Grant</string><string name="cancel">Cancel</string><string name="files_processed">Successfully processed %d mod files</string><string name="files_processing_error">File processing failed: %s</string><string name="user_cancelled">Import cancelled by user</string><string name="error">Error</string><string name="no_install_minecraft">Please install Minecraft first</string><string name="no_minecraft">Exit</string><string name="exit">Exit</string><string name="overlay_permission_message">Please grant the overlay permission</string><string name="overlay_permission_not_granted">Overlay permission not granted</string><string name="storage_permission_not_granted">Storage permission not granted</string><string name="import_apk">Import APK</string><string name="install">Install</string><string name="version_name">Version name</string><string name="name_invalid">This name cannot be used or is already taken!</string><string name="install_done">Install finished, version: %1$s</string><string name="illegal_apk_title">Illegal APK</string><string name="not_mc_apk">Not a Minecraft APK</string><string name="not_found_version">No version found</string><string name="installing_title">Installing…</string><string name="installing_message">Please wait, installing Minecraft</string><string name="enable_debug_log">Enable Debug Log</string><string name="settings_title">Settings</string><string name="settings_desc">Your launcher settings</string><string name="unknown_sources_permission_title">Install Permission</string><string name="unknown_sources_permission_message">To install or update this app, please grant permission to install unknown apps. Click "Grant" to open system settings, enable the permission for this app, and return to continue.</string><string name="check_update">Check for updates</string><string name="version_prefix">Version: </string><string name="new_version_found">New version found: %1$s</string><string name="update_question">Do you want to download and install the latest version?</string><string name="download_update">Download Update</string><string name="ignore_this_version">Ignore this version</string><string name="update_progress">Download progress: %1$d%%</string><string name="update_failed">Download failed: %1$s</string><string name="install_failed">Unable to install update: %1$s</string><string name="already_latest_version">Already latest version (%1$s)</string><string name="version_ignored">This version has been ignored</string><string name="downloading_update">Downloading update…</string><string name="repair_libs_in_progress">Repairing game library files…</string><string name="repair_completed">Repair Completed</string><string name="repair_libs_success_message">Game library files have been successfully repaired. You can now launch the game.</string><string name="repair_failed">Repair Failed</string><string name="repair_libs_failed_message">Failed to repair game library files. Please check file integrity.</string><string name="repair_error">Repair Error</string><string name="repair_libs_error_message">An error occurred during repair: %s</string><string name="missing_libs_title">Missing game library files - %s</string><string name="missing_libs_message">Required game library files are missing. Do you want to repair them now?</string><string name="repair">Repair</string><string name="repair_libs_dialog_title">Repairing Game Libraries</string><string name="requires_repair">[Needs Repair]</string><string name="theme_title">Theme Mode</string><string name="theme_follow_system">Follow System</string><string name="theme_light">Force Day Mode</string><string name="theme_dark">Force Night Mode</string><string name="error_no_browser">No browser available</string><string name="eula_title">LeviLauncher License Agreement</string><string name="eula_message"><b>Welcome to LeviLauncher</b>\n\nBy using this software you agree to:\n\n• This is an <b>unofficial</b> Minecraft launcher\n• You must own a <b>licensed</b> copy of Minecraft\n• Any form of piracy or cheating is prohibited\n• Mods/resource packs are used at your own risk\n• Not affiliated with Mojang/Microsoft\n\nContinued use constitutes agreement</string><string name="eula_agree">Accept</string><string name="eula_exit">Decline</string><string name="allow_unknown_sources">Please allow installation from unknown sources before proceeding</string><string name="dialog_title_delete_version">Delete Version</string><string name="dialog_message_delete_version">Are you sure to delete this custom version? This action cannot be undone.</string><string name="dialog_positive_delete">Delete</string><string name="dialog_negative_cancel">Cancel</string><string name="toast_delete_success">Deleted successfully</string><string name="toast_delete_failed">Delete failed: %1$s</string><string name="error_delete_builtin_version">Cannot delete built-in version</string><string name="invalid_mod_file">You selected an invalid mod file!</string><string name="error_versions">Error Versions</string><string name="dialog_title_delete_mod">Delete Mod</string><string name="dialog_message_delete_mod">Are you sure you want to delete this mod?</string><string name="version_isolation">Version Isolation</string><string name="language">Language</string><string name="english">English</string><string name="chinese">简体中文</string><string name="russian">Русский</string></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppFullScreenTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values\theme.xml" qualifiers=""><style name="Base.Theme.FullScreen" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorTertiary">@color/tertiary</item>
        <item name="colorOnTertiary">@color/on_tertiary</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOutline">@color/outline</item>

        <item name="android:windowBackground">@color/background</item>

        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>

        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>

    </style></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="primary">#424242</color><color name="on_primary">#FFFFFF</color><color name="secondary">#616161</color><color name="on_secondary">#FFFFFF</color><color name="tertiary">#757575</color><color name="on_tertiary">#FFFFFF</color><color name="error">#CF6679</color><color name="on_error">#000000</color><color name="background">#303030</color><color name="surface">#353535</color><color name="on_background">#E0E0E0</color><color name="on_surface">#E0E0E0</color><color name="outline">#BDBDBD</color></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values-ru\strings.xml" qualifiers="ru"><string name="app_name">LeviLauncher</string><string name="minecraft">Minecraft</string><string name="version">Версия</string><string name="launch">Запустить</string><string name="mods_title">🧩 Моды (%d)</string><string name="no_mods_found">Моды не найдены</string><string name="mod_load_order">Порядок загрузки: %d</string><string name="drag_to_reorder">Перетащите для изменения порядка</string><string name="mod_reordered">Порядок модов обновлён</string><string name="about_title">☕ О приложении</string><string name="copyright">©2024–2025 LeviMC. Все права защищены.</string><string name="font_license">Это приложение использует шрифт Misans.</string><string name="resourcepack_detected_title">Обнаружен набор ресурсов</string><string name="resourcepack_detected_message">Хотите запустить игру сейчас?\n\nНабор ресурсов: %s</string><string name="launch_now">Запустить сейчас</string><string name="launch_later">Запустить позже</string><string name="import_confirmation_title">Импортировать моды</string><string name="import_confirmation_message">Вы уверены в том что хотите импортировать выбранные %d файлы модов?</string><string name="overwrite_file_title">Файл Уже Существует</string><string name="overwrite_file_message">Файл %s уже существует. Вы хотите перезаписать его?</string><string name="overwrite">Перезаписать</string><string name="skip">Пропустить</string><string name="confirm">Подтвердить</string><string name="installed_packages">Установленные пакеты</string><string name="local_custom">Доступные версии</string><string name="storage_permission_title">Необходимо разрешение на доступ к хранилищу</string><string name="storage_permission_message">Пожалуйста выдайте доступ к хранилищу для доступа к папке модов и для определения модов.</string><string name="grant_permission">Выдать</string><string name="cancel">Отмена</string><string name="files_processed">Успешно добавлены файлы мода %d </string><string name="files_processing_error">Не удалось добавить файл: %s</string><string name="user_cancelled">Импорт отменён пользователем</string><string name="error">Ошибка</string><string name="no_install_minecraft">Сначала установите Minecraft</string><string name="no_minecraft">Выйти</string><string name="exit">Выйти</string><string name="overlay_permission_message">Необходимо разрешение на отображение поверх других приложений</string><string name="overlay_permission_not_granted">Разрешение на отображение поверх других приложений не выдано</string><string name="storage_permission_not_granted">Разрешение на доступ к хранилищу не выдано</string><string name="import_apk">Импортировать APK</string><string name="install">Установить</string><string name="version_name">Название версии</string><string name="name_invalid">Данное название нельзя использовать или оно уже занято!</string><string name="install_done">Установка завершена, версия: %1$s</string><string name="illegal_apk_title">Нелегальный APK</string><string name="not_mc_apk">Не APK Minecraft</string><string name="not_found_version">Версия не найдена</string><string name="installing_title">Установка…</string><string name="installing_message">Пожалуйста подождите, установка Minecraft</string><string name="enable_debug_log">Включить журнал отладки</string><string name="settings_title">Настройки</string><string name="settings_desc">Настройки вашего лаунчера</string><string name="unknown_sources_permission_title">Разрешение на установку</string><string name="unknown_sources_permission_message">Чтобы установить или обновить это приложение, пожалуйста выдайте разрешение на установку неизвестных приложений. Нажмите "Выдать" чтобы открыть настройки системы, выдайте разрешение для этого приложения, и вернитесь чтобы продолжить.</string><string name="check_update">Проверить обновления</string><string name="version_prefix">Версия: </string><string name="new_version_found">Найдена новая версия: %1$s</string><string name="update_question">Вы хотите скачать и установить последнюю версию?</string><string name="download_update">Скачать обновление</string><string name="ignore_this_version">Игнорировать эту версию</string><string name="update_progress">Прогресс скачивания: %1$d%%</string><string name="update_failed">Не удалось скачать: %1$s</string><string name="install_failed">Не удалось установить обновление: %1$s</string><string name="already_latest_version">Уже последняя версия (%1$s)</string><string name="version_ignored">Эта версия была проигнорирована</string><string name="downloading_update">Скачивание обновления…</string><string name="repair_libs_in_progress">Восстановление файлов библиотеки игры…</string><string name="repair_completed">Восстановление завершено</string><string name="repair_libs_success_message">Файлы библиотек игры успешно восстановлены. Вы можете запустить игру.</string><string name="repair_failed">Не удалось восстановить</string><string name="repair_libs_failed_message">Не удалось восстановить файлы библиотек игры. Пожалуйста проверьте целостность файлов.</string><string name="repair_error">Ошибка восстановления</string><string name="repair_libs_error_message">Произошла ошибка во время восстановления: %s</string><string name="missing_libs_title">Отсутствуют файлы библиотек игры - %s</string><string name="missing_libs_message">Необходимые файлы библиотек игры отсутствуют. Хотите восстановить их сейчас?</string><string name="repair">Восстановить</string><string name="repair_libs_dialog_title">Восстановление библиотек игры</string><string name="requires_repair">[Необходимо восстановление]</string><string name="theme_title">Тема</string><string name="theme_follow_system">Как в системе</string><string name="theme_light">Светлая тема</string><string name="theme_dark">Тёмная тема</string><string name="error_no_browser">Браузер не доступен</string><string name="eula_title">Лицензионное соглашение LeviLauncher</string><string name="eula_message"><b>Добро пожаловать в LeviLauncher</b>\n\nПри использовании данной программы вы соглашаетесь с тем что:\n\n• Это <b>неофициальный</b> лаунчер Minecraft\n• Вам необходимо владеть <b>лицензионной</b> копией Minecraft\n• Любая форма пиратства и читов запрещена\n• Моды/наборы ресурсов используются на ваш риск\n• Не связано с Mojang/Microsoft\n\nДальнейшее использование означает согласие с лицензионным соглашением</string><string name="eula_agree">Принять</string><string name="eula_exit">Отклонить</string><string name="allow_unknown_sources">Пожалуйста, разрешите установку из неизвестных источников и повторите попытку</string><string name="dialog_title_delete_version">Удалить версию</string><string name="dialog_message_delete_version">Вы уверены, что хотите удалить эту пользовательскую версию? Это действие не может быть отменено.</string><string name="dialog_positive_delete">Удалить</string><string name="dialog_negative_cancel">Отмена</string><string name="toast_delete_success">Успешно удалено</string><string name="toast_delete_failed">Ошибка удаления: %1$s</string><string name="error_delete_builtin_version">Нельзя удалить встроенную версию</string><string name="invalid_mod_file">Вы выбрали недопустимый мод-файл!</string><string name="error_versions">Ошибочные версии</string><string name="dialog_title_delete_mod">Удалить мод</string><string name="dialog_message_delete_mod">Вы уверены, что хотите удалить этот мод?</string><string name="version_isolation">Изоляция версий</string><string name="language">Язык</string><string name="english">English</string><string name="chinese">简体中文</string><string name="russian">Русский</string></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values-v27\styles.xml" qualifiers="v27"><style name="AppFullScreenTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values-v27\theme.xml" qualifiers="v27"><style name="Base.Theme.FullScreen" parent="Theme.Material3.DayNight.NoActionBar">
                <item name="android:windowFullscreen">true</item>
                <item name="colorPrimary">@color/primary</item>
                <item name="colorOnPrimary">@color/on_primary</item>
                <item name="colorSecondary">@color/secondary</item>
                <item name="colorOnSecondary">@color/on_secondary</item>
                <item name="colorSurface">@color/surface</item>
                <item name="colorOnSurface">@color/on_surface</item>
                <item name="colorTertiary">@color/tertiary</item>
                <item name="colorOnTertiary">@color/on_tertiary</item>
                <item name="android:colorBackground">@color/background</item>
                <item name="colorOnBackground">@color/on_background</item>
                <item name="colorOutline">@color/outline</item>

                <item name="android:windowBackground">@color/background</item>

                <item name="android:statusBarColor">@android:color/transparent</item>
                <item name="android:windowLightStatusBar">true</item>

                <item name="android:windowNoTitle">true</item>
                <item name="android:windowActionBar">false</item>
                <item name="android:windowContentOverlay">@null</item>
                <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        </style></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values-v29\theme.xml" qualifiers="v29"><style name="Base.Theme.FullScreen" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorTertiary">@color/tertiary</item>
        <item name="colorOnTertiary">@color/on_tertiary</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOutline">@color/outline</item>

        <item name="android:windowBackground">@color/background</item>

        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>

        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>

    </style><style name="AppTheme.Light" parent="Base.Theme.FullScreen">
        <item name="android:forceDarkAllowed">false</item>
    </style><style name="AppTheme.Dark" parent="Base.Theme.FullScreen">
        <item name="android:forceDarkAllowed">true</item>
    </style></file><file path="D:\Levi\LeviLaunchroid\app\src\main\res\values-zh-rCN\strings.xml" qualifiers="zh-rCN"><string name="app_name">LeviLauncher</string><string name="minecraft">Minecraft</string><string name="version">版本</string><string name="launch">启动</string><string name="mods_title">🧩 模组 (%d)</string><string name="no_mods_found">未找到模组</string><string name="mod_load_order">加载顺序：%d</string><string name="drag_to_reorder">拖动以重新排序</string><string name="mod_reordered">模组顺序已更新</string><string name="about_title">☕ 关于</string><string name="copyright">©2024–2025 LeviMC. 保留所有权利</string><string name="font_license">本软件使用 Misans 字体</string><string name="resourcepack_detected_title">检测到资源包导入</string><string name="resourcepack_detected_message">是否立即启动游戏？\n\n资源包：%s</string><string name="launch_now">立即启动</string><string name="launch_later">稍后启动</string><string name="import_confirmation_title">导入模组</string><string name="import_confirmation_message">确定要导入所选的 %d 个模组文件吗？</string><string name="overwrite_file_title">文件已存在</string><string name="overwrite_file_message">文件 %s 已存在，是否覆盖？</string><string name="overwrite">覆盖</string><string name="skip">跳过</string><string name="confirm">确认</string><string name="installed_packages">已安装包</string><string name="local_custom">本地自定义</string><string name="check_update">检查更新</string><string name="version_prefix">版本号：</string><string name="storage_permission_title">需要存储权限</string><string name="storage_permission_message">授予存储权限以便访问 mods 目录，检测模组。</string><string name="grant_permission">授予权限</string><string name="cancel">取消</string><string name="files_processed">成功处理 %d 个模组文件</string><string name="files_processing_error">文件处理失败：%s</string><string name="user_cancelled">用户取消导入</string><string name="error">错误</string><string name="no_install_minecraft">请先安装Minecraft</string><string name="no_minecraft">未安装Minecraft</string><string name="exit">退出</string><string name="overlay_permission_message">请授予悬浮窗权限</string><string name="overlay_permission_not_granted">未授予悬浮窗权限</string><string name="storage_permission_not_granted">未授予存储权限</string><string name="import_apk">导入APK</string><string name="install">安装</string><string name="version_name">版本信息</string><string name="name_invalid">不符合命名规则或已存在！</string><string name="install_done">安装完成，版本：%1$s</string><string name="illegal_apk_title">非法APK</string><string name="not_mc_apk">不是Minecraft APK</string><string name="not_found_version">未发现版本</string><string name="installing_title">正在安装中…</string><string name="installing_message">请稍候，正在安装MC中</string><string name="enable_debug_log">启用Debug Log</string><string name="settings_title">设置</string><string name="settings_desc">您的启动器设置</string><string name="unknown_sources_permission_title">安装权限</string><string name="unknown_sources_permission_message">请授予安装未知来源应用的权限，否则无法完成应用更新/安装。点击“授权”前往系统设置开启后返回本应用继续操作。</string><string name="new_version_found">发现新版本：%1$s</string><string name="update_question">是否下载并安装最新版本？</string><string name="download_update">下载更新</string><string name="ignore_this_version">忽略此版本</string><string name="update_progress">下载进度: %1$d%%</string><string name="update_failed">下载失败: %1$s</string><string name="install_failed">无法安装更新: %1$s</string><string name="already_latest_version">已是最新版本 (%1$s)</string><string name="version_ignored">此版本已被忽略</string><string name="downloading_update">正在下载更新…</string><string name="repair_libs_in_progress">修复游戏库文件中…</string><string name="repair_completed">修复完成</string><string name="repair_libs_success_message">游戏库文件已成功修复，现在可以启动游戏</string><string name="repair_failed">修复失败</string><string name="repair_libs_failed_message">无法修复游戏库文件，请检查文件完整性</string><string name="repair_error">修复出错</string><string name="repair_libs_error_message">修复过程中发生错误: %s</string><string name="missing_libs_title">缺少游戏库文件 - %s</string><string name="missing_libs_message">检测到缺少必要的游戏库文件，是否立即修复？</string><string name="repair">修复</string><string name="repair_libs_dialog_title">修复游戏库文件</string><string name="requires_repair">[需要修复]</string><string name="theme_title">主题模式</string><string name="theme_follow_system">跟随系统</string><string name="theme_light">强制白天</string><string name="theme_dark">强制夜间</string><string name="error_no_browser">无法打开浏览器</string><string name="eula_title">LeviLauncher 用户协议</string><string name="eula_message"><b>欢迎使用 LeviLauncher</b>\n\n使用本软件即表示您同意：\n\n• 此为<b>非官方</b> Minecraft 启动器\n• 您需拥有<b>合法授权</b>的 Minecraft 副本\n• 禁止任何形式的盗版或作弊行为\n• 模组/资源包使用风险自负\n• 与 Mojang/Microsoft 无关联\n\n继续使用视为接受条款</string><string name="eula_agree">接受</string><string name="eula_exit">拒绝</string><string name="allow_unknown_sources">请允许安装未知来源应用再操作</string><string name="dialog_title_delete_version">删除版本</string><string name="dialog_message_delete_version">确定要删除此自定义版本？删除后不可恢复。</string><string name="dialog_positive_delete">删除</string><string name="dialog_negative_cancel">取消</string><string name="toast_delete_success">删除成功</string><string name="toast_delete_failed">删除失败: %1$s</string><string name="error_delete_builtin_version">不能删除内置版本</string><string name="invalid_mod_file">你选择了非法的模组文件！</string><string name="error_versions">错误版本</string><string name="dialog_title_delete_mod">删除 Mod</string><string name="dialog_message_delete_mod">确定要删除该 Mod 吗？</string><string name="version_isolation">版本隔离</string><string name="language">语言</string><string name="english">English</string><string name="chinese">简体中文</string><string name="russian">Русский</string></file><file name="file_provider_paths" path="D:\Levi\LeviLaunchroid\app\src\main\res\xml\file_provider_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\Levi\LeviLaunchroid\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Levi\LeviLaunchroid\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Levi\LeviLaunchroid\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Levi\LeviLaunchroid\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Levi\LeviLaunchroid\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs"><file name="arm64-v8a/libleviutils.so" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\arm64-v8a\libleviutils.so"/><file name="armeabi-v7a/libleviutils.so" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\armeabi-v7a\libleviutils.so"/><file name="include/gui/gui.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\gui\gui.h"/><file name="include/gui/ImGuiAnsiColor.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\gui\ImGuiAnsiColor.h"/><file name="include/gui/renderer.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\gui\renderer.h"/><file name="include/imgui/imconfig.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\imgui\imconfig.h"/><file name="include/imgui/imgui.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\imgui\imgui.h"/><file name="include/imgui/imgui_impl_android.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\imgui\imgui_impl_android.h"/><file name="include/imgui/imgui_impl_opengl3.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\imgui\imgui_impl_opengl3.h"/><file name="include/imgui/imgui_internal.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\imgui\imgui_internal.h"/><file name="include/imgui/imstb_rectpack.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\imgui\imstb_rectpack.h"/><file name="include/imgui/imstb_textedit.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\imgui\imstb_textedit.h"/><file name="include/imgui/imstb_truetype.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\imgui\imstb_truetype.h"/><file name="include/logger/logger.h" path="D:\Levi\LeviLaunchroid\app\src\main\jniLibs\include\logger\logger.h"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Levi\LeviLaunchroid\app\src\debug\jniLibs"/></dataSet></merger>
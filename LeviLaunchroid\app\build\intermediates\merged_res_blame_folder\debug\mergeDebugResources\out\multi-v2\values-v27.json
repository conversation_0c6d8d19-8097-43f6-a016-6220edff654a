{"logs": [{"outputFile": "org.levimc.launcher.app-mergeDebugResources-30:/values-v27/values-v27.xml", "map": [{"source": "D:\\Levi\\LeviLaunchroid\\app\\src\\main\\res\\values-v27\\styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endLines": "8", "endColumns": "12", "endOffsets": "459"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "8", "endColumns": "12", "endOffsets": "451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5588f16723e7a1ef0290c5ed47d7e081\\transformed\\core-splashscreen-1.0.1\\res\\values-v27\\values-v27.xml", "from": {"startLines": "2,3,4,7", "startColumns": "4,4,4,4", "startOffsets": "55,136,229,405", "endLines": "2,3,6,9", "endColumns": "80,92,12,12", "endOffsets": "131,224,400,588"}, "to": {"startLines": "33,34,35,38", "startColumns": "4,4,4,4", "startOffsets": "1912,1993,2086,2262", "endLines": "33,34,37,40", "endColumns": "80,92,12,12", "endOffsets": "1988,2081,2257,2445"}}, {"source": "D:\\Levi\\LeviLaunchroid\\app\\src\\main\\res\\values-v27\\theme.xml", "from": {"startLines": "2", "startColumns": "8", "startOffsets": "61", "endLines": "25", "endColumns": "16", "endOffsets": "1535"}, "to": {"startLines": "9", "startColumns": "4", "startOffsets": "456", "endLines": "32", "endColumns": "16", "endOffsets": "1907"}}]}]}
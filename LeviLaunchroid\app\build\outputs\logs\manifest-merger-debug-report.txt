-- Merging decision tree log ---
manifest
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:2:1-181:12
INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:2:1-181:12
INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:2:1-181:12
INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:2:1-181:12
MERGED from [androidx.databinding:viewbinding:8.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3873cf8ed40107ac1681582d54ab865e\transformed\viewbinding-8.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5588f16723e7a1ef0290c5ed47d7e081\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [:preloader] D:\Levi\LeviLaunchroid\preloader\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b923a94a0a3fff51545ad4d215c7ae9a\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0dd3509e331ffda00d074076d997d500\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6f807c74bc70cdaadf29bde3bf17ab0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f30831b919b2528567b209346c061ff\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\943a6947ef2d3870cd3cc626c2aab500\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\704e1133649f20e7e15676281dca8f86\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed1dce5004e273e403e77a288f372a4d\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c14905e0dc69aa74dca6fa266b41b2d7\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8abeef8de17028a23ba7e44d6e68469\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6023fa968d9cbb97a14859890702213e\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b505fc6f3e4a86138b2312bffb5e2bc\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ada98945c479dc98c4dd300a65621340\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95e984f9190d779e5b0b8cdec20b712\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4c434148e786a531cbfc7cc27b1605\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be0e79da1da8d3c8414f0347e2160209\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e44ef053d82383c7572bc6afe418c30\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c43ccbd7e82bfb8a1f8e9707e0bf63dd\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6907dfccbea6f7f945c8f9178fc15a75\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\574de7ee7993b7547df42b877eaa6261\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25563a152cb587ee1986b18b2b6db67f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da933ae2f66e91488e7d692a0382419c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f2b981496059a86aaa1148761ab217e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91a78cc2f37fde4645351f9854dc8fed\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0919b2cd7668a3ffad2816f9978ac126\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9432c8a078d27bd5597654879a1a8e1\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00a5ac59d96fc924b1d8830028179058\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f91c8e7371b5ad6a53dd0dae64c61fa\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2bb6803121b39a18ac147ce494adbf5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c3abe3d116a9bfdfd8c765b16dd38b\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d7243d527fea089f520b739052dc3dc\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67cce916a62510377111125940e07555\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\062f2eb6737fdc6cdf631c7d033f457d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71d77b42874dc750e9c73aa3cf02735\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcabd2ebc060c091fb1c6e7a789f4754\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5e11f5d9b22162afca3dbfa96273733\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8d5a8ca029c9156dce21390c2fa9d8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79eeb00f220ad82842176587c499effe\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:2:11-69
	tools:ignore
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:4:5-32
uses-sdk
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:6:5-9:62
INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:6:5-9:62
INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:6:5-9:62
MERGED from [androidx.databinding:viewbinding:8.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3873cf8ed40107ac1681582d54ab865e\transformed\viewbinding-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3873cf8ed40107ac1681582d54ab865e\transformed\viewbinding-8.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5588f16723e7a1ef0290c5ed47d7e081\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5588f16723e7a1ef0290c5ed47d7e081\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [:preloader] D:\Levi\LeviLaunchroid\preloader\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:preloader] D:\Levi\LeviLaunchroid\preloader\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b923a94a0a3fff51545ad4d215c7ae9a\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b923a94a0a3fff51545ad4d215c7ae9a\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0dd3509e331ffda00d074076d997d500\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0dd3509e331ffda00d074076d997d500\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6f807c74bc70cdaadf29bde3bf17ab0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6f807c74bc70cdaadf29bde3bf17ab0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f30831b919b2528567b209346c061ff\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f30831b919b2528567b209346c061ff\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\943a6947ef2d3870cd3cc626c2aab500\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\943a6947ef2d3870cd3cc626c2aab500\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\704e1133649f20e7e15676281dca8f86\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\704e1133649f20e7e15676281dca8f86\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed1dce5004e273e403e77a288f372a4d\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed1dce5004e273e403e77a288f372a4d\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c14905e0dc69aa74dca6fa266b41b2d7\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c14905e0dc69aa74dca6fa266b41b2d7\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8abeef8de17028a23ba7e44d6e68469\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8abeef8de17028a23ba7e44d6e68469\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6023fa968d9cbb97a14859890702213e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6023fa968d9cbb97a14859890702213e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b505fc6f3e4a86138b2312bffb5e2bc\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b505fc6f3e4a86138b2312bffb5e2bc\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ada98945c479dc98c4dd300a65621340\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ada98945c479dc98c4dd300a65621340\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95e984f9190d779e5b0b8cdec20b712\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95e984f9190d779e5b0b8cdec20b712\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4c434148e786a531cbfc7cc27b1605\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4c434148e786a531cbfc7cc27b1605\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be0e79da1da8d3c8414f0347e2160209\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be0e79da1da8d3c8414f0347e2160209\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e44ef053d82383c7572bc6afe418c30\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e44ef053d82383c7572bc6afe418c30\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c43ccbd7e82bfb8a1f8e9707e0bf63dd\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c43ccbd7e82bfb8a1f8e9707e0bf63dd\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6907dfccbea6f7f945c8f9178fc15a75\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6907dfccbea6f7f945c8f9178fc15a75\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\574de7ee7993b7547df42b877eaa6261\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\574de7ee7993b7547df42b877eaa6261\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25563a152cb587ee1986b18b2b6db67f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25563a152cb587ee1986b18b2b6db67f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da933ae2f66e91488e7d692a0382419c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da933ae2f66e91488e7d692a0382419c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f2b981496059a86aaa1148761ab217e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f2b981496059a86aaa1148761ab217e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91a78cc2f37fde4645351f9854dc8fed\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91a78cc2f37fde4645351f9854dc8fed\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0919b2cd7668a3ffad2816f9978ac126\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0919b2cd7668a3ffad2816f9978ac126\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9432c8a078d27bd5597654879a1a8e1\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9432c8a078d27bd5597654879a1a8e1\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00a5ac59d96fc924b1d8830028179058\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00a5ac59d96fc924b1d8830028179058\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f91c8e7371b5ad6a53dd0dae64c61fa\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f91c8e7371b5ad6a53dd0dae64c61fa\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2bb6803121b39a18ac147ce494adbf5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2bb6803121b39a18ac147ce494adbf5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c3abe3d116a9bfdfd8c765b16dd38b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c3abe3d116a9bfdfd8c765b16dd38b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d7243d527fea089f520b739052dc3dc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d7243d527fea089f520b739052dc3dc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67cce916a62510377111125940e07555\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67cce916a62510377111125940e07555\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\062f2eb6737fdc6cdf631c7d033f457d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\062f2eb6737fdc6cdf631c7d033f457d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71d77b42874dc750e9c73aa3cf02735\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71d77b42874dc750e9c73aa3cf02735\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcabd2ebc060c091fb1c6e7a789f4754\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcabd2ebc060c091fb1c6e7a789f4754\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5e11f5d9b22162afca3dbfa96273733\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5e11f5d9b22162afca3dbfa96273733\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8d5a8ca029c9156dce21390c2fa9d8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8d5a8ca029c9156dce21390c2fa9d8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79eeb00f220ad82842176587c499effe\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79eeb00f220ad82842176587c499effe\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	tools:overrideLibrary
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:9:9-59
	android:targetSdkVersion
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:8:9-38
		INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:7:9-35
		INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml
uses-feature#0x20000
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:11:5-13:35
	android:glEsVersion
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:12:9-38
	android:required
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:13:9-32
uses-feature#android.hardware.touchscreen
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:14:5-16:36
	android:required
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:16:9-33
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:15:9-52
uses-permission#android.permission.INTERNET
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:18:5-67
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:18:22-64
uses-permission#android.permission.CHANGE_WIFI_MULTICAST_STATE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:19:5-86
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:19:22-83
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:20:5-79
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:20:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:21:5-23:40
	tools:ignore
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:23:9-37
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:22:9-65
uses-permission#android.permission.VIBRATE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:24:5-66
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:24:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:25:5-77
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:25:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:26:5-68
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:26:22-65
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:27:5-76
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:27:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:28:5-80
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:28:22-77
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:29:5-77
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:29:22-74
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:30:5-32:40
	tools:ignore
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:32:9-37
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:31:9-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:33:5-78
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:33:22-75
uses-permission#android.permission.OVERLAY_WINDOW
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:34:5-73
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:34:22-70
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:35:5-83
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:35:22-80
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:36:5-38:53
	tools:ignore
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:38:9-50
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:37:9-61
queries
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:40:5-42:15
package#com.mojang.minecraftpe
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:41:9-58
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:41:18-55
application
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:44:5-179:19
INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:44:5-179:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b923a94a0a3fff51545ad4d215c7ae9a\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b923a94a0a3fff51545ad4d215c7ae9a\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0dd3509e331ffda00d074076d997d500\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0dd3509e331ffda00d074076d997d500\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67cce916a62510377111125940e07555\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67cce916a62510377111125940e07555\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\062f2eb6737fdc6cdf631c7d033f457d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\062f2eb6737fdc6cdf631c7d033f457d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:47:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:50:9-35
	android:label
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:46:9-41
	android:icon
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:45:9-43
	android:theme
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:48:9-53
	android:networkSecurityConfig
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:49:9-69
activity#org.levimc.launcher.ui.activities.SplashActivity
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:52:9-61:20
	android:screenOrientation
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:55:13-56
	android:exported
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:54:13-36
	tools:ignore
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:56:13-42
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:53:13-76
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:57:13-60:29
action#android.intent.action.MAIN
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:58:17-69
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:58:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:59:17-77
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:59:27-74
activity#org.levimc.launcher.ui.activities.MainActivity
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:63:9-67:45
	android:screenOrientation
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:66:13-56
	android:exported
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:65:13-36
	tools:ignore
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:67:13-42
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:64:13-74
activity#org.levimc.launcher.ui.activities.IntentHandler
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:69:9-126:20
	android:screenOrientation
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:76:13-56
	android:excludeFromRecents
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:73:13-47
	android:launchMode
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:75:13-44
	android:exported
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:74:13-36
	tools:ignore
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:77:13-42
	android:configChanges
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:72:13-178
	android:alwaysRetainTaskState
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:71:13-49
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:70:13-56
meta-data#android.app.lib_name
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:78:13-80:45
	android:value
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:80:17-42
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:79:17-52
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.DEFAULT
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:82:13-85:29
action#android.intent.action.VIEW
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:83:17-69
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:83:25-66
category#android.intent.category.DEFAULT
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:17-76
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:84:27-73
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:*/*+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.mcaddon+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.mcpack+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.mctemplate+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.mcworld+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.so+data:pathPattern:.*\\..*\\..*\\..*\\.mcaddon+data:pathPattern:.*\\..*\\..*\\..*\\.mcpack+data:pathPattern:.*\\..*\\..*\\..*\\.mctemplate+data:pathPattern:.*\\..*\\..*\\..*\\.mcworld+data:pathPattern:.*\\..*\\..*\\..*\\.so+data:pathPattern:.*\\..*\\..*\\.mcaddon+data:pathPattern:.*\\..*\\..*\\.mcpack+data:pathPattern:.*\\..*\\..*\\.mctemplate+data:pathPattern:.*\\..*\\..*\\.mcworld+data:pathPattern:.*\\..*\\..*\\.so+data:pathPattern:.*\\..*\\.mcaddon+data:pathPattern:.*\\..*\\.mcpack+data:pathPattern:.*\\..*\\.mctemplate+data:pathPattern:.*\\..*\\.mcworld+data:pathPattern:.*\\..*\\.so+data:pathPattern:.*\\.mcaddon+data:pathPattern:.*\\.mcpack+data:pathPattern:.*\\.mctemplate+data:pathPattern:.*\\.mcworld+data:pathPattern:.*\\.so+data:scheme:content+data:scheme:file
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:86:13-119:29
data
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:17-48
	android:host
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:91:23-39
	android:scheme
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:92:23-44
	android:pathPattern
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:94:23-52
	android:mimeType
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:90:23-45
intent-filter#action:name:xbox_live_achievement_unlock+action:name:xbox_live_game_invite+category:name:android.intent.category.DEFAULT
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:120:13-125:29
action#xbox_live_game_invite
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:121:17-64
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:121:25-61
action#xbox_live_achievement_unlock
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:122:17-71
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:122:25-68
activity#org.levimc.launcher.core.minecraft.NativeMinecraftLauncher
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:127:9-140:20
	android:screenOrientation
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:134:13-56
	android:excludeFromRecents
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:131:13-47
	android:launchMode
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:133:13-44
	android:exported
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:132:13-37
	tools:ignore
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:136:13-42
	android:configChanges
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:130:13-178
	android:alwaysRetainTaskState
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:129:13-49
	android:theme
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:135:13-54
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:128:13-86
activity#com.microsoft.xal.browser.IntentHandler
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:141:9-156:20
	android:launchMode
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:144:13-44
	android:exported
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:143:13-36
	android:configChanges
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:145:13-81
	android:alwaysRetainTaskState
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:146:13-49
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:142:13-67
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth+data:scheme:ms-xal-0000000048183522
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:147:13-155:29
category#android.intent.category.BROWSABLE
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:154:17-78
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:154:27-75
activity#com.microsoft.xal.browser.BrowserLaunchActivity
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:157:9-162:52
	android:launchMode
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:160:13-44
	android:exported
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:159:13-37
	android:configChanges
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:161:13-81
	android:alwaysRetainTaskState
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:162:13-49
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:158:13-75
activity#com.microsoft.xal.browser.WebKitWebViewController
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:163:9-168:52
	android:launchMode
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:166:13-44
	android:exported
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:165:13-37
	android:configChanges
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:167:13-81
	android:alwaysRetainTaskState
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:168:13-49
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:164:13-77
provider#androidx.core.content.FileProvider
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:170:9-178:20
	android:grantUriPermissions
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:174:13-47
	android:authorities
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:173:13-67
	android:exported
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:172:13-37
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:171:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:175:13-177:63
	android:resource
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:177:17-60
	android:name
		ADDED from D:\Levi\LeviLaunchroid\app\src\main\AndroidManifest.xml:176:17-67
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\062f2eb6737fdc6cdf631c7d033f457d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\062f2eb6737fdc6cdf631c7d033f457d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c90155e27f306154971b3f4c70b0eb5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a9031c7b104008593533639d26a276\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#org.levimc.launcher.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#org.levimc.launcher.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e65fc3b7d0a9477c2db825c7f4882f80\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\394f840bcabb1d4882ac8fa36ac058b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92

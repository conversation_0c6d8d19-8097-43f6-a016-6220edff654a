<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.activities.SplashActivity">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5"/>

    <ImageView
        android:id="@+id/imgLeaf"
        android:layout_width="245dp"
        android:layout_height="245dp"
        app:layout_constraintBottom_toTopOf="@+id/tvAppName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.45"
        android:src="@drawable/ic_leaf_logo"
        android:contentDescription="Leaf Logo"
        android:layout_marginBottom="2dp"/>

    <TextView
        android:id="@+id/tvAppName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="LeviLauncher"
        android:textSize="64sp"
        android:textStyle="bold"
        android:textColor="#FF000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical"
        app:layout_constraintTop_toBottomOf="@id/imgLeaf"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="80dp" 
        android:includeFontPadding="false"
        android:letterSpacing="0.025"/>

</androidx.constraintlayout.widget.ConstraintLayout>
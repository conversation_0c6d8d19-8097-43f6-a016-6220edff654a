// Generated by view binder compiler. Do not edit!
package org.levimc.launcher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.levimc.launcher.R;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Guideline guidelineVertical;

  @NonNull
  public final ImageView imgLeaf;

  @NonNull
  public final TextView tvAppName;

  private ActivitySplashBinding(@NonNull ConstraintLayout rootView,
      @NonNull Guideline guidelineVertical, @NonNull ImageView imgLeaf,
      @NonNull TextView tvAppName) {
    this.rootView = rootView;
    this.guidelineVertical = guidelineVertical;
    this.imgLeaf = imgLeaf;
    this.tvAppName = tvAppName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.guideline_vertical;
      Guideline guidelineVertical = ViewBindings.findChildViewById(rootView, id);
      if (guidelineVertical == null) {
        break missingId;
      }

      id = R.id.imgLeaf;
      ImageView imgLeaf = ViewBindings.findChildViewById(rootView, id);
      if (imgLeaf == null) {
        break missingId;
      }

      id = R.id.tvAppName;
      TextView tvAppName = ViewBindings.findChildViewById(rootView, id);
      if (tvAppName == null) {
        break missingId;
      }

      return new ActivitySplashBinding((ConstraintLayout) rootView, guidelineVertical, imgLeaf,
          tvAppName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
